# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#
# List of comma-separated packages that start with or equal this string
# will cause a security exception to be thrown when
# passed to checkPackageAccess unless the
# corresponding RuntimePermission ("accessClassInPackage."+package) has
# been granted.
package.access=sun.,org.apache.catalina.,org.apache.coyote.,org.apache.jasper.,org.apache.tomcat.
#
# List of comma-separated packages that start with or equal this string
# will cause a security exception to be thrown when
# passed to checkPackageDefinition unless the
# corresponding RuntimePermission ("defineClassInPackage."+package) has
# been granted.
#
# by default, no packages are restricted for definition, and none of
# the class loaders supplied with the JDK call checkPackageDefinition.
#
package.definition=sun.,java.,org.apache.catalina.,org.apache.coyote.,\
org.apache.jasper.,org.apache.naming.,org.apache.tomcat.

#
#
# List of comma-separated paths defining the contents of the "common"
# classloader. Prefixes should be used to define what is the repository type.
# Path may be relative to the CATALINA_HOME or CATALINA_BASE path or absolute.
# If left as blank,the JVM system loader will be used as Catalina's "common"
# loader.
# Examples:
#     "foo": Add this folder as a class repository
#     "foo/*.jar": Add all the JARs of the specified folder as class
#                  repositories
#     "foo/bar.jar": Add bar.jar as a class repository
#
# Note: Values are enclosed in double quotes ("...") in case either the
#       ${catalina.base} path or the ${catalina.home} path contains a comma.
#       Because double quotes are used for quoting, the double quote character
#       may not appear in a path.
common.loader="${catalina.base}/lib","${catalina.base}/lib/*.jar","${catalina.home}/lib","${catalina.home}/lib/*.jar"

#
# List of comma-separated paths defining the contents of the "server"
# classloader. Prefixes should be used to define what is the repository type.
# Path may be relative to the CATALINA_HOME or CATALINA_BASE path or absolute.
# If left as blank, the "common" loader will be used as Catalina's "server"
# loader.
# Examples:
#     "foo": Add this folder as a class repository
#     "foo/*.jar": Add all the JARs of the specified folder as class
#                  repositories
#     "foo/bar.jar": Add bar.jar as a class repository
#
# Note: Values may be enclosed in double quotes ("...") in case either the
#       ${catalina.base} path or the ${catalina.home} path contains a comma.
#       Because double quotes are used for quoting, the double quote character
#       may not appear in a path.
server.loader=

#
# List of comma-separated paths defining the contents of the "shared"
# classloader. Prefixes should be used to define what is the repository type.
# Path may be relative to the CATALINA_BASE path or absolute. If left as blank,
# the "common" loader will be used as Catalina's "shared" loader.
# Examples:
#     "foo": Add this folder as a class repository
#     "foo/*.jar": Add all the JARs of the specified folder as class
#                  repositories
#     "foo/bar.jar": Add bar.jar as a class repository
# Please note that for single jars, e.g. bar.jar, you need the URL form
# starting with file:.
#
# Note: Values may be enclosed in double quotes ("...") in case either the
#       ${catalina.base} path or the ${catalina.home} path contains a comma.
#       Because double quotes are used for quoting, the double quote character
#       may not appear in a path.
shared.loader=

# List of JAR files that should not be scanned using the JarScanner
# functionality. This is typically used to scan JARs for configuration
# information. JARs that do not contain such information may be excluded from
# the scan to speed up the scanning process. This is the default list. JARs on
# this list are excluded from all scans. Scan specific lists (to exclude JARs
# from individual scans) follow this. The list must be a comma separated list of
# JAR file names.
# The JARs listed below include:
# - Tomcat Bootstrap JARs
# - Tomcat API JARs
# - Catalina JARs
# - Jasper JARs
# - Tomcat JARs
# - Common non-Tomcat JARs
# - Test JARs (JUnit, Cobertura and dependencies)
tomcat.util.scan.DefaultJarScanner.jarsToSkip=\
bootstrap.jar,commons-daemon.jar,tomcat-juli.jar,\
annotations-api.jar,el-api.jar,jsp-api.jar,servlet-api.jar,websocket-api.jar,\
jaspic-api.jar,\
catalina.jar,catalina-ant.jar,catalina-ha.jar,catalina-storeconfig.jar,\
catalina-tribes.jar,\
jasper.jar,jasper-el.jar,ecj-*.jar,\
tomcat-api.jar,tomcat-util.jar,tomcat-util-scan.jar,tomcat-coyote.jar,\
tomcat-dbcp.jar,tomcat-jni.jar,tomcat-websocket.jar,\
tomcat-i18n-en.jar,tomcat-i18n-es.jar,tomcat-i18n-fr.jar,tomcat-i18n-ja.jar,\
tomcat-juli-adapters.jar,catalina-jmx-remote.jar,catalina-ws.jar,\
tomcat-jdbc.jar,\
tools.jar,\
commons-beanutils*.jar,commons-codec*.jar,commons-collections*.jar,\
commons-dbcp*.jar,commons-digester*.jar,commons-fileupload*.jar,\
httpcore-*.jar,\
commons-httpclient*.jar,commons-io*.jar,commons-lang*.jar,commons-logging*.jar,\
commons-math*.jar,commons-pool*.jar,\
jstl.jar,taglibs-standard-spec-*.jar,\
geronimo-spec-jaxrpc*.jar,wsdl4j*.jar,\
ant.jar,ant-junit*.jar,aspectj*.jar,jmx.jar,h2*.jar,hibernate*.jar,httpclient*.jar,\
jmx-tools.jar,jta*.jar,log4j*.jar,mail*.jar,slf4j*.jar,\
xercesImpl.jar,xmlParserAPIs.jar,xml-apis.jar,\
junit.jar,junit-*.jar,hamcrest-*.jar,easymock-*.jar,cglib-*.jar,\
objenesis-*.jar,ant-launcher.jar,\
cobertura-*.jar,asm-*.jar,dom4j-*.jar,icu4j-*.jar,jaxen-*.jar,jdom-*.jar,\
assertj-core-*.jar,mockito-core-*.jar,localstack-utils-*.jar,powermock-*.jar,\
jetty-*.jar,oro-*.jar,servlet-api-*.jar,tagsoup-*.jar,xmlParserAPIs-*.jar,\
xom-*.jar

# Additional JARs (over and above the default JARs listed above) to skip when
# scanning for Servlet 3.0 pluggability features. These features include web
# fragments, annotations, SCIs and classes that match @HandlesTypes. The list
# must be a comma separated list of JAR file names.
org.apache.catalina.startup.ContextConfig.jarsToSkip=

# Additional JARs (over and above the default JARs listed above) to skip when
# scanning for TLDs. The list must be a comma separated list of JAR file names.
org.apache.catalina.startup.TldConfig.jarsToSkip=accessors-smart-2.5.2.jar,\
activation-1.1.1.jar,\
advancedsavedqueryserver.jar,\
amazoncloudserver.jar,\
annotations-2.0.0.jar,\
annotations-api.jar,\
ant-1.10.8.jar,\
ant-1.10.10.jar,\
ant-commons-net-1.10.11.jar,\
antisamy-1.5.8.jar,\
antlr4-runtime-4.7.1.jar,\
antlr-runtime-3.5.3.jar,\
aopalliance-1.0.jar,\
apk-parser-2.6.14.jar,\
apksig-3.5.2.jar,\
asm-3.3.1.jar,\
aspectjweaver-1.9.7.jar,\
assertj-core-3.27.3.jar,\
atlantic-8.6.3.jar,\
auditreportservicesserver.jar,\
avalon-framework-4.2.0.jar,\
awaitility-4.1.0.jar,\
aws-java-sdk-core-1.12.780.jar,\
aws-java-sdk-kms-1.12.780.jar,\
aws-java-sdk-s3-1.12.780.jar,\
aws-java-sdk-sqs-1.12.780.jar,\
aws-java-sdk-sts-1.12.780.jar,\
backofficeserver.jar,\
backofficesolrsearchserver.jar,\
backoffice-core-22.05.32-RC1.jar,\
backoffice-widgets-22.05.32-RC1.jar,\
basecommercebackofficeserver.jar,\
basecommerceserver.jar,\
batik-all-1.16.jar,\
bcprov-jdk18on-1.80.jar,\
bootstrap.jar,\
bsh-2.0b6.jar,\
btc-ascii-table-1.0.jar,\
catalina-ant.jar,\
catalina-ha.jar,\
catalina.jar,\
catalina-jmx-remote.jar,\
catalina-storeconfig.jar,\
catalina-tribes.jar,\
catalogserver.jar,\
cglib-nodep-3.3.0.jar,\
charon-1.4.1.jar,\
ckez-4.18.0.0.jar,\
classmate-1.3.4.jar,\
cloning-1.10.3.jar,\
cms2libserver.jar,\
cms2server.jar,\
cmsbackofficeserver.jar,\
cockpitadmin-22.05.32-RC1.jar,\
cockpitcore-22.05.32-RC1.jar,\
cockpit-data-integration-22.05.32-RC1.jar,\
cockpit-demo-widgets-22.05.32-RC1.jar,\
cockpitframework-22.05.32-RC1.jar,\
cockpit-module-aggregator-22.05.32-RC1.jar,\
cockpit-standard-editors-22.05.32-RC1.jar,\
cockpittesting-22.05.32-RC1.jar,\
cockpitwidgets-22.05.32-RC1.jar,\
commentsserver.jar,\
commerceservicesbackofficeserver.jar,\
commons-beanutils-1.9.4.jar,\
commons-codec-1.16.jar,\
commons-collections-3.2.2.jar,\
commons-collections4-4.4.jar,\
commons-configuration-1.10.jar,\
commons-daemon.jar,\
commons-dbcp-1.4.jar,\
commons-digester-2.1.jar,\
commons-discovery-0.5.jar,\
commons-el-1.0.jar,\
commons-email-1.5.jar,\
commons-fileupload-1.5.jar,\
commons-io-2.15.1.jar,\
commons-jexl-1.1.jar,\
commons-lang-2.6.jar,\
commons-lang3-3.12.0.jar,\
commons-logging-1.2.jar,\
commons-net-3.0.1.jar,\
commons-pool-1.6.jar,\
commons-pool2-2.4.2.jar,\
commonsserver.jar,\
commons-text-1.10.0.jar,\
commons-validator-1.7.jar,\
com.sap.security.core.server.csi-1.00.8.jar,\
concurrentlinkedhashmap-lru-1.4.jar,\
coreserver.jar,\
customerreviewserver.jar,\
customersupportbackofficeserver.jar,\
ddlutils-1.0.jar,\
deliveryzoneserver.jar,\
dom4j-1.6.1.jar,\
dom4j-2.1.1.jar,\
drools-compiler-8.44.0-SAP-PATCH-1.jar,\
drools-core-8.44.0-SAP-PATCH-1.jar,\
drools-core-reflective-7.73.2.SAP-PATCH.jar,\
droolsruleengineservicesserver.jar,\
drools-templates-7.73.0.Final.jar,\
dumbster-1.6.jar,\
easymock-4.3.jar,\
easymockclassextension-3.1.jar,\
ecj-3.15.1.jar,\
ehcache-2.10.9.2.jar,\
el-api.jar,\
embeddedserverserver.jar,\
europe1server.jar,\
ezmorph-1.0.6.jar,\
failureaccess-1.0.1.jar,\
fastutil-6.5.6.jar,\
fest-assert-1.4.jar,\
fest-util-1.1.6.jar,\
fop-2.6.jar,\
geodesy-1.1.3.jar,\
gmapsz-3.0.5.jar,\
groovy-3.0.9.jar,\
groovy-ant-3.0.9.jar,\
groovy-json-3.0.9.jar,\
groovy-jsr223-3.0.9.jar,\
groovy-servlet-3.0.9.jar,\
groovy-templates-3.0.9.jar,\
groovy-test-3.0.9.jar,\
groovy-xml-3.0.9.jar,\
gson-2.8.8.jar,\
gson-2.9.0.jar,\
guava-32.0.1-jre.jar,\
hacserver.jar,\
hamcrest-all-1.3.jar,\
hibernate-validator-6.1.5.Final.jar,\
hk2-2.6.1.jar,\
hk2-api-2.6.1.jar,\
hk2-locator-2.6.1.jar,\
hk2-utils-2.6.1.jar,\
hsqldb-2.4.1.jar,\
http-builder-0.7.1.jar,\
httpclient-4.5.13.jar,\
httpcore-4.4.13.jar,\
httpmime-4.5.13.jar,\
httpunit-1.7.jar,\
hybris-encryption-1.1.jar,\
hybriskey-1.0.jar,\
hybrislicence.jar,\
hybris-simple-statistics-1.0.1.jar,\
impexserver.jar,\
jackson-annotations-2.18.3.jar,\
jackson-core-2.18.3.jar,\
jackson-databind-2.18.3.jar,\
jackson-datatype-jdk8-2.9.10.jar,\
jackson-datatype-jsr310-2.9.10.jar,\
jackson-jaxrs-base-2.12.3.jar,\
jackson-jaxrs-json-provider-2.12.3.jar,\
jackson-module-jaxb-annotations-2.12.3.jar,\
jai-codec-1.1.3.jar,\
jai_core-1.1.3.jar,\
jasper-el.jar,\
jasper.jar,\
jaspic-api.jar,\
java-sizeof-0.0.4.jar,\
javassist-3.21.0-GA.jar,\
javax.annotation-api-1.3.2.jar,\
javax.inject-2.5.0-b42.jar,\
javax.json-1.1.4.jar,\
javax.json-api-1.1.4.jar,\
javax.ws.rs-api-2.0.1.jar,\
javolution-5.5.1.jar,\
jaxb-api-2.3.3.jar,\
jaxb-core-*******.jar,\
jaxb-impl-2.3.4.jar,\
jaxb-xjc-2.2.11.jar,\
jaxen-1.2.0.jar,\
jboss-logging-3.3.2.Final.jar,\
jcip-annotations-1.0.jar,\
jcl-over-slf4j-1.7.30.jar,\
jersey-apache-connector-2.25.1.jar,\
jersey-client-1.13.jar,\
jersey-client-2.25.1.jar,\
jersey-common-2.25.1.jar,\
jersey-core-1.13.jar,\
jersey-entity-filtering-2.34.jar,\
jersey-guava-2.25.1.jar,\
jersey-json-1.13.jar,\
jersey-media-json-jackson-2.34.jar,\
jersey-server-2.34.jar,\
jersey-servlet-1.13.jar,\
jersey-spring-1.13.jar,\
jersey-spring5-2.34.jar,\
jersey-test-framework-core-1.34.jar,\
jfreechart-1.0.14.jar,\
jgroups-4.0.24.Final.jar,\
jgroups-kubernetes-1.0.9.Final.jar,\
jmx_prometheus_javaagent-0.18.0.jar,\
joda-time-2.9.9.jar,\
json-lib-2.4-jdk13.jar,\
json-path-2.9.0.jar,\
json-smart-2.5.2.jar,\
json-unit-assertj-2.8.0.jar,\
json-unit-core-2.8.0.jar,\
json-unit-json-path-2.8.0.jar,\
jsoup-1.14.2.jar,\
jsp-api.jar,\
jsr305-2.0.0.jar,\
jsr311-api-1.1.1.jar,\
jstl-api-1.2.jar,\
junit-4.13.2.jar,\
junit4-dataprovider-2.6.jar,\
junit-dataprovider-core-2.6.jar,\
keycloak-adapter-core-24.0.5.jar,\
keycloak-adapter-spi-24.0.5.jar,\
keycloak-common-24.0.5.jar,\
keycloak-core-24.0.5.jar,\
keycloak-spring-security-adapter-24.0.5.jar,\
kie-api-7.73.0.Final.jar,\
kie-internal-7.73.0.Final.jar,\
kie-soup-commons-7.73.0.Final.jar,\
kie-soup-maven-support-7.73.0.Final.jar,\
kie-soup-project-datamodel-commons-7.73.0.Final.jar,\
kxml2-2.3.0.jar,\
likey-no-iaik-jdk11-1.0.9.jar,\
localstack-utils-0.2.1.jar,\
log4j-1.2.17.jar,\
log4j-api-2.17.2.jar,\
log4j-core-2.17.2.jar,\
log4j-slf4j-impl-2.17.2.jar,\
jakarta.mail-1.6.5.jar,\
mediaconversionbackofficeserver.jar,\
mediaconversionserver.jar,\
mediawebserver.jar,\
metrics-core-4.1.18.jar,\
metrics-jmx-4.1.18.jar,\
metrics-healthchecks-4.1.18.jar,\
mime-util-2.1.3.jar,\
mockito-core-3.12.4.jar,\
models.jar,\
mvel2-2.4.4.Final.jar,\
mysql-connector-j-8.2.0.jar,\
nekohtml-1.9.22.jar,\
netty-buffer-4.1.118.Final.jar,\
netty-codec-4.1.118.Final.jar,\
netty-codec-http-4.1.118.Final.jar,\
netty-codec-socks-4.1.118.Final.jar,\
netty-common-4.1.118.Final.jar,\
netty-handler-4.1.118.Final.jar,\
netty-handler-proxy-4.1.118.Final.jar,\
netty-resolver-4.1.118.Final.jar,\
netty-transport-4.1.118.Final.jar,\
noggit-0.8.jar,\
oauth2server.jar,\
objenesis-3.2.jar,\
opentest4j-1.2.0.jar,\
ordercalculationserver.jar,\
org.eclipse.persistence.antlr-2.7.6.jar,\
org.eclipse.persistence.asm-2.7.6.jar,\
org.eclipse.persistence.core-2.7.6.jar,\
org.eclipse.persistence.moxy-2.7.6.jar,\
orika-core-1.5.4.jar,\
oro-2.0.8.jar,\
owasp-java-html-sanitizer-20211018.2.jar,\
paranamer-2.8.jar,\
paymentserver.jar,\
paymentstandardserver.jar,\
personalizationservicesserver.jar,\
platformbackofficeserver.jar,\
poi-5.2.2.jar,\
poi-ooxml-5.2.2.jar,\
poi-ooxml-schemas-5.2.2.jar,\
processingserver.jar,\
promotionengineservicesserver.jar,\
promotionsbackofficeserver.jar,\
promotionsserver.jar,\
protobuf-java-3.21.7.jar,\
quartz-2.3.2.jar,\
reactive-streams-1.0.3.jar,\
reactor-core-3.4.41.RELEASE.jar,\
reflections-0.9.11.jar,\
rhino-1.7.7.jar,\
ruledefinitionsserver.jar,\
ruleenginebackofficeserver.jar,\
ruleengineserver.jar,\
ruleengineservicesserver.jar,\
rxjava-1.3.8.jar,\
sac-1.3.jar,\
scimark-2.0.jar,\
scriptingserver.jar,\
serializer-2.7.2.jar,\
servlet-api.jar,\
sitemesh-3.0-alpha-2.jar,\
slf4j-api-1.7.30.jar,\
solrfacetsearchbackofficeserver.jar,\
solrfacetsearchserver.jar,\
solrserverserver.jar,\
solr-solrj-8.11.2.jar,\
spock-core-1.3-groovy-2.5.jar,\
spock-spring-1.3-groovy-2.5.jar,\
spotbugs-annotations-3.1.12.jar,\
spring-aop-5.3.42.jar,\
spring-aspects-5.3.42.jar,\
spring-beans-5.3.42.jar,\
spring-context-5.3.42.jar,\
spring-context-support-5.3.42.jar,\
spring-core-5.3.42.jar,\
spring-expression-5.3.42.jar,\
springfox-core-2.9.2.jar,\
springfox-schema-2.9.2.jar,\
springfox-spi-2.9.2.jar,\
springfox-spring-web-2.9.2.jar,\
springfox-swagger2-2.9.2.jar,\
springfox-swagger-common-2.9.2.jar,\
springfox-swagger-ui-2.9.2.jar,\
spring-aop-5.3.42.jar,\
spring-aspects-5.3.42.jar,\
spring-beans-5.3.42.jar,\
spring-context-5.3.42.jar,\
spring-context-support-5.3.42.jar,\
spring-core-5.3.42.jar,\
spring-expression-5.3.42.jar,\
spring-hateoas-1.2.2.jar,\
spring-instrument-5.3.42.jar,\
spring-integration-core-5.5.7.jar,\
spring-integration-event-5.5.7.jar,\
spring-integration-feed-5.5.7.jar,\
spring-integration-file-5.5.7.jar,\
spring-integration-ftp-5.5.7.jar,\
spring-integration-groovy-5.5.7.jar,\
spring-integration-http-5.5.7.jar,\
spring-integration-ip-5.5.7.jar,\
spring-integration-jdbc-5.5.7.jar,\
spring-integration-jms-5.5.7.jar,\
spring-integration-jmx-5.5.7.jar,\
spring-integration-mail-5.5.7.jar,\
spring-integration-rmi-5.5.7.jar,\
spring-integration-scripting-5.5.7.jar,\
spring-integration-security-5.5.7.jar,\
spring-integration-sftp-5.5.7.jar,\
spring-integration-stream-5.5.7.jar,\
spring-integration-test-5.5.7.jar,\
spring-integration-ws-5.5.7.jar,\
spring-integration-xml-5.5.7.jar,\
spring-integration-xmpp-5.5.7.jar,\
spring-integration-zip-2.0.0.RELEASE.jar,\
spring-jdbc-5.3.42.jar,\
spring-jms-5.3.42.jar,\
spring-messaging-5.3.42.jar,\
spring-mobile-device-1.1.5.RELEASE.jar,\
spring-orm-5.3.42.jar,\
spring-oxm-5.3.42.jar,\
spring-plugin-core-1.2.0.RELEASE.jar,\
spring-plugin-metadata-1.2.0.RELEASE.jar,\
spring-retry-1.3.1.jar,\
spring-security-config-5.8.16.jar,\
spring-security-core-5.8.16.jar,\
spring-security-jwt-1.0.10.RELEASE.jar,\
spring-security-oauth2-2.5.2.RELEASE.jar,\
spring-security-web-5.8.16.jar,\
spring-session-1.3.5.RELEASE.jar,\
spring-test-5.3.42.jar,\
spring-tx-5.3.42.jar,\
spring-web-5.3.42.jar,\
spring-ws-core-3.1.1.jar,\
spring-xml-3.0.10.RELEASE.jar,\
stax2-api-3.1.4.jar,\
stax-utils-20070216.jar,\
stripe-java-18.4.0.jar,\
swagger-annotations-1.5.22.jar,\
swagger-models-1.5.22.jar,\
ticketsystembackofficeserver.jar,\
ticketsystemserver.jar,\
tomcat-api.jar,\
tomcat-coyote.jar,\
tomcat-dbcp.jar,\
tomcatembeddedserverserver.jar,\
tomcat-i18n-es.jar,\
tomcat-i18n-fr.jar,\
tomcat-i18n-ja.jar,\
tomcat-i18n-ru.jar,\
tomcat-jdbc.jar,\
tomcat-jni.jar,\
tomcat-juli-adapters.jar,\
tomcat-juli.jar,\
tomcat-util.jar,\
tomcat-util-scan.jar,\
tomcat-websocket.jar,\
validation-api-2.0.1.Final.jar,\
validationserver.jar,\
velocity-engine-core-2.3.jar,\
velocity-engine-patch-2.3-1.jar,\
velocity-tools-generic-3.1.jar,\
voucherbackofficeserver.jar,\
voucherserver.jar,\
webfragmentExt_backofficesolrsearch.jar,\
webservicescommonsserver.jar,\
websocket-api.jar,\
wiremock-jre8-standalone-2.35.0.jar,\
woodstox-core-asl-4.4.1.jar,\
workflowserver.jar,\
wrapper.jar,\
wro4j-core-1.8.0.jar,\
wro4j-core-1.7.9.jar,\
wss4j-1.6.19.jar,\
xalan-2.7.2.jar,\
xercesImpl-2.12.0.jar,\
xml-apis-1.4.01.jar,\
xml-apis-ext-1.3.04.jar,\
xmlbeans-5.0.3.jar,\
xml-combiner-2.2.jar,\
xmlenc-0.52.jar,\
xmlgraphics-commons-2.7.jar,\
xml-resolver-1.2.jar,\
xmlsec-2.2.6.jar,\
xstream-1.4.21.jar,\
ybootstrap.jar,\
yimpexgenerator-*******.jar,\
ytomcat.jar,\
zcommon-8.6.3.jar,\
zel-8.6.3.jar,\
zhtml-8.6.3.jar,\
zk-8.6.3.jar,\
zkbind-8.6.3.jar,\
zkcharts-3.0.2.jar,\
zkex-8.6.3.jar,\
zkmax-8.6.3.jar,\
zkplus-8.6.3.jar,\
zkspring-core-3.2.0.jar,\
zookeeper-3.8.0.jar,\
zul-8.6.3.jar,\
zuti-8.6.3.jar,\
zweb-8.6.3.jar

# Default list of JAR files that should not be scanned using the JarScanner
# functionality. This is typically used to scan JARs for configuration
# information. JARs that do not contain such information may be excluded from
# the scan to speed up the scanning process. This is the default list. JARs on
# this list are excluded from all scans. The list must be a comma separated list
# of JAR file names.
# The list of JARs to skip may be over-ridden at a Context level for individual
# scan types by configuring a JarScanner with a nested JarScanFilter.
# The JARs listed below include:
# - Tomcat Bootstrap JARs
# - Tomcat API JARs
# - Catalina JARs
# - Jasper JARs
# - Tomcat JARs
# - Common non-Tomcat JARs
# - Test JARs (JUnit, Cobertura and dependencies)
tomcat.util.scan.StandardJarScanFilter.jarsToSkip=\
bootstrap.jar,commons-daemon.jar,tomcat-juli.jar,\
annotations-api.jar,el-api.jar,jsp-api.jar,servlet-api.jar,websocket-api.jar,\
jaspic-api.jar,\
catalina.jar,catalina-ant.jar,catalina-ha.jar,catalina-storeconfig.jar,\
catalina-tribes.jar,\
jasper.jar,jasper-el.jar,ecj-*.jar,\
tomcat-api.jar,tomcat-util.jar,tomcat-util-scan.jar,tomcat-coyote.jar,\
tomcat-dbcp.jar,tomcat-jni.jar,tomcat-websocket.jar,\
tomcat-i18n-en.jar,tomcat-i18n-es.jar,tomcat-i18n-fr.jar,tomcat-i18n-ja.jar,\
tomcat-juli-adapters.jar,catalina-jmx-remote.jar,catalina-ws.jar,\
tomcat-jdbc.jar,\
tools.jar,\
commons-beanutils*.jar,commons-codec*.jar,commons-collections*.jar,\
commons-dbcp*.jar,commons-digester*.jar,commons-fileupload*.jar,\
commons-httpclient*.jar,commons-io*.jar,commons-lang*.jar,commons-logging*.jar,\
commons-math*.jar,commons-pool*.jar,\
jstl.jar,taglibs-standard-spec-*.jar,\
geronimo-spec-jaxrpc*.jar,wsdl4j*.jar,\
ant.jar,ant-junit*.jar,aspectj*.jar,jmx.jar,h2*.jar,hibernate*.jar,httpclient*.jar,\
jmx-tools.jar,jta*.jar,log4j*.jar,mail*.jar,slf4j*.jar,\
xercesImpl.jar,xmlParserAPIs.jar,xml-apis.jar,\
junit.jar,junit-*.jar,hamcrest-*.jar,easymock-*.jar,cglib-*.jar,\
objenesis-*.jar,ant-launcher.jar,\
cobertura-*.jar,asm-*.jar,dom4j-*.jar,icu4j-*.jar,jaxen-*.jar,jdom-*.jar,\
jetty-*.jar,oro-*.jar,servlet-api-*.jar,tagsoup-*.jar,xmlParserAPIs-*.jar,\
xom-*.jar\
accessors-smart-2.5.2.jar,\
activation-1.1.1.jar,\
advancedsavedqueryserver.jar,\
amazoncloudserver.jar,\
annotations-2.0.0.jar,\
annotations-api.jar,\
ant-1.10.8.jar,\
ant-1.10.10.jar,\
ant-commons-net-1.10.11.jar,\
antisamy-1.5.8.jar,\
antlr4-runtime-4.7.1.jar,\
antlr-runtime-3.5.3.jar,\
aopalliance-1.0.jar,\
apk-parser-2.6.14.jar,\
apksig-3.5.2.jar,\
asm-3.3.1.jar,\
aspectjweaver-1.9.7.jar,\
assertj-core-3.27.3.jar,\
atlantic-8.6.3.jar,\
auditreportservicesserver.jar,\
avalon-framework-4.2.0.jar,\
awaitility-4.1.0.jar,\
aws-java-sdk-core-1.12.292.jar,\
aws-java-sdk-kms-1.12.292.jar,\
aws-java-sdk-s3-1.12.292.jar,\
aws-java-sdk-sqs-1.12.292.jar,\
aws-java-sdk-sts-1.12.292.jar,\
backofficeserver.jar,\
backofficesolrsearchserver.jar,\
backoffice-core-22.05.7-RC8.jar,\
backoffice-widgets-22.05.7-RC8.jar,\
basecommercebackofficeserver.jar,\
basecommerceserver.jar,\
batik-all-1.16.jar,\
bcprov-jdk15on-1.69.jar,\
bootstrap.jar,\
bsh-2.0b6.jar,\
btc-ascii-table-1.0.jar,\
catalina-ant.jar,\
catalina-ha.jar,\
catalina.jar,\
catalina-jmx-remote.jar,\
catalina-storeconfig.jar,\
catalina-tribes.jar,\
catalogserver.jar,\
cglib-nodep-3.3.0.jar,\
charon-1.2.17.jar,\
ckez-4.18.0.0.jar,\
classmate-1.3.4.jar,\
cloning-1.10.3.jar,\
cms2libserver.jar,\
cms2server.jar,\
cmsbackofficeserver.jar,\
cockpitadmin-22.05.7-RC8.jar,\
cockpitcore-22.05.7-RC8.jar,\
cockpit-data-integration-22.05.7-RC8.jar,\
cockpit-demo-widgets-22.05.7-RC8.jar,\
cockpitframework-22.05.7-RC8.jar,\
cockpit-module-aggregator-22.05.7-RC8.jar,\
cockpit-standard-editors-22.05.7-RC8.jar,\
cockpittesting-22.05.7-RC8.jar,\
cockpitwidgets-22.05.7-RC8.jar,\
commentsserver.jar,\
commerceservicesbackofficeserver.jar,\
commons-beanutils-1.9.4.jar,\
commons-codec-1.15.jar,\
commons-collections-3.2.2.jar,\
commons-collections4-4.4.jar,\
commons-configuration-1.10.jar,\
commons-daemon.jar,\
commons-dbcp-1.4.jar,\
commons-digester-2.1.jar,\
commons-discovery-0.5.jar,\
commons-el-1.0.jar,\
commons-email-1.5.jar,\
commons-fileupload-1.4.jar,\
commons-io-2.11.0.jar,\
commons-jexl-1.1.jar,\
commons-lang-2.6.jar,\
commons-lang3-3.12.0.jar,\
commons-logging-1.2.jar,\
commons-net-3.0.1.jar,\
commons-pool-1.6.jar,\
commons-pool2-2.4.2.jar,\
commonsserver.jar,\
commons-text-1.10.0.jar,\
commons-validator-1.7.jar,\
com.sap.security.core.server.csi-1.00.8.jar,\
concurrentlinkedhashmap-lru-1.4.jar,\
coreserver.jar,\
customerreviewserver.jar,\
customersupportbackofficeserver.jar,\
ddlutils-1.0.jar,\
deliveryzoneserver.jar,\
dom4j-1.6.1.jar,\
dom4j-2.1.1.jar,\
drools-compiler-7.73.0.Final.jar,\
drools-core-7.73.2.SAP-PATCH.jar,\
drools-core-reflective-7.73.2.SAP-PATCH.jar,\
droolsruleengineservicesserver.jar,\
drools-templates-7.73.0.Final.jar,\
dumbster-1.6.jar,\
easymock-4.3.jar,\
easymockclassextension-3.1.jar,\
ecj-3.15.1.jar,\
ehcache-2.10.9.2.jar,\
el-api.jar,\
embeddedserverserver.jar,\
europe1server.jar,\
ezmorph-1.0.6.jar,\
failureaccess-1.0.1.jar,\
fastutil-6.5.6.jar,\
fest-assert-1.4.jar,\
fest-util-1.1.6.jar,\
fop-2.6.jar,\
geodesy-1.1.3.jar,\
gmapsz-3.0.5.jar,\
groovy-3.0.9.jar,\
groovy-ant-3.0.9.jar,\
groovy-json-3.0.9.jar,\
groovy-jsr223-3.0.9.jar,\
groovy-servlet-3.0.9.jar,\
groovy-templates-3.0.9.jar,\
groovy-test-3.0.9.jar,\
groovy-xml-3.0.9.jar,\
gson-2.8.8.jar,\
gson-2.9.0.jar,\
guava-32.0.1-jre.jar,\
hacserver.jar,\
hamcrest-all-1.3.jar,\
hibernate-validator-6.1.5.Final.jar,\
hk2-2.6.1.jar,\
hk2-api-2.6.1.jar,\
hk2-locator-2.6.1.jar,\
hk2-utils-2.6.1.jar,\
hsqldb-2.4.1.jar,\
http-builder-0.7.1.jar,\
httpclient-4.5.13.jar,\
httpcore-4.4.13.jar,\
httpmime-4.5.13.jar,\
httpunit-1.7.jar,\
hybris-encryption-1.1.jar,\
hybriskey-1.0.jar,\
hybrislicence.jar,\
hybris-simple-statistics-1.0.1.jar,\
impexserver.jar,\
jackson-annotations-2.13.4.jar,\
jackson-core-2.13.4.jar,\
jackson-databind-2.13.4.2.jar,\
jackson-datatype-jdk8-2.9.10.jar,\
jackson-datatype-jsr310-2.9.10.jar,\
jackson-jaxrs-base-2.12.3.jar,\
jackson-jaxrs-json-provider-2.12.3.jar,\
jackson-module-jaxb-annotations-2.12.3.jar,\
jai-codec-1.1.3.jar,\
jai_core-1.1.3.jar,\
jasper-el.jar,\
jasper.jar,\
jaspic-api.jar,\
java-sizeof-0.0.4.jar,\
javassist-3.21.0-GA.jar,\
javax.annotation-api-1.3.2.jar,\
javax.inject-2.5.0-b42.jar,\
javax.json-1.1.4.jar,\
javax.json-api-1.1.4.jar,\
javax.ws.rs-api-2.0.1.jar,\
javolution-5.5.1.jar,\
jaxb-api-2.3.3.jar,\
jaxb-core-*******.jar,\
jaxb-impl-2.3.4.jar,\
jaxb-xjc-2.2.11.jar,\
jaxen-1.2.0.jar,\
jboss-logging-3.3.2.Final.jar,\
jcip-annotations-1.0.jar,\
jcl-over-slf4j-1.7.30.jar,\
jersey-apache-connector-2.25.1.jar,\
jersey-client-1.13.jar,\
jersey-client-2.25.1.jar,\
jersey-common-2.25.1.jar,\
jersey-core-1.13.jar,\
jersey-entity-filtering-2.34.jar,\
jersey-guava-2.25.1.jar,\
jersey-json-1.13.jar,\
jersey-media-json-jackson-2.34.jar,\
jersey-server-2.34.jar,\
jersey-servlet-1.13.jar,\
jersey-spring-1.13.jar,\
jersey-spring5-2.34.jar,\
jersey-test-framework-core-1.34.jar,\
jfreechart-1.0.14.jar,\
jgroups-4.0.24.Final.jar,\
jgroups-kubernetes-1.0.9.Final.jar,\
jmx_prometheus_javaagent-0.18.0.jar,\
joda-time-2.9.9.jar,\
json-lib-2.4-jdk13.jar,\
json-path-2.9.0.jar,\
json-smart-2.5.2.jar,\
json-unit-assertj-2.8.0.jar,\
json-unit-core-2.8.0.jar,\
json-unit-json-path-2.8.0.jar,\
jsoup-1.14.2.jar,\
jsp-api.jar,\
jsr305-2.0.0.jar,\
jsr311-api-1.1.1.jar,\
jstl-api-1.2.jar,\
junit-4.13.2.jar,\
junit4-dataprovider-2.6.jar,\
junit-dataprovider-core-2.6.jar,\
keycloak-adapter-core-24.0.5.jar,\
keycloak-adapter-spi-24.0.5.jar,\
keycloak-common-24.0.5.jar,\
keycloak-core-24.0.5.jar,\
keycloak-spring-security-adapter-24.0.5.jar,\
kie-api-7.73.0.Final.jar,\
kie-internal-7.73.0.Final.jar,\
kie-soup-commons-7.73.0.Final.jar,\
kie-soup-maven-support-7.73.0.Final.jar,\
kie-soup-project-datamodel-commons-7.73.0.Final.jar,\
kxml2-2.3.0.jar,\
likey-no-iaik-jdk11-1.0.9.jar,\
localstack-utils-0.2.1.jar,\
log4j-1.2.17.jar,\
log4j-api-2.17.2.jar,\
log4j-core-2.17.2.jar,\
log4j-slf4j-impl-2.17.2.jar,\
jakarta.mail-1.6.5.jar,\
mediaconversionbackofficeserver.jar,\
mediaconversionserver.jar,\
mediawebserver.jar,\
metrics-core-4.1.18.jar,\
metrics-jmx-4.1.18.jar,\
metrics-healthchecks-4.1.18.jar,\
mime-util-2.1.3.jar,\
mockito-core-3.12.4.jar,\
models.jar,\
mvel2-2.4.4.Final.jar,\
mysql-connector-j-8.2.0.jar,\
nekohtml-1.9.22.jar,\
netty-buffer-4.1.118.Final.jar,\
netty-codec-4.1.118.Final.jar,\
netty-codec-http-4.1.118.Final.jar,\
netty-codec-socks-4.1.118.Final.jar,\
netty-common-4.1.118.Final.jar,\
netty-handler-4.1.118.Final.jar,\
netty-handler-proxy-4.1.118.Final.jar,\
netty-resolver-4.1.118.Final.jar,\
netty-transport-4.1.118.Final.jar,\
noggit-0.8.jar,\
oauth2server.jar,\
objenesis-3.2.jar,\
opentest4j-1.2.0.jar,\
ordercalculationserver.jar,\
org.eclipse.persistence.antlr-2.7.6.jar,\
org.eclipse.persistence.asm-2.7.6.jar,\
org.eclipse.persistence.core-2.7.6.jar,\
org.eclipse.persistence.moxy-2.7.6.jar,\
orika-core-1.5.4.jar,\
oro-2.0.8.jar,\
owasp-java-html-sanitizer-20211018.2.jar,\
paranamer-2.8.jar,\
paymentserver.jar,\
paymentstandardserver.jar,\
personalizationservicesserver.jar,\
platformbackofficeserver.jar,\
poi-5.2.2.jar,\
poi-ooxml-5.2.2.jar,\
poi-ooxml-schemas-5.2.2.jar,\
processingserver.jar,\
promotionengineservicesserver.jar,\
promotionsbackofficeserver.jar,\
promotionsserver.jar,\
protobuf-java-3.21.7.jar,\
quartz-2.3.2.jar,\
reactive-streams-1.0.3.jar,\
reactor-core-3.4.41.RELEASE.jar,\
reflections-0.9.11.jar,\
rhino-1.7.7.jar,\
ruledefinitionsserver.jar,\
ruleenginebackofficeserver.jar,\
ruleengineserver.jar,\
ruleengineservicesserver.jar,\
rxjava-1.3.8.jar,\
sac-1.3.jar,\
scimark-2.0.jar,\
scriptingserver.jar,\
serializer-2.7.2.jar,\
servlet-api.jar,\
sitemesh-3.0-alpha-2.jar,\
slf4j-api-1.7.30.jar,\
solrfacetsearchbackofficeserver.jar,\
solrfacetsearchserver.jar,\
solrserverserver.jar,\
solr-solrj-8.11.2.jar,\
spock-core-1.3-groovy-2.5.jar,\
spock-spring-1.3-groovy-2.5.jar,\
spotbugs-annotations-3.1.12.jar,\
spring-aop-5.3.42.jar,\
spring-aspects-5.3.42.jar,\
spring-beans-5.3.42.jar,\
spring-context-5.3.42.jar,\
spring-context-support-5.3.42.jar,\
spring-core-5.3.42.jar,\
spring-expression-5.3.42.jar,\
springfox-core-2.9.2.jar,\
springfox-schema-2.9.2.jar,\
springfox-spi-2.9.2.jar,\
springfox-spring-web-2.9.2.jar,\
springfox-swagger2-2.9.2.jar,\
springfox-swagger-common-2.9.2.jar,\
springfox-swagger-ui-2.9.2.jar,\
spring-aop-5.3.42.jar,\
spring-aspects-5.3.42.jar,\
spring-beans-5.3.42.jar,\
spring-context-5.3.42.jar,\
spring-context-support-5.3.42.jar,\
spring-core-5.3.42.jar,\
spring-expression-5.3.42.jar,\
spring-hateoas-1.2.2.jar,\
spring-instrument-5.3.42.jar,\
spring-integration-core-5.5.7.jar,\
spring-integration-event-5.5.7.jar,\
spring-integration-feed-5.5.7.jar,\
spring-integration-file-5.5.7.jar,\
spring-integration-ftp-5.5.7.jar,\
spring-integration-groovy-5.5.7.jar,\
spring-integration-http-5.5.7.jar,\
spring-integration-ip-5.5.7.jar,\
spring-integration-jdbc-5.5.7.jar,\
spring-integration-jms-5.5.7.jar,\
spring-integration-jmx-5.5.7.jar,\
spring-integration-mail-5.5.7.jar,\
spring-integration-rmi-5.5.7.jar,\
spring-integration-scripting-5.5.7.jar,\
spring-integration-security-5.5.7.jar,\
spring-integration-sftp-5.5.7.jar,\
spring-integration-stream-5.5.7.jar,\
spring-integration-test-5.5.7.jar,\
spring-integration-ws-5.5.7.jar,\
spring-integration-xml-5.5.7.jar,\
spring-integration-xmpp-5.5.7.jar,\
spring-integration-zip-2.0.0.RELEASE.jar,\
spring-jdbc-5.3.42.jar,\
spring-jms-5.3.42.jar,\
spring-messaging-5.3.42.jar,\
spring-mobile-device-1.1.5.RELEASE.jar,\
spring-orm-5.3.42.jar,\
spring-oxm-5.3.42.jar,\
spring-plugin-core-1.2.0.RELEASE.jar,\
spring-plugin-metadata-1.2.0.RELEASE.jar,\
spring-retry-1.3.1.jar,\
spring-security-config-5.8.16.jar,\
spring-security-core-5.8.16.jar,\
spring-security-jwt-1.0.10.RELEASE.jar,\
spring-security-oauth2-2.5.2.RELEASE.jar,\
spring-security-web-5.8.16.jar,\
spring-session-1.3.5.RELEASE.jar,\
spring-test-5.3.42.jar,\
spring-tx-5.3.42.jar,\
spring-web-5.3.42.jar,\
spring-ws-core-3.1.1.jar,\
spring-xml-3.0.10.RELEASE.jar,\
stax2-api-3.1.4.jar,\
stax-utils-20070216.jar,\
stripe-java-18.4.0.jar,\
swagger-annotations-1.5.22.jar,\
swagger-models-1.5.22.jar,\
ticketsystembackofficeserver.jar,\
ticketsystemserver.jar,\
tomcat-api.jar,\
tomcat-coyote.jar,\
tomcat-dbcp.jar,\
tomcatembeddedserverserver.jar,\
tomcat-i18n-es.jar,\
tomcat-i18n-fr.jar,\
tomcat-i18n-ja.jar,\
tomcat-i18n-ru.jar,\
tomcat-jdbc.jar,\
tomcat-jni.jar,\
tomcat-juli-adapters.jar,\
tomcat-juli.jar,\
tomcat-util.jar,\
tomcat-util-scan.jar,\
tomcat-websocket.jar,\
validation-api-2.0.1.Final.jar,\
validationserver.jar,\
velocity-engine-core-2.3.jar,\
velocity-engine-patch-2.3-1.jar,\
velocity-tools-generic-3.1.jar,\
voucherbackofficeserver.jar,\
voucherserver.jar,\
webfragmentExt_backofficesolrsearch.jar,\
webservicescommonsserver.jar,\
websocket-api.jar,\
wiremock-jre8-standalone-2.35.0.jar,\
woodstox-core-asl-4.4.1.jar,\
workflowserver.jar,\
wrapper.jar,\
wro4j-core-1.8.0.jar,\
wro4j-core-1.7.9.jar,\
wss4j-1.6.19.jar,\
xalan-2.7.2.jar,\
xercesImpl-2.12.0.jar,\
xml-apis-1.4.01.jar,\
xml-apis-ext-1.3.04.jar,\
xmlbeans-5.0.3.jar,\
xml-combiner-2.2.jar,\
xmlenc-0.52.jar,\
xmlgraphics-commons-2.7.jar,\
xml-resolver-1.2.jar,\
xmlsec-2.2.6.jar,\
xstream-1.4.21.jar,\
ybootstrap.jar,\
yimpexgenerator-*******.jar,\
ytomcat.jar,\
zcommon-8.6.3.jar,\
zel-8.6.3.jar,\
zhtml-8.6.3.jar,\
zk-8.6.3.jar,\
zkbind-8.6.3.jar,\
zkcharts-3.0.2.jar,\
zkex-8.6.3.jar,\
zkmax-8.6.3.jar,\
zkplus-8.6.3.jar,\
zkspring-core-3.2.0.jar,\
zookeeper-3.8.0.jar,\
zul-8.6.3.jar,\
zuti-8.6.3.jar,\
zweb-8.6.3.jar

# Default list of JAR files that should be scanned that overrides the default
# jarsToSkip list above. This is typically used to include a specific JAR that
# has been excluded by a broad file name pattern in the jarsToSkip list.
# The list of JARs to scan may be over-ridden at a Context level for individual
# scan types by configuring a JarScanner with a nested JarScanFilter.
tomcat.util.scan.StandardJarScanFilter.jarsToScan=\
log4j-web*.jar,log4j-taglib*.jar,log4javascript*.jar,slf4j-taglib*.jar

# String cache configuration.
tomcat.util.buf.StringCache.byte.enabled=true
#tomcat.util.buf.StringCache.char.enabled=true
#tomcat.util.buf.StringCache.trainThreshold=500000
#tomcat.util.buf.StringCache.cacheSize=5000

# Allow for changes to HTTP request validation
# WARNING: Using this option will expose the server to CVE-2016-6816
#tomcat.util.http.parser.HttpParser.requestTargetAllow=|
