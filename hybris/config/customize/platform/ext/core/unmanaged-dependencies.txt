fastutil-6.5.6.jar
jaxb-api-2.3.1.jar
jaxb-core-2.3.0.1.jar
jaxb-xjc-2.2.11.jar
spring-session-1.3.5.RELEASE.jar
rxjava-1.3.8.jar
jersey-guava-2.25.1.jar
commons-collections4-4.4.jar
joda-time-2.13.0.jar
commons-collections-3.2.2.jar
jakarta.mail-1.6.5.jar
wss4j-1.6.19.jar
javolution-5.5.1.jar
bsh-2.0b6.jar
commons-configuration-1.10.jar
ddlutils-1.0.jar
antlr4-runtime-4.7.1.jar
httpcore-4.4.13.jar
commons-lang-2.6.jar
serializer-2.7.2.jar
commons-beanutils-1.9.4.jar
jaxen-1.2.0.jar
json-path-2.9.0.jar
commons-digester-2.1.jar
commons-dbcp-1.4.jar
swagger-models-1.5.22.jar
commons-jexl-1.1.jar
reflections-0.9.11.jar
mime-util-2.1.3.jar
concurrentlinkedhashmap-lru-1.4.jar
commons-el-1.0.jar
commons-pool2-2.4.2.jar
commons-pool-1.6.jar
jgroups-kubernetes-1.0.9.Final.jar
commons-discovery-0.5.jar
fest-assert-1.4.jar
annotations-2.0.0.jar
activation-1.1.1.jar
oro-2.0.8.jar
commons-logging-1.2.jar
commons-email-1.5.jar
kxml2-2.3.0.jar
asm-3.3.1.jar
httpmime-4.5.13.jar
paranamer-2.8.jar
jsr305-2.0.0.jar
fest-util-1.1.6.jar
jstl-api-1.2.jar
javax.annotation-api-1.3.2.jar
swagger-annotations-1.5.22.jar
jcl-over-slf4j-1.7.30.jar
xml-combiner-2.2.jar
xmlenc-0.52.jar
easymockclassextension-3.1.jar
javax.inject-2.4.0-b31.jar
failureaccess-1.0.1.jar
aopalliance-1.0.jar
jcip-annotations-1.0.jar
hybris-simple-statistics-1.0.1.jar
hybriskey-1.0.jar
charon-1.4.1.jar
hybris-encryption-1.1.jar
btc-ascii-table-1.0.jar
likey-no-iaik-jdk17-1.0.10.jar
com.sap.security.core.server.csi-1.00.8.jar
spring-aop-5.3.42.jar
spring-aspects-5.3.42.jar
spring-beans-5.3.42.jar
spring-context-5.3.42.jar
spring-context-support-5.3.42.jar
spring-core-5.3.42.jar
spring-expression-5.3.42.jar
spring-instrument-5.3.42.jar
spring-jdbc-5.3.42.jar
spring-jms-5.3.42.jar
spring-messaging-5.3.42.jar
spring-orm-5.3.42.jar
spring-oxm-5.3.42.jar
spring-test-5.3.42.jar
spring-tx-5.3.42.jar
spring-web-5.3.42.jar
spring-webmvc-5.3.42.jar
