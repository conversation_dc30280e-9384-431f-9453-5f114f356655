package com.sast.cis.orderprocess.converter.aa;

import com.sast.cis.core.dto.portal.*;
import com.sast.cis.core.model.*;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.core.service.customer.InternalCustomerUidTranslationService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import de.hybris.platform.core.model.order.AbstractOrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.core.model.user.UserModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import de.hybris.platform.variants.model.VariantProductModel;
import generated.com.sast.cis.core.model.*;
import generated.de.hybris.platform.core.model.c2l.CountryBuilder;
import generated.de.hybris.platform.core.model.c2l.CurrencyBuilder;
import generated.de.hybris.platform.core.model.order.AbstractOrderEntryBuilder;
import generated.de.hybris.platform.core.model.order.OrderBuilder;
import generated.de.hybris.platform.core.model.user.UserBuilder;
import generated.de.hybris.platform.variants.model.VariantProductBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.List;

import static com.sast.cis.core.enums.LicenseType.*;
import static java.util.List.of;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.util.Lists.emptyList;
import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class OrderModelToAaOrderConfirmationDtoPopulatorUnitTest {

    private static final String EUR_ISOCODE = "EUR";
    private static final String USD_ISOCODE = "USD";
    private static final String ORDER_CODE = "TestOrderCode";
    private static final String COMPANY_ACCOUNT_ID = "TestRemoteID";
    private static final String DEVELOPER_COMPANY_ID = "DEVELOPER_COMPANY_ID";
    private static final String INTEGRATOR_USER_ID = "TestIntegratorUserID@shop";
    private static final String INTEGRATOR_USER_SSOID = "TestIntegratorUserID";
    private static final String DISTRIBUTOR_COMPANY_NAME = "testDistributorName";
    private static final String DISTRIBUTOR_COMPANY_ID = "testDistributorID";
    private static final Date CREATION_TIMESTAMP = new Date();
    private static final String APP_CODE = "TestAppCode";
    private static final String APP_PACKAGE_NAME = "test.package.name";
    private static final String SELLER_PRODUCT_ID = "testSellerProductId";
    private static final String INVALID_UID = "InvalidUserUid";
    private static final String INVALID_PRODUCT_CODE = "InvalidProductCode";
    private static final double BASE_CONVERSION = 1d;
    private static final double EUR_USD_CONVERSION = 1.2d;

    @Mock
    private InternalCustomerUidTranslationService<IntegratorModel> internalIntegratorUidTranslationService;

    @Mock
    private IotCompanyService iotCompanyService;

    @Mock
    private Converter<AbstractOrderEntryModel, AaOrderEntryDto> abstractOrderEntryModelToAaOrderEntryDtoConverter;

    @InjectMocks
    private OrderModelToAaOrderConfirmationDtoPopulator orderModelToAaOrderConfirmationDtoPopulator;

    private IntegratorModel mockIntegrator;
    private CurrencyModel orderCurrency;

    private AppModel app;
    private AppLicenseModel appLicense;
    private OrderModel order;
    private AaDistributorCompanyModel mockAaDistributorCompany;

    private AbstractOrderEntryModel orderEntry;
    private AaOrderEntryDto orderEntryDto;

    @Before
    public void setUp() {
        mockIntegrator = IntegratorBuilder.generate()
            .withUid(INTEGRATOR_USER_ID)
            .withCompany(IoTCompanyBuilder.generate()
                .withUid(COMPANY_ACCOUNT_ID)
                .buildMockInstance())
            .buildMockInstance();

        orderCurrency = CurrencyBuilder.generate().withConversion(BASE_CONVERSION).withIsocode(EUR_ISOCODE).buildMockInstance();
        CurrencyModel developerCurrency = CurrencyBuilder.generate().withConversion(EUR_USD_CONVERSION).withIsocode(USD_ISOCODE)
            .buildMockInstance();
        CountryModel developerCountry = CountryBuilder.generate().withCurrency(developerCurrency).buildMockInstance();
        IoTCompanyModel developerCompany = IoTCompanyBuilder
            .generate()
            .withUid(DEVELOPER_COMPANY_ID)
            .withCountry(developerCountry)
            .buildMockInstance();

        when(iotCompanyService.getDeveloperCompanyForOrderItems(any(OrderModel.class))).thenReturn(developerCompany);
        when(internalIntegratorUidTranslationService.translateToSsoId(mockIntegrator)).thenReturn(INTEGRATOR_USER_SSOID);

        BundleInfoModel bundleInfo = BundleInfoBuilder.generate().withName("S").withSize(1).buildMockInstance();
        app = AppBuilder.generate()
            .withCode(APP_CODE)
            .withPackageName(APP_PACKAGE_NAME)
            .buildMockInstance();
        appLicense = AppLicenseBuilder.generate()
            .withSellerProductId(SELLER_PRODUCT_ID)
            .withBaseProduct(app)
            .withBundleInfo(bundleInfo)
            .buildMockInstance();
        when(appLicense.getLicenseType()).thenReturn(SUBSCRIPTION);
        orderEntry = AbstractOrderEntryBuilder.generate()
            .withProduct(appLicense)
            .withQuantity(1L).buildMockInstance();
        orderEntryDto = new AaOrderEntryDto();
        mockAaDistributorCompany = AaDistributorCompanyBuilder.generate()
            .withUmpId(DISTRIBUTOR_COMPANY_ID)
            .withCompanyName(DISTRIBUTOR_COMPANY_NAME)
            .buildMockInstance();
        order = validOrderBuilder()
            .withEntries(of(orderEntry))
            .withStatus(OrderStatus.COMPLETED)
            .withAaDistributorCompany(mockAaDistributorCompany)
            .buildMockInstance();

        when(abstractOrderEntryModelToAaOrderEntryDtoConverter.convertAll(List.of(orderEntry))).thenReturn(List.of(orderEntryDto));
    }

    private OrderBuilder<?, ?> validOrderBuilder() {
        return OrderBuilder.generate()
            .withUser(mockIntegrator)
            .withCreationtime(CREATION_TIMESTAMP)
            .withCurrency(orderCurrency)
            .withStatus(OrderStatus.COMPLETED)
            .withCode(ORDER_CODE)
            .withAaDistributorCompany(mockAaDistributorCompany)
            .withEntries(of(orderEntry));
    }

    @Test
    public void validOrder_returnsFilledOrderConfirmation() {
        AaOrderConfirmationDto aaOrderConfirmationDto = new AaOrderConfirmationDto();
        orderModelToAaOrderConfirmationDtoPopulator.populate(order, aaOrderConfirmationDto);

        assertThat(aaOrderConfirmationDto).usingRecursiveComparison().isEqualTo(validOrderConfirmation());
    }

    private AaOrderConfirmationDto validOrderConfirmation() {
        return new AaOrderConfirmationDto()
            .withCreationTimestamp(CREATION_TIMESTAMP)
            .withBuyer(new BuyerDto()
                .withCompanyId(COMPANY_ACCOUNT_ID)
                .withUserId(INTEGRATOR_USER_SSOID))
            .withSeller(new SellerDto()
                .withCompanyId(DEVELOPER_COMPANY_ID))
            .withDistributorCompany(new DistributorCompanyDto()
                .withCompanyId(DISTRIBUTOR_COMPANY_ID)
                .withCompanyName(DISTRIBUTOR_COMPANY_NAME))
            .withOrderId(ORDER_CODE)
            .withOrderStatus(OrderStatus.COMPLETED.getCode())
            .withOrderCurrency(orderCurrency.getIsocode())
            .withOrderEntries(of(orderEntryDto));
    }

    @Test
    public void emptyOrder_returnsOrderConfirmationWithEmptyOrder() {
        when(appLicense.getLicenseType()).thenReturn(FULL);

        OrderModel emptyOrder = validOrderBuilder().withEntries(emptyList()).buildMockInstance();
        AaOrderConfirmationDto aaOrderConfirmationDto = new AaOrderConfirmationDto();
        orderModelToAaOrderConfirmationDtoPopulator.populate(emptyOrder, aaOrderConfirmationDto);

        assertThat(aaOrderConfirmationDto)
            .usingRecursiveComparison().isEqualTo(validOrderConfirmation().withOrderEntries(emptyList()));
    }

    @Test
    public void orderContainingSomethingOtherThanAppLicense_throws() {
        when(appLicense.getLicenseType()).thenReturn(FULL);
        VariantProductModel variant = VariantProductBuilder.generate().withCode(INVALID_PRODUCT_CODE).buildMockInstance();
        when(orderEntry.getProduct()).thenReturn(variant);

        assertThrows(IllegalArgumentException.class, () ->
            orderModelToAaOrderConfirmationDtoPopulator.populate(order, new AaOrderConfirmationDto()));
    }

    @Test
    public void userOfOrderIsNotAnIntegrator_throws() {
        UserModel user = UserBuilder.generate().withUid(INVALID_UID).buildMockInstance();
        when(order.getUser()).thenReturn(user);

        assertThrows(IllegalArgumentException.class,
            () -> orderModelToAaOrderConfirmationDtoPopulator.populate(this.order, new AaOrderConfirmationDto()));
    }
}
