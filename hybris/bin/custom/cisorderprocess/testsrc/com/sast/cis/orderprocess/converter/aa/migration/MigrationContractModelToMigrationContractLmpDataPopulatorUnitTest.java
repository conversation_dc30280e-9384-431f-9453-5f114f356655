package com.sast.cis.orderprocess.converter.aa.migration;

import com.sast.cis.aa.core.model.MigrationContractModel;
import com.sast.cis.core.dto.portal.MigrationContractLmpData;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.servicelayer.dto.converter.ConversionException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class MigrationContractModelToMigrationContractLmpDataPopulatorUnitTest {

    private static final String UMP_CONTRACT_ID = "TestUmpContractId";
    private static final String UNIQUE_BUNDLE_IDENTIFIER = "TestUniqueBundleIdentifier";

    @InjectMocks
    private MigrationContractModelToMigrationContractLmpDataPopulator populator;

    @Mock
    private MigrationContractModel contractModel;

    @Test
    public void populate_validGroupModels_populatesLmpDataList() throws ConversionException {
        when(contractModel.getUmpContractId()).thenReturn(UMP_CONTRACT_ID);
        when(contractModel.getUniqueBundleIdentifier()).thenReturn(UNIQUE_BUNDLE_IDENTIFIER);

        final MigrationContractLmpData lmpData = new MigrationContractLmpData();

        populator.populate(contractModel, lmpData);

        MigrationContractLmpData expectedLmpData = new MigrationContractLmpData()
            .withUpmContractId(UMP_CONTRACT_ID)
            .withBundleId(UNIQUE_BUNDLE_IDENTIFIER);

        assertThat(lmpData).usingRecursiveComparison().isEqualTo(expectedLmpData);
    }
}
