package com.sast.cis.orderprocess.converter.aa.migration;

import com.sast.cis.aa.core.model.MigrationOrderDraftModel;
import com.sast.cis.aa.core.model.MigrationOrderEntryDraftModel;
import com.sast.cis.core.dto.portal.*;
import com.sast.cis.core.model.AaDistributorCompanyModel;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.company.IotCompanyService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import generated.com.sast.cis.aa.core.model.MigrationOrderDraftBuilder;
import generated.com.sast.cis.aa.core.model.MigrationOrderEntryDraftBuilder;
import generated.com.sast.cis.core.model.AaDistributorCompanyBuilder;
import generated.com.sast.cis.core.model.AppBuilder;
import generated.com.sast.cis.core.model.AppLicenseBuilder;
import generated.com.sast.cis.core.model.IoTCompanyBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;
import java.util.List;
import java.util.Set;

import static com.sast.cis.core.enums.LicenseType.SUBSCRIPTION;
import static java.util.List.of;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class MigrationOrderDraftModelToAaOrderConfirmationDtoPopulatorUnitTest {

    private static final Date CREATION_TIMESTAMP = new Date();
    private static final String ORDER_CODE = "TestOrderCode";
    private static final String SELLER_COMPANY_ID = "SELLER_COMPANY_ID";
    private static final String BUYER_COMPANY_ID = "BUYER_COMPANY_ID";
    private static final String DISTRIBUTOR_COMPANY_NAME = "testDistributorName";
    private static final String DISTRIBUTOR_COMPANY_ID = "testDistributorID";
    private static final String APP_CODE = "TestAppCode";
    private static final String APP_PACKAGE_NAME = "test.package.name";
    private static final String SELLER_PRODUCT_ID = "testSellerProductId";

    @Mock
    private IotCompanyService iotCompanyService;

    @Mock
    private Converter<MigrationOrderEntryDraftModel, AaOrderEntryDto> migrationOrderDraftModelToAaOrderEntryDtoConverter;

    @InjectMocks
    private MigrationOrderDraftModelToAaOrderConfirmationDtoPopulator migrationOrderDraftModelToAaOrderConfirmationDtoPopulator;

    private AppModel app;
    private AppLicenseModel appLicense;
    private MigrationOrderDraftModel order;

    private MigrationOrderEntryDraftModel orderEntry;
    private AaOrderEntryDto orderEntryDto;

    @Before
    public void setUp() {
        IoTCompanyModel sellerCompany = IoTCompanyBuilder
            .generate()
            .withUid(SELLER_COMPANY_ID)
            .buildMockInstance();

        IoTCompanyModel buyerCompany = IoTCompanyBuilder
            .generate()
            .withUid(BUYER_COMPANY_ID)
            .buildMockInstance();

        AaDistributorCompanyModel aaDistributorCompany = AaDistributorCompanyBuilder.generate()
            .withUmpId(DISTRIBUTOR_COMPANY_ID)
            .withCompanyName(DISTRIBUTOR_COMPANY_NAME)
            .buildMockInstance();

        app = AppBuilder.generate()
            .withCode(APP_CODE)
            .withPackageName(APP_PACKAGE_NAME)
            .withCompany(sellerCompany)
            .buildMockInstance();

        appLicense = AppLicenseBuilder.generate()
            .withSellerProductId(SELLER_PRODUCT_ID)
            .withBaseProduct(app)
            .buildMockInstance();

        when(appLicense.getLicenseType()).thenReturn(SUBSCRIPTION);
        orderEntry = MigrationOrderEntryDraftBuilder.generate()
            .withAppLicense(appLicense)
            .withQuantity(1)
            .buildMockInstance();

        orderEntryDto = new AaOrderEntryDto();

        order = MigrationOrderDraftBuilder.generate()
            .withCreationtime(CREATION_TIMESTAMP)
            .withCode(ORDER_CODE)
            .withEntries(Set.of(orderEntry))
            .withCompany(buyerCompany)
            .withAaDistributor(aaDistributorCompany)
            .buildMockInstance();

        when(iotCompanyService.getDeveloperCompanyFromMigrationDraft(order)).thenReturn(sellerCompany);
        when(migrationOrderDraftModelToAaOrderEntryDtoConverter.convertAll(Set.of(orderEntry))).thenReturn(List.of(orderEntryDto));
    }

    @Test
    public void validOrder_returnsFilledOrderConfirmation() {
        AaOrderConfirmationDto aaOrderConfirmationDto = new AaOrderConfirmationDto();
        migrationOrderDraftModelToAaOrderConfirmationDtoPopulator.populate(order, aaOrderConfirmationDto);

        assertThat(aaOrderConfirmationDto).usingRecursiveComparison().isEqualTo(validOrderConfirmation());
    }

    private AaOrderConfirmationDto validOrderConfirmation() {
        return new AaOrderConfirmationDto()
            .withCreationTimestamp(CREATION_TIMESTAMP)
            .withBuyer(new BuyerDto()
                .withCompanyId(BUYER_COMPANY_ID))
            .withSeller(new SellerDto()
                .withCompanyId(SELLER_COMPANY_ID))
            .withDistributorCompany(new DistributorCompanyDto()
                .withCompanyId(DISTRIBUTOR_COMPANY_ID)
                .withCompanyName(DISTRIBUTOR_COMPANY_NAME))
            .withOrderId(ORDER_CODE)
            .withOrderEntries(of(orderEntryDto));
    }
}
