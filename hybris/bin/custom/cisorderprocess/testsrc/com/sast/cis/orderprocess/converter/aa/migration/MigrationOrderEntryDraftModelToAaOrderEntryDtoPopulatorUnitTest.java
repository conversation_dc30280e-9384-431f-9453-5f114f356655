package com.sast.cis.orderprocess.converter.aa.migration;

import com.sast.cis.aa.core.contentmodules.converter.ContentModuleToAaContentModuleDtoConverter;
import com.sast.cis.aa.core.model.*;
import com.sast.cis.core.dto.portal.*;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.enums.StoreProductType;
import com.sast.cis.core.migration.MigrationOrderDraftService;
import com.sast.cis.core.model.*;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static java.util.Collections.emptyList;
import static java.util.List.of;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class MigrationOrderEntryDraftModelToAaOrderEntryDtoPopulatorUnitTest {

    private static final String APP_CODE = "TestAppCode";
    private static final String APP_PACKAGE_NAME = "test.package.name";
    private static final String SELLER_PRODUCT_ID = "testSellerProductId";
    private static final int BUNDLE_SIZE = 1;
    private static final String LMP_MODULE_CODE = "testLmpModuleCode";

    @Mock
    private ContentModuleToAaContentModuleDtoConverter contentModuleToAaContentModuleDtoConverter;

    @Mock
    private Converter<MigrationContractModel, MigrationContractLmpData> migrationContractModelToMigrationContractLmpDataConverter;

    @Mock
    private AppModel app;

    @Mock
    private AppLicenseModel appLicense;

    @Mock
    private ContentModuleModel contentModule;

    @Mock
    private RuntimeModel runtime;

    @Mock
    private BundleInfoModel bundleInfo;

    @Mock
    private MigrationContractModel migrationContract;

    @Mock
    private MigrationContractModel anotherMigrationContract;

    @Mock
    private MigrationOrderEntryDraftModel entry;

    @Mock
    private MigrationOrderDraftService migrationOrderDraftService;

    private MigrationOrderEntryDraftModelToAaOrderEntryDtoPopulator populator;

    @Before
    public void setUp() {
        populator = new MigrationOrderEntryDraftModelToAaOrderEntryDtoPopulator(
            contentModuleToAaContentModuleDtoConverter,
            migrationContractModelToMigrationContractLmpDataConverter,
            migrationOrderDraftService);

        when(contentModule.getLmpModuleCode()).thenReturn(LMP_MODULE_CODE);

        when(appLicense.getBaseProduct()).thenReturn(app);
        when(appLicense.getSellerProductId()).thenReturn(SELLER_PRODUCT_ID);
        when(appLicense.getBundleInfo()).thenReturn(bundleInfo);
        when(appLicense.getContentModules()).thenReturn(of(contentModule));
        when(appLicense.getRuntime()).thenReturn(runtime);
        when(appLicense.getLicenseType()).thenReturn(LicenseType.SUBSCRIPTION);

        when(entry.getAppLicense()).thenReturn(appLicense);
        when(entry.getQuantity()).thenReturn(1);

        when(migrationOrderDraftService.getMigrationContractsByMigrationOrderEntryDraft(entry)).thenReturn(List.of(migrationContract, anotherMigrationContract));

        when(contentModuleToAaContentModuleDtoConverter.convertContentModules(any(List.class), any(RuntimeModel.class)))
            .thenReturn(of(new AaContentModuleDto()));

        when(migrationContractModelToMigrationContractLmpDataConverter.convertAll(anyList()))
            .thenReturn(List.of(
                new MigrationContractLmpData(),
                new MigrationContractLmpData()
            ));

        when(app.getCode()).thenReturn(APP_CODE);
        when(app.getPackageName()).thenReturn(APP_PACKAGE_NAME);

        when(bundleInfo.getSize()).thenReturn(BUNDLE_SIZE);
    }

    @Test
    public void testPopulate() {
        AaOrderEntryDto aaOrderEntryDto = new AaOrderEntryDto();

        populator.populate(entry, aaOrderEntryDto);

        assertThat(aaOrderEntryDto).usingRecursiveComparison().isEqualTo(validAaOrderEntryDto());
    }

    @Test
    public void testPopulate_noContentModule_dtoPopulatedWithAppLicenseContentModule() {
        AaOrderEntryDto aaOrderEntryDto = new AaOrderEntryDto();
        AaOrderEntryDto expectedAaOrderEntryDto = validAaOrderEntryDto();

        when(app.getContentModules()).thenReturn(emptyList());
        when(appLicense.getContentModules()).thenReturn(emptyList());
        expectedAaOrderEntryDto.setModules(emptyList());

        populator.populate(entry, aaOrderEntryDto);

        assertThat(aaOrderEntryDto).usingRecursiveComparison().isEqualTo(expectedAaOrderEntryDto);
    }

    @Test
    public void testPopulate_withEmptyContractGroups() {
        AaOrderEntryDto aaOrderEntryDto = new AaOrderEntryDto();

        when(migrationContractModelToMigrationContractLmpDataConverter.convertAll(anyList())).thenReturn(emptyList());

        populator.populate(entry, aaOrderEntryDto);

        assertThat(aaOrderEntryDto.getMigrationContracts()).isEmpty();
    }

    @Test
    public void testPopulate_withNullQuantity() {
        AaOrderEntryDto aaOrderEntryDto = validAaOrderEntryDto();

        when(entry.getQuantity()).thenReturn(null);
        populator.populate(entry, aaOrderEntryDto);

        assertThat(aaOrderEntryDto.getQuantity()).isEqualTo(0);
    }

    private AaOrderEntryDto validAaOrderEntryDto() {
        return new AaOrderEntryDto()
            .withQuantity(1)
            .withBundleAmount(1)
            .withBaseProduct(new OrderItemProductDto()
                .withApplicationId(APP_PACKAGE_NAME)
                .withCode(APP_CODE)
                .withSellerPartNumber(SELLER_PRODUCT_ID)
                .withType(StoreProductType.AA_LICENSE))
            .withLicenseType(LicenseType.SUBSCRIPTION.getCode())
            .withBillOfMaterials(of(new AaContentModuleDto()))
            .withModules(of(LMP_MODULE_CODE))
            .withMigrationContracts(of(
                new MigrationContractLmpData(),
                new MigrationContractLmpData())
            );
    }
}
