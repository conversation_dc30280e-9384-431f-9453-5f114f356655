package com.sast.cis.orderprocess.service

import com.sast.cis.core.enums.PaymentMethodType
import com.sast.cis.core.enums.TerminationRuleUnit
import com.sast.cis.core.model.*
import com.sast.cis.core.service.customer.integrator.IntegratorService
import com.sast.cis.core.service.subscription.TerminationRuleService
import com.sast.cis.email2.constants.EmailType
import com.sast.cis.email2.dto.aa.AaEmailOrderItem
import com.sast.cis.email2.dto.aa.AaEmailPaymentData
import com.sast.cis.email2.dto.aa.AaOrderSuccessData
import com.sast.cis.email2.service.CisEmailService
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.c2l.CurrencyModel
import de.hybris.platform.core.model.order.OrderEntryModel
import de.hybris.platform.servicelayer.config.ConfigurationService
import de.hybris.platform.testframework.JUnitPlatformSpecification
import generated.com.sast.cis.core.model.*
import generated.de.hybris.platform.core.model.c2l.CurrencyBuilder
import generated.de.hybris.platform.core.model.c2l.LanguageBuilder
import generated.de.hybris.platform.core.model.order.OrderBuilder
import generated.de.hybris.platform.core.model.order.OrderEntryBuilder
import org.apache.commons.configuration.Configuration
import org.junit.Test

import java.time.Period

@UnitTest
class AaOrderEmailServiceUnitSpec extends JUnitPlatformSpecification {
    private static final String ORDER_CODE = '982734'
    private static final String IBAN = '**********************'
    private static final String GERMAN = 'de'
    private static final String LMP_URL = 'https://worksonmymachine.lol/licenses'
    private static final String USER_EMAIL = '<EMAIL>'
    private static final String USER_NAME = 'Hans Dampf'
    private static final CurrencyModel EUR = CurrencyBuilder.generate().withIsocode('EUR').buildInstance()
    private static final SepaMandatePaymentInfoModel PAYMENT_INFO = SepaMandatePaymentInfoBuilder.generate()
        .withIBAN(IBAN)
        .buildInstance()
    private static final IoTCompanyModel COMPANY = IoTCompanyBuilder.generate()
            .withCommunicationLanguage(LanguageBuilder.generate().withIsocode(GERMAN).buildInstance())
            .buildInstance()
    private static final ORDER_USER = IntegratorBuilder.generate()
            .withName(USER_NAME)
            .withEmailAddress(USER_EMAIL)
            .buildInstance()
    private static final TerminationRulePeriodModel YEAR = TerminationRulePeriodBuilder.generate()
            .withUnit(TerminationRuleUnit.YEAR).withValue(1).buildInstance()
    private static final TerminationRulePeriodModel THREE_MONTH = TerminationRulePeriodBuilder.generate()
            .withUnit(TerminationRuleUnit.MONTH).withValue(3).buildInstance()

    private ConfigurationService configurationService = Mock()
    private CisEmailService cisEmailService = Mock()
    private TerminationRuleService terminationRuleService = Mock()
    private IntegratorService integratorService = Mock()

    private AaOrderEmailService aaOrderEmailService

    private Configuration mockConfig = Mock()

    private AppLicenseModel fixedLicense
    private AppLicenseModel subscriptionLicense
    private AppLicenseModel onetimeLicense

    def setup() {
        aaOrderEmailService = new AaOrderEmailService(
                configurationService,
                cisEmailService,
                terminationRuleService,
                integratorService
        )

        integratorService.checkAndCast(ORDER_USER) >> ORDER_USER
        configurationService.getConfiguration() >> mockConfig
        mockConfig.getString('aa.dmp.url') >> LMP_URL

        AppModel fixedApp = AppBuilder.generate()
                .withName('Fixed App', Locale.forLanguageTag(GERMAN))
                .buildInstance()
        fixedLicense = AppLicenseBuilder.generate()
                .withBaseProduct(fixedApp)
                .withName('Fixed License', Locale.forLanguageTag(GERMAN))
                .buildInstance()
        AppModel subscriptionApp = AppBuilder.generate()
                .withName('Subscription App', Locale.forLanguageTag(GERMAN))
                .buildInstance()
        subscriptionLicense = AppLicenseBuilder.generate()
                .withBaseProduct(subscriptionApp)
                .withName('Subscription License', Locale.forLanguageTag(GERMAN))
                .buildInstance()
        AppModel onetimeApp = AppBuilder.generate()
                .withName('Onetime App', Locale.forLanguageTag(GERMAN))
                .buildInstance()
        onetimeLicense = AppLicenseBuilder.generate()
                .withBaseProduct(onetimeApp)
                .withName('Onetime License', Locale.forLanguageTag(GERMAN))
                .buildInstance()

        terminationRuleService.getTerminationRule(fixedLicense) >> ContractTerminationRuleBuilder.generate()
                .withFixedPeriod(YEAR)
                .buildInstance()
        terminationRuleService.getTerminationRule(subscriptionLicense) >> ContractTerminationRuleBuilder.generate()
                .withInitialPeriod(THREE_MONTH)
                .buildInstance()
        terminationRuleService.getTerminationRule(onetimeLicense) >> null
        terminationRuleService.toPeriod(YEAR) >> Period.ofYears(1)
        terminationRuleService.toPeriod(THREE_MONTH) >> Period.ofMonths(3)
    }

    @Test
    void 'order email for a given valid order'() {
        given:

        def givenOrder = OrderBuilder.generate()
                .withCode(ORDER_CODE)
                .withUser(ORDER_USER)
                .withEntries([
                        createEntry(fixedLicense, 499.0, 3),
                        createEntry(subscriptionLicense, 100.0, 2),
                        createEntry(onetimeLicense, 350.0, 2)
                ])
                .withCurrency(EUR)
                .withPaymentInfo(PAYMENT_INFO)
                .withCompany(COMPANY)
                .buildInstance()

        def expectedData = AaOrderSuccessData.builder()
            .emailLanguage(GERMAN)
            .orderNumber(ORDER_CODE)
            .customerName(USER_NAME)
            .payment(AaEmailPaymentData.builder().method(PaymentMethodType.SEPA_DIRECTDEBIT).identifier('····1527').build())
            .invoiceEmail(USER_EMAIL)
            .orderItems([
                    AaEmailOrderItem.builder().productName('Fixed App').variantName('Fixed License').amount(BigDecimal.valueOf(499.0))
                            .totalAmount(BigDecimal.valueOf(1497.0)).currency('EUR').billingCycle('P1Y').quantity(3).build(),
                    AaEmailOrderItem.builder().productName('Subscription App').variantName('Subscription License').amount(BigDecimal.valueOf(100.0))
                            .totalAmount(BigDecimal.valueOf(200.0)).currency('EUR').billingCycle('P3M').quantity(2).build(),
                    AaEmailOrderItem.builder().productName('Onetime App').variantName('Onetime License').amount(BigDecimal.valueOf(350.0))
                            .totalAmount(BigDecimal.valueOf(700.0)).currency('EUR').billingCycle(null).quantity(2).build()
            ])
            .licenseManagementUrl(new URI(LMP_URL))
            .build()

        when:
        aaOrderEmailService.sendOrderSuccessDelayedActivationEmail(givenOrder)

        then:
        1 * cisEmailService.sendEmail(EmailType.ORDER_SUCCESS_DELAYED_ACTIVATION, ORDER_USER, expectedData)
    }

    @Test
    void 'order without user throws IllegalStateException'() {
        given:

        def givenOrder = OrderBuilder.generate()
                .withCode(ORDER_CODE)
                .withUser(null)
                .withEntries([
                        createEntry(fixedLicense, 499.0, 3),
                ])
                .withCurrency(EUR)
                .withPaymentInfo(PAYMENT_INFO)
                .withCompany(COMPANY)
                .buildInstance()

        when:
        aaOrderEmailService.sendOrderSuccessDelayedActivationEmail(givenOrder)

        then:
        thrown(IllegalStateException)
    }

    @Test
    void 'order without company throws IllegalStateException'() {
        given:

        def givenOrder = OrderBuilder.generate()
                .withCode(ORDER_CODE)
                .withUser(ORDER_USER)
                .withEntries([
                        createEntry(fixedLicense, 499.0, 3),
                ])
                .withCurrency(EUR)
                .withPaymentInfo(PAYMENT_INFO)
                .withCompany(null)
                .buildInstance()

        when:
        aaOrderEmailService.sendOrderSuccessDelayedActivationEmail(givenOrder)

        then:
        thrown(IllegalStateException)
    }

    @Test
    void 'failures when determining termination rules are ignored'() {
        given:

        def givenOrder = OrderBuilder.generate()
                .withCode(ORDER_CODE)
                .withUser(ORDER_USER)
                .withEntries([
                        createEntry(fixedLicense, 499.0, 3),
                        createEntry(subscriptionLicense, 100.0, 2),
                        createEntry(onetimeLicense, 350.0, 2)
                ])
                .withCurrency(EUR)
                .withPaymentInfo(PAYMENT_INFO)
                .withCompany(COMPANY)
                .buildInstance()

        def expectedData = AaOrderSuccessData.builder()
                .emailLanguage(GERMAN)
                .orderNumber(ORDER_CODE)
                .customerName(USER_NAME)
                .payment(AaEmailPaymentData.builder().method(PaymentMethodType.SEPA_DIRECTDEBIT).identifier('····1527').build())
                .invoiceEmail(USER_EMAIL)
                .orderItems([
                        AaEmailOrderItem.builder().productName('Fixed App').variantName('Fixed License').amount(BigDecimal.valueOf(499.0))
                                .totalAmount(BigDecimal.valueOf(1497.0)).currency('EUR').billingCycle(null).quantity(3).build(),
                        AaEmailOrderItem.builder().productName('Subscription App').variantName('Subscription License').amount(BigDecimal.valueOf(100.0))
                                .totalAmount(BigDecimal.valueOf(200.0)).currency('EUR').billingCycle('P3M').quantity(2).build(),
                        AaEmailOrderItem.builder().productName('Onetime App').variantName('Onetime License').amount(BigDecimal.valueOf(350.0))
                                .totalAmount(BigDecimal.valueOf(700.0)).currency('EUR').billingCycle(null).quantity(2).build()
                ])
                .licenseManagementUrl(new URI(LMP_URL))
                .build()

        when:
        aaOrderEmailService.sendOrderSuccessDelayedActivationEmail(givenOrder)

        then:
        terminationRuleService.getTerminationRule(fixedLicense) >> {throw new RuntimeException()}
        1 * cisEmailService.sendEmail(EmailType.ORDER_SUCCESS_DELAYED_ACTIVATION, ORDER_USER, expectedData)
    }

    private static OrderEntryModel createEntry(AppLicenseModel license, Double price, Long quantity) {
        return OrderEntryBuilder.generate()
                .withProduct(license)
                .withBasePrice(price)
                .withTotalPrice(quantity * price)
                .withQuantity(quantity)
                .buildInstance()
    }
}
