package com.sast.cis.orderprocess.converter.aa;

import com.sast.cis.aa.core.contentmodules.converter.ContentModuleToAaContentModuleDtoConverter;
import com.sast.cis.aa.core.model.ContentModuleModel;
import com.sast.cis.aa.core.model.MigrationContractModel;
import com.sast.cis.aa.core.model.MigrationOrderEntryDraftModel;
import com.sast.cis.core.dto.portal.*;
import com.sast.cis.core.enums.StoreProductType;
import com.sast.cis.core.migration.MigrationOrderDraftService;
import com.sast.cis.core.model.*;
import com.sast.cis.orderprocess.converter.FixedTermContractDtoProvider;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.order.OrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static com.sast.cis.core.enums.LicenseType.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.util.Lists.emptyList;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class AbstractOrderEntryModelToAaOrderEntryDtoPopulatorUnitTest {

    private static final String EVALUATION_LICENSE_TYPE = "EVALUATION";
    private static final String FULL_LICENSE_TYPE = "FULL";
    private static final String SUBSCRIPTION_LICENSE_TYPE = "SUBSCRIPTION";
    private static final String APP_CODE = "TestAppCode";
    private static final String APP_PACKAGE_NAME = "test.package.name";
    private static final String SELLER_PRODUCT_ID = "testSellerProductId";

    @Mock
    private Converter<SubscriptionContractModel, SubscriptionDto> subscriptionConverter;

    @Mock
    private ContentModuleToAaContentModuleDtoConverter contentModuleToAaContentModuleDtoConverter;

    @Mock
    private FixedTermContractDtoProvider fixedTermContractDtoProvider;

    @Mock
    private Converter<MigrationContractModel, MigrationContractLmpData> migrationContractModelToMigrationContractLmpDataConverter;

    @Mock
    private MigrationOrderDraftService migrationOrderDraftService;

    @Mock
    private OrderEntryModel orderEntry;

    @Mock
    private RuntimeModel runtime;

    @Mock
    private ContentModuleModel contentModule;

    @Mock
    private BundleInfoModel bundleInfo;

    @Mock
    private AppModel app;

    @Mock
    private AppLicenseModel appLicense;

    @Mock
    private OrderModel order;

    @Mock
    private MigrationOrderEntryDraftModel migrationOrderEntryDraft;

    private AbstractOrderEntryModelToAaOrderEntryDtoPopulator abstractOrderEntryModelToAaOrderEntryDtoPopulator;
    private AaContentModuleDto aaContentModuleDto;
    private FixedTermContractDto fixedTermContractDto;

    @Before
    public void setUp() {
        abstractOrderEntryModelToAaOrderEntryDtoPopulator = new AbstractOrderEntryModelToAaOrderEntryDtoPopulator(
            contentModuleToAaContentModuleDtoConverter,
            fixedTermContractDtoProvider,
            subscriptionConverter,
            migrationContractModelToMigrationContractLmpDataConverter,
            migrationOrderDraftService);

        aaContentModuleDto = new AaContentModuleDto();
        fixedTermContractDto = new FixedTermContractDto();

        when(contentModuleToAaContentModuleDtoConverter.convertContentModules(List.of(contentModule), runtime))
            .thenReturn(List.of(aaContentModuleDto));
        when(contentModule.getLmpModuleCode()).thenReturn("lmpModuleTestCode");
        when(fixedTermContractDtoProvider.createFixedTermContractDTOsForOrderEntry(orderEntry))
            .thenReturn(List.of());

        when(bundleInfo.getSize()).thenReturn(1);

        when(app.getCode()).thenReturn(APP_CODE);
        when(app.getPackageName()).thenReturn(APP_PACKAGE_NAME);
        when(app.getContentModules()).thenReturn(List.of(contentModule));

        when(appLicense.getSellerProductId()).thenReturn(SELLER_PRODUCT_ID);
        when(appLicense.getBaseProduct()).thenReturn(app);
        when(appLicense.getRuntime()).thenReturn(runtime);
        when(appLicense.getBundleInfo()).thenReturn(bundleInfo);
        when(appLicense.getLicenseType()).thenReturn(SUBSCRIPTION);

        when(orderEntry.getProduct()).thenReturn(appLicense);
        when(orderEntry.getQuantity()).thenReturn(1L);
        when(orderEntry.getOrder()).thenReturn(order);

        when(order.isMigrationOrder()).thenReturn(false);
    }

    @Test
    public void validOrder_returnsOrderItemWithFixedTermContracts() {
        when(appLicense.getLicenseType()).thenReturn(FULL);
        when(fixedTermContractDtoProvider.createFixedTermContractDTOsForOrderEntry(orderEntry))
            .thenReturn(List.of(fixedTermContractDto));

        final AaOrderEntryDto aaOrderEntryDto = new AaOrderEntryDto();
        abstractOrderEntryModelToAaOrderEntryDtoPopulator.populate(orderEntry, aaOrderEntryDto);
        AaOrderEntryDto validAaOrderEntryDto = validAaOrderEntryDto(FULL_LICENSE_TYPE);
        validAaOrderEntryDto.withFixedTermContracts(List.of(fixedTermContractDto));

        assertThat(aaOrderEntryDto).usingRecursiveComparison().isEqualTo(validAaOrderEntryDto);
    }

    @Test
    public void validSubscriptionOrder_returnsFilledOrderConfirmation() {
        final SubscriptionContractModel subscription = mock(SubscriptionContractModel.class);

        when(orderEntry.getBuyerContracts()).thenReturn(List.of(subscription));
        final SubscriptionDto subscriptionDto = new SubscriptionDto();

        when(subscriptionConverter.convert(subscription)).thenReturn(subscriptionDto);
        final AaOrderEntryDto aaOrderEntryDto = new AaOrderEntryDto();

        abstractOrderEntryModelToAaOrderEntryDtoPopulator.populate(orderEntry, aaOrderEntryDto);

        final var expectedAaOrderEntryDto = validAaOrderEntryDto(SUBSCRIPTION_LICENSE_TYPE);
        expectedAaOrderEntryDto.setSubscriptionContracts(List.of(subscriptionDto));

        assertThat(aaOrderEntryDto).usingRecursiveComparison().isEqualTo(expectedAaOrderEntryDto);
        verify(subscriptionConverter).convert(subscription);
    }

    @Test
    public void validOrderOfEvaluationLicense_returnsFilledOrderConfirmation() {
        when(appLicense.getLicenseType()).thenReturn(EVALUATION);
        AaOrderEntryDto aaOrderEntryDto = new AaOrderEntryDto();
        abstractOrderEntryModelToAaOrderEntryDtoPopulator.populate(orderEntry, aaOrderEntryDto);

        assertThat(aaOrderEntryDto).usingRecursiveComparison().isEqualTo(validAaOrderEntryDto(EVALUATION_LICENSE_TYPE));
    }

    @Test
    public void populate_contentModuleAttachedToAppLicense_dtoPopulatedWithAppLicenseContentModule() {
        when(appLicense.getLicenseType()).thenReturn(FULL);

        AaOrderEntryDto aaOrderEntryDto = new AaOrderEntryDto();
        abstractOrderEntryModelToAaOrderEntryDtoPopulator.populate(orderEntry, aaOrderEntryDto);

        assertThat(aaOrderEntryDto).usingRecursiveComparison().isEqualTo(validAaOrderEntryDto(FULL_LICENSE_TYPE));
    }

    @Test
    public void validNonBundleLicenseOrder_returnsFilledOrderConfirmation() {
        when(appLicense.getLicenseType()).thenReturn(FULL);

        AaOrderEntryDto aaOrderEntryDto = new AaOrderEntryDto();
        abstractOrderEntryModelToAaOrderEntryDtoPopulator.populate(orderEntry, aaOrderEntryDto);

        assertThat(aaOrderEntryDto).usingRecursiveComparison().isEqualTo(validAaOrderEntryDto(FULL_LICENSE_TYPE));
    }

    @Test
    public void populate_noContentModule_dtoPopulatedWithAppLicenseContentModule() {
        aaContentModuleDto = null;
        when(appLicense.getLicenseType()).thenReturn(FULL);
        when(appLicense.getBundleInfo()).thenReturn(null);
        when(app.getContentModules()).thenReturn(emptyList());
        when(appLicense.getContentModules()).thenReturn(emptyList());

        AaOrderEntryDto aaOrderEntryDto = new AaOrderEntryDto();
        abstractOrderEntryModelToAaOrderEntryDtoPopulator.populate(orderEntry, aaOrderEntryDto);

        AaOrderEntryDto expectedAaOrderEntryDto = validAaOrderEntryDto(FULL_LICENSE_TYPE);
        assertThat(aaOrderEntryDto).usingRecursiveComparison().isEqualTo(expectedAaOrderEntryDto);
    }

    @Test
    public void populateValidMigrationOrder_dtoPopulatedWithMigrationContracts() {
        final MigrationContractModel migrationContractModel = mock(MigrationContractModel.class);
        final MigrationContractModel anotherMigrationContractModel = mock(MigrationContractModel.class);
        final List<MigrationContractModel> migrationContracts = List.of(migrationContractModel, anotherMigrationContractModel);

        when(appLicense.getLicenseType()).thenReturn(FULL);
        when(order.isMigrationOrder()).thenReturn(true);
        when(migrationOrderDraftService.getMigrationOrderEntryDraftByResultingOrderEntry(orderEntry)).thenReturn(migrationOrderEntryDraft);
        when(migrationOrderDraftService.getMigrationContractsByMigrationOrderEntryDraft(migrationOrderEntryDraft)).thenReturn(
            migrationContracts);
        when(migrationContractModelToMigrationContractLmpDataConverter.convertAll(migrationContracts))
            .thenReturn(List.of(
                new MigrationContractLmpData().withUpmContractId("migrationContract1"),
                new MigrationContractLmpData().withUpmContractId("migrationContract2")
            ));

        final AaOrderEntryDto aaOrderEntryDto = new AaOrderEntryDto();
        abstractOrderEntryModelToAaOrderEntryDtoPopulator.populate(orderEntry, aaOrderEntryDto);
        AaOrderEntryDto validAaOrderEntryDto = validAaOrderEntryDto(FULL_LICENSE_TYPE);
        validAaOrderEntryDto.withMigrationContracts(
            List.of(
                new MigrationContractLmpData().withUpmContractId("migrationContract1"),
                new MigrationContractLmpData().withUpmContractId("migrationContract2")
            ));

        assertThat(aaOrderEntryDto).usingRecursiveComparison().isEqualTo(validAaOrderEntryDto);
    }

    private AaOrderEntryDto validAaOrderEntryDto(final String licenseType) {
        List<AaContentModuleDto> contentModuleDtos = aaContentModuleDto != null ? List.of(aaContentModuleDto) : emptyList();
        List<String> lmpModuleCodes = aaContentModuleDto != null ? List.of("lmpModuleTestCode") : emptyList();
        return new AaOrderEntryDto()
            .withQuantity(1)
            .withBundleAmount(1)
            .withBaseProduct(new OrderItemProductDto()
                .withApplicationId(APP_PACKAGE_NAME)
                .withCode(APP_CODE)
                .withSellerPartNumber(SELLER_PRODUCT_ID)
                .withType(StoreProductType.AA_LICENSE))
            .withTotalPrice(0d)
            .withLicenseType(licenseType)
            .withSubscriptionContracts(emptyList())
            .withFixedTermContracts(emptyList())
            .withBillOfMaterials(contentModuleDtos)
            .withModules(lmpModuleCodes);
    }
}
