package com.sast.cis.orderprocess.process.aa;

import com.sast.cis.core.service.MattermostNotificationService;
import com.sast.cis.orderprocess.service.OrderEmailService;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.orderprocessing.model.OrderProcessModel;
import de.hybris.platform.processengine.action.AbstractSimpleDecisionAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@Deprecated
public class AaOrderSuccessMessageAction extends AbstractSimpleDecisionAction<OrderProcessModel> {
    private final OrderEmailService orderEmailService;
    private final MattermostNotificationService mattermostNotificationService;

    public AaOrderSuccessMessageAction(OrderEmailService orderEmailService,
        MattermostNotificationService mattermostNotificationService) {
        this.orderEmailService = orderEmailService;
        this.mattermostNotificationService = mattermostNotificationService;
    }

    @Override
    public Transition executeAction(OrderProcessModel orderProcess) {
        OrderModel order = orderProcess.getOrder();
        try {
            orderEmailService.sendOrderSuccessEmail(order);
        } catch (Exception e) {
            LOG.error("ALERT Error occurred while sending order success email for order={}", order.getCode(), e);
            return Transition.NOK;
        }

        notifyBusiness(order);

        notifyDeveloper(order);

        notifyAaServiceDesk(order);

        return Transition.OK;
    }

    private void notifyAaServiceDesk(OrderModel order) {
        try {
            orderEmailService.sendNotifyCustomerSupportEmail(order);
        } catch (Exception e) {
            LOG.error("Failed to send notify email to AA Service Desk when purchase success, order:{}", order.getCode(), e);
        }
    }

    private void notifyDeveloper(OrderModel order) {
        try {
            orderEmailService.sendNotifyDeveloperEmail(order);
        } catch (Exception e) {
            LOG.error("Failed to send notify email to App developers when purchase success, order:{}", order.getCode(), e);
        }
    }

    private void notifyBusiness(OrderModel order) {
        if (order.getTotalPrice() <= 0) {
            LOG.debug("skipping sending notification to internal team because order value {} is of low importance", order.getTotalPrice());
            return;
        }
        try {
            mattermostNotificationService.sendSuccessfulPurchaseMessage(order);
        } catch (Exception e) {
            LOG.error("Failed to send purchase notification to mattermost", e);
        }
    }
}
