package com.sast.cis.orderprocess.service;

import com.google.common.base.Preconditions;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.ContractTerminationRuleModel;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.service.customer.integrator.IntegratorService;
import com.sast.cis.core.service.subscription.TerminationRuleService;
import com.sast.cis.email2.constants.EmailType;
import com.sast.cis.email2.dto.aa.AaEmailOrderItem;
import com.sast.cis.email2.dto.aa.AaOrderSuccessData;
import com.sast.cis.email2.service.CisEmailService;
import com.sast.cis.email2.service.EmailLanguageHelper;
import com.sast.cis.email2.typehandler.order.EmailPaymentDataFactory;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.URI;
import java.time.Period;
import java.util.List;
import java.util.Locale;
import java.util.Optional;

import static com.sast.cis.core.constants.CiscoreConstants.LMP_URL_AA;

@Service
@Slf4j
@RequiredArgsConstructor
public class AaOrderEmailService {
    private final ConfigurationService configurationService;
    private final CisEmailService cisEmailService;
    private final TerminationRuleService terminationRuleService;
    private final IntegratorService integratorService;

    @SneakyThrows
    public void sendOrderSuccessDelayedActivationEmail(OrderModel order) {
        Preconditions.checkState(order.getCompany() != null,
                "Order with code=%s has no company", order.getCode());
        IntegratorModel integrator = getIntegrator(order);
        String lmpUrl = configurationService.getConfiguration().getString(LMP_URL_AA);
        String emailLanguage = EmailLanguageHelper.determineEmailLanguage(order.getCompany());

        AaOrderSuccessData orderSuccessData = AaOrderSuccessData.builder()
                .emailLanguage(emailLanguage)
                .orderNumber(order.getCode())
                .customerName(integrator.getName())
                .payment(EmailPaymentDataFactory.forPaymentInfo(order.getPaymentInfo()))
                .invoiceEmail(integrator.getEmailAddress())
                .orderItems(getAaOrderItems(order, Locale.forLanguageTag(emailLanguage)))
                .licenseManagementUrl(new URI(lmpUrl))
                .build();

        cisEmailService.sendEmail(EmailType.ORDER_SUCCESS_DELAYED_ACTIVATION, integrator, orderSuccessData);
    }

    private List<AaEmailOrderItem> getAaOrderItems(OrderModel order, Locale locale) {
        return CollectionUtils.emptyIfNull(order.getEntries()).stream()
                .map(item -> {
                    AppLicenseModel appLicense = (AppLicenseModel) item.getProduct();
                    AppModel license = (AppModel) appLicense.getBaseProduct();

                    return AaEmailOrderItem.builder()
                            .productName(license.getName(locale))
                            .variantName(appLicense.getName(locale))
                            .amount(BigDecimal.valueOf(item.getBasePrice()))
                            .totalAmount(BigDecimal.valueOf(item.getTotalPrice()))
                            .currency(order.getCurrency().getIsocode())
                            .billingCycle(getBillingCycle(appLicense).map(Period::toString).orElse(null))
                            .quantity(item.getQuantity() != null ? item.getQuantity() : 1)
                            .build();
                })
                .toList();
    }

    private IntegratorModel getIntegrator(OrderModel order) {
        if (order.getUser() == null) {
            throw new IllegalStateException(String.format("Order with code=%s has no user", order.getCode()));
        }
        return integratorService.checkAndCast(order.getUser());
    }

    private Optional<Period> getBillingCycle(AppLicenseModel license) {
        try {
            ContractTerminationRuleModel contractTerminationRuleModel = terminationRuleService
                    .getTerminationRule(license);

            if (contractTerminationRuleModel != null && contractTerminationRuleModel.getInitialPeriod() != null) {
                return Optional.of(terminationRuleService.toPeriod(contractTerminationRuleModel.getInitialPeriod()));
            } else if(contractTerminationRuleModel != null && contractTerminationRuleModel.getFixedPeriod() != null) {
                return Optional.of(terminationRuleService.toPeriod(contractTerminationRuleModel.getFixedPeriod()));
            }
            return Optional.empty();
        } catch (Exception e) {
            LOG.warn("Could not determine billing cycle for product {}", license.getCode(), e);
            return Optional.empty();
        }
    }
}
