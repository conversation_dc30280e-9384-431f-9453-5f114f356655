package com.sast.cis.orderprocess.process.aa;

import com.sast.cis.core.enums.Feature;
import com.sast.cis.core.service.FeatureToggleService;
import com.sast.cis.orderprocess.service.AaOrderEmailService;
import com.sast.cis.orderprocess.service.OrderEmailService;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.orderprocessing.model.OrderProcessModel;
import de.hybris.platform.processengine.action.AbstractSimpleDecisionAction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class DelayedOrderConfirmationMailAction extends AbstractSimpleDecisionAction<OrderProcessModel> {
    private final AaOrderEmailService aaOrderEmailService;
    private final OrderEmailService orderEmailService;
    private final FeatureToggleService featureToggleService;

    @Override
    public Transition executeAction(OrderProcessModel orderProcess) {
        OrderModel order = orderProcess.getOrder();
        if (!featureToggleService.isEnabledOrDefault(Feature.FEATURE_ENABLE_ORDER_DELAYED_CONFIRMATION, true)) {
            return sendOldConfirmationMail(order);
        }

        try {
            aaOrderEmailService.sendOrderSuccessDelayedActivationEmail(order);
        } catch (Exception e) {
            LOG.error("ALERT Error occurred while sending order success email for order={}", order.getCode(), e);
            return Transition.NOK;
        }
        return Transition.OK;
    }

    private Transition sendOldConfirmationMail(final OrderModel order) {
        try {
            orderEmailService.sendOrderSuccessEmail(order);
        } catch (Exception e) {
            LOG.error("ALERT Error occurred while sending order success email for order={}", order.getCode(), e);
            return Transition.NOK;
        }
        return Transition.OK;
    }
}
