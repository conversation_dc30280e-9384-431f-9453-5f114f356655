package com.sast.cis.orderprocess.process.aa;

import com.sast.cis.orderprocess.service.OrderEmailService;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.orderprocessing.model.OrderProcessModel;
import de.hybris.platform.processengine.action.AbstractSimpleDecisionAction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class ImmediateOrderConfirmationMailAction extends AbstractSimpleDecisionAction<OrderProcessModel> {
    private final OrderEmailService orderEmailService;

    @Override
    public Transition executeAction(OrderProcessModel orderProcess) {
        OrderModel order = orderProcess.getOrder();
        try {
            orderEmailService.sendOrderSuccessEmail(order);
        } catch (Exception e) {
            LOG.error("ALERT Error occurred while sending order success email for order={}", order.getCode(), e);
            return Transition.NOK;
        }
        return Transition.OK;
    }
}
