package com.sast.cis.ump.account.notifications.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sast.cis.core.data.UmpCompanyData;
import com.sast.cis.core.service.ObjectMapperService;
import com.sast.cis.core.service.company.sync.IotCompanySyncService;
import com.sast.cis.core.validator.UmpDataValidator;
import com.sast.cis.ump.account.TestDataLoader;
import com.sast.cis.ump.account.notifications.NotificationType;
import com.sast.cis.ump.account.notifications.dto.CompanyUpdateData;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CompanyUpdateNotificationHandlerUnitTest {

    @Mock
    private IotCompanySyncService iotCompanySyncService;

    @Mock
    private NotificationHandlerDispatcher dispatcher;

    @Mock
    private UmpDataValidator umpDataValidator;

    @Captor
    private ArgumentCaptor<UmpCompanyData> companyDataArgumentCaptor;

    private ObjectMapperService objectMapperService;
    private CompanyUpdateNotificationHandler handler;

    @Before
    public void setUp() {
        objectMapperService = new ObjectMapperService(new ObjectMapper());
        handler = new CompanyUpdateNotificationHandler(objectMapperService, dispatcher, iotCompanySyncService, umpDataValidator);
    }

    @Test
    public void testHandlerRegisteredWithCompanyUpdateNotificationType() {
        verify(dispatcher).registerHandler(NotificationType.COMPANY_UPDATE, handler);
    }

    @Test
    public void givenCompanyUpdateNotification_whenHandle_thenDeserializeAndInvokeCompanyUpdateService() throws Exception {
        final String companyUpdateJson = TestDataLoader.load("CompanyUpdateData.json");
        final NotificationPayload notificationPayload = new NotificationPayload(companyUpdateJson, "iotstore");

        handler.handle(notificationPayload);

        verify(iotCompanySyncService).updateOrCreateCompany(companyDataArgumentCaptor.capture());
        final UmpCompanyData capturedCompanyData = companyDataArgumentCaptor.getValue();
        final CompanyUpdateData payloadCompanyData = objectMapperService.toObject(companyUpdateJson, CompanyUpdateData.class);

        // recursive comparison ignoring is possible in newer version of assertJ library, when upgraded belowStatements can be reduced to one.
        assertThat(capturedCompanyData)
            .usingRecursiveComparison().ignoringFields("businessAddress", "billingAddress", "companyTypes", "marketplaceId")
            .isEqualTo(payloadCompanyData.getCompanyData());
        assertThat(capturedCompanyData.getBillingAddress()).usingRecursiveComparison()
            .isEqualTo(payloadCompanyData.getCompanyData().getBillingAddress());
        assertThat(capturedCompanyData.getBusinessAddress()).usingRecursiveComparison()
            .isEqualTo(payloadCompanyData.getCompanyData().getBusinessAddress());
        assertThat(capturedCompanyData.getCompanyTypes()).isEqualTo(payloadCompanyData.getCompanyData().getCompanyTypes());
    }
}
