package com.sast.cis.core.converter.product;

import com.sast.cis.core.data.DeviceCapabilityData;
import com.sast.cis.core.model.*;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import generated.com.sast.cis.core.model.*;
import generated.de.hybris.platform.core.model.product.ProductBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class ProductDeviceCapabilityPopulatorUnitTest {

    @Mock
    private Converter<DeviceCapabilityModel, DeviceCapabilityData> deviceCapabilityConverter;

    @InjectMocks
    private ProductDeviceCapabilityPopulator populator;

    @Test
    public void populate_WithApp_properlyPopulated() {
        DeviceCapabilityModel deviceCapability = createDeviceCapability();
        ApkMediaModel apk = createApk(Set.of(deviceCapability));
        AppVersionModel appVersion = createAppVersion(apk);
        AppModel app = createApp(appVersion);

        DeviceCapabilityData deviceCapabilityData = new DeviceCapabilityData();
        when(deviceCapabilityConverter.convert(deviceCapability)).thenReturn(deviceCapabilityData);

        ProductData actualResult = new ProductData();
        populator.populate(app, actualResult);

        ProductData expectedResult = new ProductData().withDeviceCapabilities(Set.of(deviceCapabilityData));

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_WithApp_properlyPopulated_noRepeat() {
        DeviceCapabilityModel deviceCapability1 = createDeviceCapability();
        DeviceCapabilityModel deviceCapability2 = createDeviceCapability();
        DeviceCapabilityModel deviceCapability3 = createDeviceCapability();
        ApkMediaModel apk = createApk(Set.of(deviceCapability1, deviceCapability2, deviceCapability3));
        AppVersionModel appVersion = createAppVersion(apk);
        AppModel app = createApp(appVersion);

        DeviceCapabilityData deviceCapabilityData1 = new DeviceCapabilityData();
        deviceCapabilityData1.setCode("123");
        DeviceCapabilityData deviceCapabilityData2 = new DeviceCapabilityData();
        deviceCapabilityData2.setCode("123");
        DeviceCapabilityData deviceCapabilityData3 = new DeviceCapabilityData();
        deviceCapabilityData3.setCode("123");
        when(deviceCapabilityConverter.convert(deviceCapability1)).thenReturn(deviceCapabilityData1);
        when(deviceCapabilityConverter.convert(deviceCapability2)).thenReturn(deviceCapabilityData2);
        when(deviceCapabilityConverter.convert(deviceCapability3)).thenReturn(deviceCapabilityData3);

        ProductData actualResult = new ProductData();
        populator.populate(app, actualResult);

        assertThat(actualResult.getDeviceCapabilities()).hasSize(1);
    }

    @Test
    public void populate_WithAppWithoutDeviceCapability_properlyPopulatedWithoutDeviceCapabilities() {
        ApkMediaModel apk = createApk(Set.of());
        AppVersionModel appVersion = createAppVersion(apk);
        AppModel app = createApp(appVersion);

        ProductData actualResult = new ProductData();
        populator.populate(app, actualResult);

        ProductData expectedResult = new ProductData().withDeviceCapabilities(Set.of());

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_WithUnknownProduct_notPopulated() {
        ProductModel product = ProductBuilder.generate().buildMockInstance();

        ProductData actualResult = new ProductData();
        populator.populate(product, actualResult);

        ProductData expectedResult = new ProductData();

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }


    private AppModel createApp(AppVersionModel appVersion) {
        return AppBuilder.generate().withLatestVersion(appVersion).buildMockInstance();
    }

    private AppVersionModel createAppVersion(ApkMediaModel apk) {
        return AppVersionBuilder.generate().withApk(apk).buildMockInstance();
    }

    private ApkMediaModel createApk(Set<DeviceCapabilityModel> deviceCapabilities) {
        return ApkMediaBuilder.generate().withDeviceCapabilities(deviceCapabilities).buildMockInstance();
    }

    private DeviceCapabilityModel createDeviceCapability() {
        return DeviceCapabilityBuilder.generate().buildMockInstance();
    }

}
