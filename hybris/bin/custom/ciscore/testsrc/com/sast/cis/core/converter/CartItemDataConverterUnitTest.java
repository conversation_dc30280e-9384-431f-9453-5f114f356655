package com.sast.cis.core.converter;

import com.sast.cis.core.constants.MediaFormat;
import com.sast.cis.core.data.*;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.BundleInfoModel;
import com.sast.cis.core.model.RuntimeModel;
import com.sast.cis.core.service.AppLicenseService;
import com.sast.cis.core.service.TranslationService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.cms2.common.service.SessionSearchRestrictionsDisabler;
import de.hybris.platform.commercefacades.order.data.OrderEntryData;
import de.hybris.platform.commercefacades.product.data.ImageData;
import de.hybris.platform.commercefacades.product.data.ImageDataType;
import de.hybris.platform.commercefacades.product.data.PriceData;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.servicelayer.dto.converter.ConversionException;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.rules.ExpectedException.none;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CartItemDataConverterUnitTest {

    private static final String FULL_LICENSE_CODE = "product.details.license.full";
    private static final String TEST_LICENSE = "TestLicense";
    private static final BigDecimal TOTAL_PRICE = BigDecimal.TEN;
    private static final BigDecimal ITEM_PRICE = BigDecimal.ONE;
    private static final String TEST_COMPANY = "TestCompany";
    private static final String LOGO_URL = "abc/xyz.img";
    private static final String SMALL_LOGO_URL = "abc/xyz_small.img";
    private static final String APP_CODE = "A_1234";

    @Mock
    private AppLicenseService appLicenseService;

    @Mock
    private TranslationService translationService;

    @Mock
    private PriceDataConverter priceDataConverter;

    @Mock
    private Converter<PriceData, ScalePriceInfo> commercePriceDataConverter;

    @Mock
    private Converter<RuntimeModel, RuntimeData> runtimeConverter;

    @Mock
    private Converter<BundleInfoModel, BundleInfoData> bundleInfoConverter;

    @Mock
    private SessionSearchRestrictionsDisabler sessionSearchRestrictionsDisabler;

    @Rule
    public ExpectedException thrown = none();

    private CartItemDataConverter cartItemDataConverter;

    @Mock
    private AppLicenseModel appLicense;

    @Mock
    private AppModel appModel;

    @Mock
    private RuntimeModel runtimeModel;

    private CartItemData expectedResult;
    private PriceData totalPriceData;
    private PriceData itemPriceData;
    private com.sast.cis.core.data.PriceData convertedTotalPriceData;
    private com.sast.cis.core.data.PriceData convertedItemPriceData;
    private Set<PriceData> futurePrices;
    private RuntimeData runtimeData;

    @Before
    public void setup() {
        cartItemDataConverter = new CartItemDataConverter(
            appLicenseService, translationService, priceDataConverter, sessionSearchRestrictionsDisabler,
            commercePriceDataConverter, bundleInfoConverter, runtimeConverter
        );

        totalPriceData = new PriceData().withFormattedValue(TOTAL_PRICE.toString());
        itemPriceData = new PriceData().withFormattedValue(ITEM_PRICE.toString());

        convertedTotalPriceData = new com.sast.cis.core.data.PriceData().withValue(TOTAL_PRICE.toString());
        convertedItemPriceData = new com.sast.cis.core.data.PriceData().withValue(ITEM_PRICE.toString());

        when(appLicenseService.getAppLicenseForCodeOrThrow(anyString())).thenReturn(appLicense);
        when(sessionSearchRestrictionsDisabler.execute(argThat(supplier -> supplier.get().equals(appLicense)))).thenReturn(appLicense);
        when(appLicense.getLicenseType()).thenReturn(LicenseType.FULL);
        when(appLicense.getBaseProduct()).thenReturn(appModel);
        when(appLicense.getRuntime()).thenReturn(runtimeModel);
        when(appModel.getCode()).thenReturn(APP_CODE);
        when(priceDataConverter.toPriceData(totalPriceData)).thenReturn(convertedTotalPriceData);
        when(priceDataConverter.toPriceData(itemPriceData)).thenReturn(convertedItemPriceData);
        when(translationService.translate(FULL_LICENSE_CODE)).thenReturn(TEST_LICENSE);
        when(commercePriceDataConverter.convertAll(any())).thenReturn(Collections.EMPTY_LIST);
        futurePrices = Collections.emptySet();

        runtimeData = new RuntimeData().withCode("runtime_full_3y");
        when(runtimeConverter.convert(runtimeModel)).thenReturn(runtimeData);
    }

    @Test
    public void convert_parameterIsNull_throwsException() {
        thrown.expect(ConversionException.class);
        cartItemDataConverter.convert(null);
    }

    @Test
    public void convert_productDataIsNull_throwsException() {
        thrown.expect(ConversionException.class);
        cartItemDataConverter.convert(new OrderEntryData());
    }

    @Test
    public void convert_imageDataIsEmpty_returnsCartItemDataWithEmptyLogoUrl() {
        ProductData productData = createProductData(Collections.emptyList());
        expectedResult = prepareExpectedResult(StringUtils.EMPTY, StringUtils.EMPTY, TEST_LICENSE, TEST_COMPANY);
        assertConversion(productData);
    }

    @Test
    public void convert_imageDataHasInValidType_returnsCartItemDataWithEmptyLogoUrl() {
        ProductData productData = createProductData(createImageDatas(ImageDataType.GALLERY, LOGO_URL, SMALL_LOGO_URL));
        expectedResult = prepareExpectedResult(StringUtils.EMPTY, StringUtils.EMPTY, TEST_LICENSE, TEST_COMPANY);
        assertConversion(productData);
    }

    @Test
    public void convert_NullLicenseName_returnsCartItemDataWithNoLicenseName() {
        when(translationService.translate(FULL_LICENSE_CODE)).thenReturn(null);
        ProductData productData = createProductData(createImageDatas(ImageDataType.PRIMARY, LOGO_URL, SMALL_LOGO_URL));
        expectedResult = prepareExpectedResult(LOGO_URL, SMALL_LOGO_URL, null, TEST_COMPANY);
        assertConversion(productData);
    }

    @Test
    public void convert_NoScalePrices_returnsCartItemDataWithEmptyScalePrices() {
        ProductData productData = createProductDataWithoutCompanyData(createImageDatas(ImageDataType.PRIMARY, LOGO_URL, SMALL_LOGO_URL));
        expectedResult = prepareExpectedResult(LOGO_URL, SMALL_LOGO_URL, TEST_LICENSE, null);
        assertConversion(productData);
    }

    @Test
    public void convert_withScalePrices_returnsCartItemDataWithCorrectScalePrices() {
        List<ScalePriceInfo> scalePriceInfos = List.of(new ScalePriceInfo());
        when(commercePriceDataConverter.convertAll(any())).thenReturn(scalePriceInfos);
        ProductData productData = createProductDataWithoutCompanyData(createImageDatas(ImageDataType.PRIMARY, LOGO_URL, SMALL_LOGO_URL));
        expectedResult = prepareExpectedResult(LOGO_URL, SMALL_LOGO_URL, TEST_LICENSE, null).withScalePrices(scalePriceInfos);
        assertConversion(productData);
    }

    @Test
    public void convert_withProductFuturePrices_returnsCartItemDataWithCorrectFuturePrices() {
        PriceData futurePriceData = new PriceData();
        com.sast.cis.core.data.PriceData convertedFuturePriceData = new com.sast.cis.core.data.PriceData();
        futurePrices = Set.of(futurePriceData);
        when(priceDataConverter.toPriceData(futurePriceData)).thenReturn(convertedFuturePriceData);
        ProductData productData = createProductDataWithoutCompanyData(createImageDatas(ImageDataType.PRIMARY, LOGO_URL, SMALL_LOGO_URL));
        expectedResult = prepareExpectedResult(LOGO_URL, SMALL_LOGO_URL, TEST_LICENSE, null)
            .withProductFuturePrices(Set.of(convertedFuturePriceData));
        assertConversion(productData);
    }

    @Test
    public void convert_NoCompanySet_returnsCartItemDataWithNoCompanyName() {
        when(translationService.translate(FULL_LICENSE_CODE)).thenReturn(null);
        ProductData productData = createProductDataWithoutCompanyData(createImageDatas(ImageDataType.PRIMARY, LOGO_URL, SMALL_LOGO_URL));
        expectedResult = prepareExpectedResult(LOGO_URL, SMALL_LOGO_URL, null, null);
        assertConversion(productData);
    }

    @Test
    public void convert_everythingIsValid_returnsCartItemDataWithConversion() {
        ProductData productData = createProductData(createImageDatas(ImageDataType.PRIMARY, LOGO_URL, SMALL_LOGO_URL));
        expectedResult = prepareExpectedResult(LOGO_URL, SMALL_LOGO_URL, TEST_LICENSE, TEST_COMPANY);
        assertConversion(productData);
    }

    @Test
    public void givenLicenseHasNoRuntime_whenConvert_thenReturnResultWithoutRuntime() {
        when(appLicense.getRuntime()).thenReturn(null);
        final ProductData productData = createProductData(createImageDatas(ImageDataType.PRIMARY, LOGO_URL, SMALL_LOGO_URL));
        final OrderEntryData orderEntryData = new OrderEntryData()
            .withProduct(productData)
            .withEntryNumber(0)
            .withQuantity(1L)
            .withBasePrice(itemPriceData)
            .withTotalPrice(totalPriceData);

        final CartItemData actualResult = cartItemDataConverter.convert(orderEntryData);
        assertThat(actualResult.getRuntime()).isNull();
    }

    private void assertConversion(ProductData productData) {
        OrderEntryData orderEntryData = new OrderEntryData()
            .withProduct(productData)
            .withEntryNumber(0)
            .withQuantity(2l)
            .withBasePrice(itemPriceData)
            .withTotalPrice(totalPriceData)
            .withFuturePrices(futurePrices);

        CartItemData actualResult = cartItemDataConverter.convert(orderEntryData);
        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    private ProductData createProductData(List<ImageData> imageDatas) {
        return new ProductData()
            .withCompany(new IotCompanyData().withName(TEST_COMPANY))
            .withImages(imageDatas)
            .withCode(TEST_LICENSE);
    }

    private ProductData createProductDataWithoutCompanyData(List<ImageData> imageDatas) {
        return new ProductData()
            .withImages(imageDatas)
            .withCode(TEST_LICENSE);
    }

    private CartItemData prepareExpectedResult(String logoUrl, String smallLogoUrl, String licenseName, String companyName) {
        return new CartItemData()
            .withAppCode(APP_CODE)
            .withLicenseName(licenseName)
            .withCompanyName(companyName)
            .withProductCode(TEST_LICENSE)
            .withLogoUrl(logoUrl)
            .withSmallLogoUrl(smallLogoUrl)
            .withItemPrice(convertedItemPriceData)
            .withTotalPrice(convertedTotalPriceData)
            .withLicenseType(appLicense.getLicenseType().toString())
            .withQuantity(2l)
            .withScalePrices(Collections.emptyList())
            .withProductFuturePrices(Collections.emptySet())
            .withRuntime(runtimeData)
            .withEntryNumber(0);
    }

    private List<ImageData> createImageDatas(ImageDataType imageDataType, String logoUrl, String smallLogoUrl) {
        return List.of(
            new ImageData()
                .withFormat(MediaFormat.MEDIUM_ICON.getValue())
                .withImageType(imageDataType)
                .withUrl(logoUrl),
            new ImageData()
                .withFormat(MediaFormat.SMALL_ICON.getValue())
                .withImageType(imageDataType)
                .withUrl(smallLogoUrl)
        );
    }
}
