package com.sast.cis.core.converter;

import com.sast.cis.core.data.CartItemData;
import com.sast.cis.core.data.CartPageData;
import com.sast.cis.core.service.AbstractOrderHashService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.order.data.CartData;
import de.hybris.platform.commercefacades.order.data.OrderEntryData;
import de.hybris.platform.commercefacades.product.data.PriceData;
import de.hybris.platform.core.model.order.CartModel;
import de.hybris.platform.order.CartService;
import generated.de.hybris.platform.core.model.order.CartBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.List;

import static java.util.List.of;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CartPageDataConverterUnitTest {

    private static  final String CART_CODE = "cartCode";
    private static  final String CART_HASH_CODE = "8934941237";
    private static  final String COMPANY_NAME = "Test company name";
    private static  final String PRODUCT_ONE_NAME = "Test product one name";
    private static  final String PRODUCT_TWO_NAME = "Test product two name";
    private static  final String CURRENCY_ISO = "EUR";
    private static  final BigDecimal TOTAL_PRICE = BigDecimal.TEN;
    private static  final BigDecimal TOTAL_TAX = BigDecimal.ONE;


    @Mock
    private AbstractOrderHashService abstractOrderHashService;

    @Mock
    private CartService cartService;

    @Mock
    private CartItemDataConverter cartItemDataConverter;

    @Mock
    private PriceDataConverter priceDataConverter;

    @InjectMocks
    private CartPageDataConverter converter;


    @Test
    public void convert_CartData_properlyConverted() {
        PriceData totalPriceData = createDefaultPriceData(TOTAL_PRICE);
        PriceData totalTaxData = createDefaultPriceData(TOTAL_TAX);
        PriceData totalPriceWithTaxData = createDefaultPriceData(TOTAL_PRICE.add(TOTAL_TAX));

        OrderEntryData orderEntryOne = new OrderEntryData();
        OrderEntryData orderEntryTwo = new OrderEntryData();

        CartData cartData = createDefaultCartData(totalPriceData, totalTaxData, totalPriceWithTaxData, of(orderEntryOne, orderEntryTwo));

        CartItemData cartItemDataOne = createDefaultCartItemData(PRODUCT_ONE_NAME);
        CartItemData cartItemDataTwo = createDefaultCartItemData(PRODUCT_TWO_NAME);

        com.sast.cis.core.data.PriceData convertedPriceData = createDefaultCorePriceData(TOTAL_PRICE);
        com.sast.cis.core.data.PriceData convertedTaxData = createDefaultCorePriceData(TOTAL_TAX);
        com.sast.cis.core.data.PriceData convertedPriceWithTaxData = createDefaultCorePriceData(TOTAL_PRICE.add(TOTAL_TAX));

        CartModel cart = CartBuilder.generate().buildMockInstance();

        when(cartItemDataConverter.convert(orderEntryOne)).thenReturn(cartItemDataOne);
        when(cartItemDataConverter.convert(orderEntryTwo)).thenReturn(cartItemDataTwo);
        when(priceDataConverter.toPriceData(totalPriceData)).thenReturn(convertedPriceData);
        when(priceDataConverter.toPriceData(totalTaxData)).thenReturn(convertedTaxData);
        when(priceDataConverter.toPriceData(totalPriceWithTaxData)).thenReturn(convertedPriceWithTaxData);
        when(cartService.hasSessionCart()).thenReturn(true);
        when(cartService.getSessionCart()).thenReturn(cart);
        when(abstractOrderHashService.calculateHash(cart)).thenReturn(CART_HASH_CODE);

        CartPageData actualResult = converter.convert(cartData);

        CartPageData expectedResult = createExpectedResult(convertedPriceData, convertedTaxData, convertedPriceWithTaxData,
            of(cartItemDataOne, cartItemDataTwo), COMPANY_NAME, CART_HASH_CODE);

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void convert_CartDataWithoutTotalTax_returnsEmptyObject() {
        PriceData totalTaxData = createDefaultPriceData(TOTAL_TAX);

        CartData cartData = new CartData();
        cartData.setTotalPrice(null);
        cartData.setTotalTax(totalTaxData);

        CartPageData actualResult = converter.convert(cartData);

        CartPageData expectedResult = new CartPageData();

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void convert_CartDataWithoutTotalPrice_returnsEmptyObject() {
        PriceData totalPriceData = createDefaultPriceData(TOTAL_PRICE);

        CartData cartData = new CartData();
        cartData.setTotalPrice(totalPriceData);
        cartData.setTotalTax(null);

        CartPageData actualResult = converter.convert(cartData);

        CartPageData expectedResult = new CartPageData();

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void convert_CartDataWithoutItems_properlyConverted() {
        PriceData totalPriceData = createDefaultPriceData(BigDecimal.ZERO);
        PriceData totalTaxData = createDefaultPriceData(BigDecimal.ZERO);
        PriceData totalPriceWithTaxData = createDefaultPriceData(BigDecimal.ZERO);

        CartData cartData = createDefaultCartData(totalPriceData, totalTaxData, totalPriceWithTaxData, of());

        com.sast.cis.core.data.PriceData convertedPriceData = createDefaultCorePriceData(BigDecimal.ZERO);
        com.sast.cis.core.data.PriceData convertedTaxData = createDefaultCorePriceData(BigDecimal.ZERO);
        com.sast.cis.core.data.PriceData convertedPriceWithTaxData = createDefaultCorePriceData(BigDecimal.ZERO);

        CartModel cart = CartBuilder.generate().buildMockInstance();

        when(priceDataConverter.toPriceData(totalPriceData)).thenReturn(convertedPriceData);
        when(priceDataConverter.toPriceData(totalTaxData)).thenReturn(convertedTaxData);
        when(priceDataConverter.toPriceData(totalPriceWithTaxData)).thenReturn(convertedPriceWithTaxData);
        when(cartService.hasSessionCart()).thenReturn(true);
        when(cartService.getSessionCart()).thenReturn(cart);
        when(abstractOrderHashService.calculateHash(cart)).thenReturn(CART_HASH_CODE);

        CartPageData actualResult = converter.convert(cartData);

        CartPageData expectedResult = createExpectedResult(convertedPriceData, convertedTaxData, convertedPriceWithTaxData,
            of(), "", CART_HASH_CODE);

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void convert_CartDataAndThereIsNoSessionCart_properlyConvertedAndHashCodeIsNotProvided() {
        PriceData totalPriceData = createDefaultPriceData(BigDecimal.ZERO);
        PriceData totalTaxData = createDefaultPriceData(BigDecimal.ZERO);
        PriceData totalPriceWithTaxData = new PriceData().withFormattedValue(BigDecimal.ZERO.toString());

        CartData cartData = createDefaultCartData(totalPriceData, totalTaxData, totalPriceWithTaxData, of());

        com.sast.cis.core.data.PriceData convertedPriceData = createDefaultCorePriceData(BigDecimal.ZERO);
        com.sast.cis.core.data.PriceData convertedTaxData = createDefaultCorePriceData(BigDecimal.ZERO);
        com.sast.cis.core.data.PriceData convertedPriceWithTaxData = createDefaultCorePriceData(BigDecimal.ZERO);

        CartModel cart = CartBuilder.generate().buildMockInstance();

        when(priceDataConverter.toPriceData(totalPriceData)).thenReturn(convertedPriceData);
        when(priceDataConverter.toPriceData(totalTaxData)).thenReturn(convertedTaxData);
        when(priceDataConverter.toPriceData(totalPriceWithTaxData)).thenReturn(convertedPriceWithTaxData);
        when(cartService.hasSessionCart()).thenReturn(false);
        when(cartService.getSessionCart()).thenReturn(cart);
        when(abstractOrderHashService.calculateHash(cart)).thenReturn(CART_HASH_CODE);

        CartPageData actualResult = converter.convert(cartData);

        CartPageData expectedResult = createExpectedResult(convertedPriceData, convertedTaxData, convertedPriceWithTaxData,
            of(), "", null);

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    private CartData createDefaultCartData(PriceData totalPrice, PriceData totalTax, PriceData totalPriceWithTax, List<OrderEntryData> items) {
        CartData cartData = new CartData();
        cartData.setCode(CART_CODE);
        cartData.setTotalPrice(totalPrice);
        cartData.setTotalTax(totalTax);
        cartData.setTotalPriceWithTax(totalPriceWithTax);
        cartData.setOriginalTotalPrice(totalPriceWithTax);
        cartData.setEntries(items);
        return cartData;
    }

    private PriceData createDefaultPriceData(BigDecimal value) {
        return new PriceData().withCurrencyIso(CURRENCY_ISO).withFormattedValue(value.toString());
    }

    private CartItemData createDefaultCartItemData(String value) {
        return new CartItemData().withProductName(value).withCompanyName(COMPANY_NAME);
    }

    private com.sast.cis.core.data.PriceData createDefaultCorePriceData(BigDecimal totalPrice) {
        return new com.sast.cis.core.data.PriceData()
            .withSymbol(CURRENCY_ISO).withValue(totalPrice.toString());
    }

    private CartPageData createExpectedResult(com.sast.cis.core.data.PriceData convertedPriceData,
        com.sast.cis.core.data.PriceData convertedTaxData, com.sast.cis.core.data.PriceData convertedPriceWithTaxData,
        List<CartItemData> cartItems, String companyName, String cartHashCode) {
        return new CartPageData()
            .withTotalPrice(convertedPriceData)
            .withTotalTax(convertedTaxData)
            .withTotalPriceWithTax(convertedPriceWithTaxData)
            .withTotalPriceWithoutDiscount(convertedPriceWithTaxData)
            .withCartItems(cartItems)
            .withCartCode(CART_CODE)
            .withDeveloperCompanyName(companyName)
            .withCartHash(cartHashCode);
    }
}
