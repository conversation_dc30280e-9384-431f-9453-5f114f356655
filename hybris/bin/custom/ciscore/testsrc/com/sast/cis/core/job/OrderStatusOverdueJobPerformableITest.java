package com.sast.cis.core.job;

import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.service.order.CisOrderService;
import com.sast.cis.test.utils.SampleDataCreator;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.enums.PaymentStatus;
import de.hybris.platform.core.model.order.OrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.cronjob.enums.CronJobResult;
import de.hybris.platform.cronjob.enums.CronJobStatus;
import de.hybris.platform.cronjob.model.CronJobModel;
import de.hybris.platform.payment.enums.PaymentTransactionType;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.cronjob.CronJobService;
import de.hybris.platform.servicelayer.cronjob.PerformResult;
import de.hybris.platform.servicelayer.model.ModelService;
import generated.de.hybris.platform.cronjob.model.CronJobBuilder;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;

import java.util.Date;
import java.util.UUID;

import static com.sast.cis.core.dao.CatalogVersion.ONLINE;
import static com.sast.cis.core.enums.LicenseType.*;
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.APPROVED;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class OrderStatusOverdueJobPerformableITest extends ServicelayerTransactionalTest {

    private static final String PURCHASED_APP = "PurchasedAppCode";
    private static final String PURCHASED_APP_PACKAGE = "app.purchased";
    private static final String PURCHASED_APP_VERSION = "AppVersionPurchased";
    private static final String EXTERNAL_ORDER_ID_3 = "external-order-3";
    private static final Long QUANTITY = 1L;

    private final SampleDataCreator sampleDataCreator = new SampleDataCreator();

    @Resource(name = "orderStatusOverdueJobPerformable")
    private OrderStatusOverdueJobPerformable performable;

    @Resource
    private ModelService modelService;

    @Resource
    private CronJobService cronJobService;

    @Resource
    private CisOrderService cisOrderService;

    private AppModel app;

    private AppLicenseModel appFullLicense;

    private AppLicenseModel appSubscriptionLicense;

    private AppLicenseModel appTrialLicense;

    @Before
    public void setup() {
        app = sampleDataCreator.createApp(PURCHASED_APP, PURCHASED_APP_PACKAGE, ONLINE);
        appFullLicense = sampleDataCreator.createAppLicense(PURCHASED_APP_VERSION + "_FULL", app, APPROVED, FULL);
        appSubscriptionLicense = sampleDataCreator.createAppLicense(PURCHASED_APP_VERSION + "_SUBSCRIPTION", app, APPROVED, SUBSCRIPTION);
        appTrialLicense = sampleDataCreator.createAppLicense(PURCHASED_APP_VERSION + "_evaluation", app, APPROVED, EVALUATION);
    }

    @Test
    public void orderStatusOverdueJob_updatesStatusesOverdueOrders() {
        final OrderModel overdueOrder = sampleDataCreator.createOrder(OrderStatus.COMPLETED);
        overdueOrder.setCode(UUID.randomUUID().toString());
        overdueOrder.setExternalOrderId(EXTERNAL_ORDER_ID_3);
        overdueOrder.setPaymentStatus(PaymentStatus.PENDING);
        overdueOrder.setDate(DateUtils.addDays(new Date(), -cisOrderService.getMaxOverdueDays() - 1));
        final OrderEntryModel orderEntry = sampleDataCreator.createOrderEntry(overdueOrder, appFullLicense, QUANTITY);
        sampleDataCreator.createPaymentTransaction(PaymentTransactionType.AUTHORIZATION, "test", overdueOrder);

        CronJobModel cronJob = CronJobBuilder.generate()
            .withCode("TestGetInstallsPerAppCronJob")
            .withJob(cronJobService.getJob("orderStatusOverdueJob"))
            .buildIntegrationInstance();
        modelService.save(cronJob);
        PerformResult result = performable.perform(cronJob);

        assertThat(result).extracting(PerformResult::getStatus).isEqualTo(CronJobStatus.FINISHED);
        assertThat(result).extracting(PerformResult::getResult).isEqualTo(CronJobResult.SUCCESS);

        modelService.refresh(overdueOrder);

        assertThat(overdueOrder.getPaymentStatus()).isEqualTo(PaymentStatus.OVERDUE);
    }

    @Test
    public void givenPaidRecurringOrder_whenJobExecuted_thenDoNotUpdateToOverdue() {
        final OrderModel paidRecurringOrder = createOrderWithLicenseAndPaymentStatus(appSubscriptionLicense, PaymentStatus.PAID_RECURRING);

        final CronJobModel cronJob = CronJobBuilder.generate()
            .withCode("TestGetInstallsPerAppCronJob")
            .withJob(cronJobService.getJob("orderStatusOverdueJob"))
            .buildIntegrationInstance();
        modelService.save(cronJob);
        final PerformResult result = performable.perform(cronJob);

        assertThat(result).extracting(PerformResult::getStatus).isEqualTo(CronJobStatus.FINISHED);
        assertThat(result).extracting(PerformResult::getResult).isEqualTo(CronJobResult.SUCCESS);
        modelService.refresh(paidRecurringOrder);
        assertThat(paidRecurringOrder.getPaymentStatus()).isEqualTo(PaymentStatus.PAID_RECURRING);
    }

    @Test
    public void givenOrderWithExemptStatus_whenJobExecuted_thenDoNotUpdateToOverdue() {
        final OrderModel exemptOrder = createOrderWithLicenseAndPaymentStatus(appTrialLicense, PaymentStatus.EXEMPT);

        final CronJobModel cronJob = CronJobBuilder.generate()
            .withCode("TestGetInstallsPerAppCronJob")
            .withJob(cronJobService.getJob("orderStatusOverdueJob"))
            .buildIntegrationInstance();
        modelService.save(cronJob);
        final PerformResult result = performable.perform(cronJob);

        assertThat(result).extracting(PerformResult::getStatus).isEqualTo(CronJobStatus.FINISHED);
        assertThat(result).extracting(PerformResult::getResult).isEqualTo(CronJobResult.SUCCESS);
        modelService.refresh(exemptOrder);
        assertThat(exemptOrder.getPaymentStatus()).isEqualTo(PaymentStatus.EXEMPT);
    }

    @Test
    public void givenOrderWithRefundedStatus_whenJobExecuted_thenDoNotUpdateToOverdue() {
        final OrderModel refundedOrder = createOrderWithLicenseAndPaymentStatus(appFullLicense, PaymentStatus.REFUNDED);
        final OrderModel partiallyRefundedOrder = createOrderWithLicenseAndPaymentStatus(appFullLicense, PaymentStatus.PARTIALLY_REFUNDED);

        final CronJobModel cronJob = CronJobBuilder.generate()
            .withCode("TestGetInstallsPerAppCronJob")
            .withJob(cronJobService.getJob("orderStatusOverdueJob"))
            .buildIntegrationInstance();
        modelService.save(cronJob);
        final PerformResult result = performable.perform(cronJob);

        assertThat(result).extracting(PerformResult::getStatus).isEqualTo(CronJobStatus.FINISHED);
        assertThat(result).extracting(PerformResult::getResult).isEqualTo(CronJobResult.SUCCESS);
        modelService.refresh(refundedOrder);
        assertThat(refundedOrder.getPaymentStatus()).isEqualTo(PaymentStatus.REFUNDED);
        modelService.refresh(partiallyRefundedOrder);
        assertThat(partiallyRefundedOrder.getPaymentStatus()).isEqualTo(PaymentStatus.PARTIALLY_REFUNDED);
    }

    private OrderModel createOrderWithLicenseAndPaymentStatus(final AppLicenseModel appLicense, final PaymentStatus paymentStatus) {
        final OrderModel order = sampleDataCreator.createOrder(OrderStatus.COMPLETED);
        order.setCode(UUID.randomUUID().toString());
        order.setExternalOrderId(UUID.randomUUID().toString());
        order.setPaymentStatus(paymentStatus);
        order.setDate(DateUtils.addDays(new Date(), -cisOrderService.getMaxOverdueDays() - 1));
        sampleDataCreator.createOrderEntry(order, appLicense, QUANTITY);
        sampleDataCreator.createPaymentTransaction(PaymentTransactionType.AUTHORIZATION, UUID.randomUUID().toString(), order);
        return order;
    }
}
