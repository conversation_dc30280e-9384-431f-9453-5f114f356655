package com.sast.cis.core.country.sync.service;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.sast.cis.core.constants.BaseStoreEnum;
import com.sast.cis.core.country.sync.handler.StoreLanguageSyncHandler;
import com.sast.cis.core.data.MasterCountryData;
import com.sast.cis.core.model.CountryStoreConfigurationModel;
import com.sast.cis.core.service.country.CountryStoreConfigurationService;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.c2l.LanguageModel;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.store.BaseStoreModel;
import de.hybris.platform.store.services.BaseStoreService;
import lombok.SneakyThrows;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class StoreLanguageSyncHandlerIntegrationTest extends ServicelayerTransactionalTest {
    @Resource
    private StoreLanguageSyncHandler storeLanguageSyncHandler;
    @Resource
    private ModelService modelService;

    @Resource
    private CommonI18NService commonI18NService;
    @Resource
    private CountryStoreConfigurationService countryStoreConfigurationService;
    @Resource
    private BaseStoreService baseStoreService;


    private static String getContent(String testFilename) throws IOException {
        String responseBody;
        InputStream inputStream = new ClassPathResource(testFilename,
            MasterCountryDataService.class.getClassLoader()).getInputStream();
        try (BufferedReader reader = new BufferedReader(
            new InputStreamReader(inputStream))) {
            responseBody = reader.lines()
                .collect(Collectors.joining("\n"));
        }
        return responseBody;
    }

    @Test
    @SneakyThrows
    public void givenCountryHasChangedSupportedLanguages_whenSync_thenExistingEntryUpdated() {
        String testFilename = "test/countries_v3_with_tenantConfigurations.json";
        String responseBody = getContent(testFilename);
        Gson gson = new Gson();
        List<MasterCountryData> masterCountryDataResponse = gson.fromJson(responseBody, new TypeToken<List<MasterCountryData>>() {
        }.getType());

        Map<String, MasterCountryData> masterCountryData = masterCountryDataResponse.stream()
            .collect(Collectors.toMap(MasterCountryData::getIsoCode,
                Function.identity()));
        final String isoCode = "CH";
        final CountryModel country = commonI18NService.getCountry(isoCode);
        final BaseStoreModel baseStore = baseStoreService.getBaseStoreForUid(BaseStoreEnum.AA.getBaseStoreUid());
        countryStoreConfigurationService.getCountryStoreConfiguration(country, baseStore).ifPresent(configurationModel -> {
                configurationModel.setLanguages(Set.of(commonI18NService.getLanguage("en")));
                configurationModel.setDefaultLanguage(null);
                modelService.save(configurationModel);
            }
        );

        storeLanguageSyncHandler.syncCountryTenantConfigurations(masterCountryData);

        Optional<CountryStoreConfigurationModel> actualConfiguration = countryStoreConfigurationService.getCountryStoreConfiguration(
            country, baseStore);
        assertThat(actualConfiguration).isPresent();
        assertThat(actualConfiguration.get().getStorefrontEnabled()).isTrue();
        assertThat(actualConfiguration.get().getLanguages()).extracting(LanguageModel::getIsocode).containsExactlyInAnyOrder("de", "en");
        assertThat(actualConfiguration.get().getDefaultLanguage()).extracting(LanguageModel::getIsocode).isEqualTo("de");

    }

    @Test
    @SneakyThrows
    public void givenCountryHasChangedImportedCompaniesCanBuyField_whenSync_thenExistingEntryUpdated() {
        String testFilename = "test/countries_v3_with_tenantConfigurations.json";
        String responseBody = getContent(testFilename);
        Gson gson = new Gson();
        List<MasterCountryData> masterCountryDataResponse = gson.fromJson(responseBody, new TypeToken<List<MasterCountryData>>() {
        }.getType());

        Map<String, MasterCountryData> masterCountryData = masterCountryDataResponse.stream()
            .collect(Collectors.toMap(MasterCountryData::getIsoCode,
                Function.identity()));
        final String isoCode = "AT";
        final CountryModel country = commonI18NService.getCountry(isoCode);
        final BaseStoreModel baseStore = baseStoreService.getBaseStoreForUid(BaseStoreEnum.AA.getBaseStoreUid());
        countryStoreConfigurationService.getCountryStoreConfiguration(country, baseStore).ifPresent(configurationModel -> {
                configurationModel.setImportedCompaniesCanBuy(false);
                modelService.save(configurationModel);
            }
        );

        storeLanguageSyncHandler.syncCountryTenantConfigurations(masterCountryData);

        Optional<CountryStoreConfigurationModel> actualConfiguration = countryStoreConfigurationService.getCountryStoreConfiguration(
            country, baseStore);
        assertThat(actualConfiguration).isPresent();
        assertThat(actualConfiguration.get().getImportedCompaniesCanBuy()).isTrue();

    }
}
