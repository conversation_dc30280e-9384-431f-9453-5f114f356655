package com.sast.cis.core.facade;

import com.sast.cis.core.constants.devcon.DevconErrorCode;
import com.sast.cis.core.data.AppVersionMetaData;
import com.sast.cis.core.dto.DevconErrorMessage;
import com.sast.cis.core.exceptions.devcon.DevconErrorException;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.AppVersionModel;
import com.sast.cis.core.model.ProductContainerModel;
import com.sast.cis.core.service.*;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.catalog.model.CatalogModel;
import de.hybris.platform.catalog.model.CatalogVersionModel;
import de.hybris.platform.servicelayer.model.ModelService;
import generated.com.sast.cis.core.model.AppBuilder;
import generated.com.sast.cis.core.model.AppVersionBuilder;
import generated.com.sast.cis.core.model.ProductContainerBuilder;
import generated.de.hybris.platform.catalog.model.CatalogBuilder;
import generated.de.hybris.platform.catalog.model.CatalogVersionBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class AppVersionFacadeUnitTest {

    private static final String CHANGELOG = "blu";
    public static final String CATALOG_ID = "catalogId";

    @InjectMocks
    private AppVersionFacade appVersionFacade = new AppVersionFacade();

    @Mock
    private ProductContainerService productContainerService;

    @Mock
    private ErrorMessageService errorMessageService;

    @Mock
    private AppVersionService appVersionService;

    @Mock
    private AppSyncService appSyncService;

    @Mock
    private ModelService modelService;

    @Mock
    private TransactionTemplate transactionTemplate;

    @Mock
    private HtmlSanitizingService htmlSanitizingService;

    private AppVersionModel appVersion;
    private ProductContainerModel productContainer;
    private CatalogVersionModel catalogVersion;

    @Before
    public void setUp() throws Exception {
        CatalogModel catalog = CatalogBuilder.generate().withId(CATALOG_ID).buildMockInstance();
        catalogVersion = CatalogVersionBuilder.generate().withCatalog(catalog).buildMockInstance();
        AppModel app = AppBuilder.generate().withCode("1234").withCatalogVersion(catalogVersion).buildMockInstance();
        productContainer = ProductContainerBuilder.generate().withApp(app).buildMockInstance();
        when(productContainerService.getProductContainerForCode(any())).thenReturn(Optional.of(productContainer));
        appVersion = AppVersionBuilder.generate().withApp(app).buildMockInstance();
        when(appVersionService.getVersionForCodeAndCatalogVersion(any(), any())).thenReturn(Optional.of(appVersion));
        when(transactionTemplate.execute(any())).then(i -> i.getArgument(0, TransactionCallback.class).doInTransaction(null));
    }

    @Test
    public void happyPath_sanitizesHtmlSavesAppVersionAndSyncsIt() {
        AppVersionMetaData appVersionMetaData = new AppVersionMetaData().withChangelogByIsocode(Map.of("en", CHANGELOG));
        appVersionFacade.updateAppVersion(appVersionMetaData);

        verify(modelService).save(appVersion);
        verify(htmlSanitizingService).sanitizeInput(eq(CHANGELOG));
        verify(appSyncService).performSync(anyList(), eq(CATALOG_ID));
    }

    @Test
    public void missingContainer_throwsException() {
        when(productContainerService.getProductContainerForCode(any())).thenReturn(Optional.empty());
        DevconErrorMessage devconErrorMessage = new DevconErrorMessage().withCode(DevconErrorCode.BACKEND_ERROR);
        when(errorMessageService.createErrorMessage(DevconErrorCode.BACKEND_ERROR)).thenReturn(devconErrorMessage);
        assertThatThrownBy(() -> appVersionFacade.updateAppVersion(new AppVersionMetaData())).isInstanceOf(DevconErrorException.class)
            .extracting(ex -> ((DevconErrorException)ex).getDevconErrorMessage()).isEqualTo(devconErrorMessage);
    }

    @Test
    public void missingAppVersion_throwsException() {
        when(appVersionService.getVersionForCodeAndCatalogVersion(any(), any())).thenReturn(Optional.empty());

        DevconErrorMessage devconErrorMessage = new DevconErrorMessage().withCode(DevconErrorCode.APPVERSION_NOT_FOUND);
        when(errorMessageService.createErrorMessage(DevconErrorCode.APPVERSION_NOT_FOUND)).thenReturn(devconErrorMessage);
        assertThatThrownBy(() -> appVersionFacade.updateAppVersion(new AppVersionMetaData())).isInstanceOf(DevconErrorException.class)
            .extracting(ex -> ((DevconErrorException)ex).getDevconErrorMessage()).isEqualTo(devconErrorMessage);
    }

    @Test
    public void missingApp_throwsException() {
        when(productContainer.getApp()).thenReturn(null);

        DevconErrorMessage devconErrorMessage = new DevconErrorMessage().withCode(DevconErrorCode.BACKEND_ERROR);
        when(errorMessageService.createErrorMessage(DevconErrorCode.BACKEND_ERROR)).thenReturn(devconErrorMessage);
        assertThatThrownBy(() -> appVersionFacade.updateAppVersion(new AppVersionMetaData())).isInstanceOf(DevconErrorException.class)
            .extracting(ex -> ((DevconErrorException)ex).getDevconErrorMessage()).isEqualTo(devconErrorMessage);
    }

}
