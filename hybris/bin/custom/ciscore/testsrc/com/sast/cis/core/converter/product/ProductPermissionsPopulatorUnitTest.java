package com.sast.cis.core.converter.product;

import com.sast.cis.core.data.PermissionData;
import com.sast.cis.core.model.ApkMediaModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.AppVersionModel;
import com.sast.cis.core.model.PermissionModel;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import generated.com.sast.cis.core.model.ApkMediaBuilder;
import generated.com.sast.cis.core.model.AppBuilder;
import generated.com.sast.cis.core.model.AppVersionBuilder;
import generated.com.sast.cis.core.model.PermissionBuilder;
import generated.de.hybris.platform.core.model.product.ProductBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class ProductPermissionsPopulatorUnitTest {

    @Mock
    private Converter<PermissionModel, PermissionData> permissionConverter;

    @InjectMocks
    private ProductPermissionsPopulator populator;

    @Test
    public void populate_WithApp_properlyPopulated() {
        PermissionModel permission = createPermission();
        ApkMediaModel apk = createApk(List.of(permission));
        AppVersionModel appVersion = createAppVersion(apk);
        AppModel app = createApp(appVersion);

        PermissionData permissionData = new PermissionData();
        when(permissionConverter.convert(permission)).thenReturn(permissionData);

        ProductData actualResult = new ProductData();
        populator.populate(app, actualResult);

        ProductData expectedResult = new ProductData().withPermissions(List.of(permissionData));

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_WithApp_properlyPopulated_noRepeat() {
        PermissionModel permission1 = createPermission();
        PermissionModel permission2 = createPermission();
        PermissionModel permission3 = createPermission();
        ApkMediaModel apk = createApk(List.of(permission1, permission2, permission3));
        AppVersionModel appVersion = createAppVersion(apk);
        AppModel app = createApp(appVersion);

        PermissionData permissionData1 = new PermissionData();
        permissionData1.setName("abc");
        PermissionData permissionData2 = new PermissionData();
        permissionData2.setName("abc");
        PermissionData permissionData3 = new PermissionData();
        permissionData3.setName("abc");
        when(permissionConverter.convert(permission1)).thenReturn(permissionData1);
        when(permissionConverter.convert(permission2)).thenReturn(permissionData2);
        when(permissionConverter.convert(permission3)).thenReturn(permissionData3);

        ProductData actualResult = new ProductData();
        populator.populate(app, actualResult);

        assertThat(actualResult.getPermissions()).hasSize(1);
    }

    @Test
    public void populate_WithAppWithoutPermission_properlyPopulatedWithoutPermissions() {
        ApkMediaModel apk = createApk(List.of());
        AppVersionModel appVersion = createAppVersion(apk);
        AppModel app = createApp(appVersion);

        ProductData actualResult = new ProductData();
        populator.populate(app, actualResult);

        ProductData expectedResult = new ProductData().withPermissions(List.of());

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_WithUnknownProduct_notPopulated() {
        ProductModel product = ProductBuilder.generate().buildMockInstance();

        ProductData actualResult = new ProductData();
        populator.populate(product, actualResult);

        ProductData expectedResult = new ProductData();

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }


    private AppModel createApp(AppVersionModel appVersion) {
        return AppBuilder.generate().withLatestVersion(appVersion).buildMockInstance();
    }

    private AppVersionModel createAppVersion(ApkMediaModel apk) {
        return AppVersionBuilder.generate().withApk(apk).buildMockInstance();
    }

    private ApkMediaModel createApk(List<PermissionModel> permissions) {
        return ApkMediaBuilder.generate().withPermissions(permissions).buildMockInstance();
    }

    private PermissionModel createPermission() {
        return PermissionBuilder.generate().buildMockInstance();
    }

}
