package com.sast.cis.core.job;

import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.cronjob.enums.CronJobResult;
import de.hybris.platform.cronjob.enums.CronJobStatus;
import de.hybris.platform.cronjob.enums.JobLogLevel;
import de.hybris.platform.cronjob.model.CronJobModel;
import de.hybris.platform.cronjob.model.JobLogModel;
import de.hybris.platform.cronjob.model.LogFileModel;
import de.hybris.platform.jobs.GenericMaintenanceJobPerformable;
import de.hybris.platform.processengine.enums.BooleanOperator;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.cronjob.CronJobService;
import de.hybris.platform.servicelayer.cronjob.PerformResult;
import de.hybris.platform.servicelayer.model.ModelService;
import generated.de.hybris.platform.cronjob.model.CronJobBuilder;
import generated.de.hybris.platform.cronjob.model.JobLogBuilder;
import generated.de.hybris.platform.cronjob.model.LogFileBuilder;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;

import javax.annotation.Resource;

import java.util.Date;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class JobLogsLogFilesCleanupCronJobITest extends ServicelayerTransactionalTest {

    @Resource
    private GenericMaintenanceJobPerformable cleanUpLogsJobPerformable;

    @Resource
    private ModelService modelService;

    @Resource
    private CronJobService cronJobService;

    @Test
    public void testJobLogsLogFilesCleanupCronJob() {
        final int logFilesDaysOld = 10;
        final CronJobModel testJobWithLogs = CronJobBuilder.generate()
            .withCode("testJobWithLogs")
            .withJob(cronJobService.getJob("invoiceStatusOverdueJob"))
            .withFilesCount(0)
            .withFilesOperator(BooleanOperator.AND)
            .withFilesDaysOld(logFilesDaysOld)
            .withLogsCount(0)
            .withLogsOperator(BooleanOperator.AND)
            .withLogsDaysOld(logFilesDaysOld)
            .buildIntegrationInstance();
        modelService.save(testJobWithLogs);

        final Date logsToDeleteCreationDate = DateUtils.addDays(new Date(), -logFilesDaysOld - 1);
        final JobLogModel jobLogToCleanup = createTestJobLogs(testJobWithLogs, logsToDeleteCreationDate);
        final LogFileModel logFileToCleanup = createTestLogFileMode(testJobWithLogs, logsToDeleteCreationDate);

        final Date logsToKeepCreationDate = DateUtils.addDays(new Date(), -logFilesDaysOld + 5);
        final JobLogModel jobLogToKeep = createTestJobLogs(testJobWithLogs, logsToKeepCreationDate);
        final LogFileModel logFileToKeep = createTestLogFileMode(testJobWithLogs, logsToKeepCreationDate);

        final CronJobModel cronJob = CronJobBuilder.generate()
            .withCode("testJobLogsLogFilesCleanupCronJob")
            .withJob(cronJobService.getJob("cleanUpLogsJobPerformable"))
            .buildIntegrationInstance();
        modelService.save(cronJob);

        final PerformResult result = cleanUpLogsJobPerformable.perform(cronJob);
        assertThat(result).extracting(PerformResult::getStatus).isEqualTo(CronJobStatus.FINISHED);
        assertThat(result).extracting(PerformResult::getResult).isEqualTo(CronJobResult.SUCCESS);

        assertThat(modelService.isRemoved(jobLogToCleanup)).isTrue();
        assertThat(modelService.isRemoved(logFileToCleanup)).isTrue();

        assertThat(modelService.isRemoved(jobLogToKeep)).isFalse();
        assertThat(modelService.isRemoved(logFileToKeep)).isFalse();
    }

    private JobLogModel createTestJobLogs(final CronJobModel ownerCronJob, final Date jobLogCreationTime) {
        final JobLogModel jobLog = JobLogBuilder.generate()
            .withCreationtime(jobLogCreationTime)
            .withCronJob(ownerCronJob)
            .withLevel(JobLogLevel.INFO)
            .buildIntegrationInstance();
        modelService.save(jobLog);
        return jobLog;
    }

    private LogFileModel createTestLogFileMode(final CronJobModel ownerCronJob, final Date logFileCreationTime) {
        final LogFileModel logFile = LogFileBuilder.generate()
            .withCode(UUID.randomUUID().toString())
            .withCreationtime(logFileCreationTime)
            .withOwner(ownerCronJob)
            .buildIntegrationInstance();
        modelService.save(logFile);
        return logFile;
    }
}
