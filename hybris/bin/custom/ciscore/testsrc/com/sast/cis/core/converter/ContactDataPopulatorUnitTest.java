package com.sast.cis.core.converter;

import com.sast.cis.core.converter.product.ContactDataPopulator;
import com.sast.cis.core.data.AppContactData;
import com.sast.cis.core.data.EulaData;
import com.sast.cis.core.data.IotCompanyData;
import com.sast.cis.core.enums.EulaType;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.EulaModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.tngtech.java.junit.dataprovider.DataProvider;
import com.tngtech.java.junit.dataprovider.DataProviderRunner;
import com.tngtech.java.junit.dataprovider.UseDataProvider;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import generated.com.sast.cis.core.model.AppBuilder;
import generated.com.sast.cis.core.model.EulaBuilder;
import generated.com.sast.cis.core.model.IoTCompanyBuilder;
import generated.de.hybris.platform.core.model.product.ProductBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(DataProviderRunner.class)
public class ContactDataPopulatorUnitTest {

    private static final String NAME = "companyName";
    private static final String FRIENDLY_NAME = "companyFriendlyName";
    private static final String SUPPORT_PHONE_NUMBER = "supportPhoneNumber";
    private static final String PRIVACY_POLICY_URL = "privacyPolicyUrl";
    private static final String APP_EMAIL_ADDRESS = "appEmailAddress";
    private static final String APP_URL = "supportPageUrl";
    private static final String HTTP = "https://";
    private static final EulaModel STANDARD_EULA = EulaBuilder.generate().withType(EulaType.STANDARD).buildMockInstance();
    private static final EulaData STANDARD_EULA_DATA = new EulaData().withType(EulaType.STANDARD);
    private static final EulaModel CUSTOM_EULA = EulaBuilder.generate().withType(EulaType.CUSTOM).withCustomUrl("terms.com")
        .buildMockInstance();
    private static final EulaData CUSTOM_EULA_DATA = new EulaData().withCustomUrl("terms.com").withType(EulaType.CUSTOM);
    private static final IotCompanyData IOT_COMPANY_DATA = new IotCompanyData().withName(NAME).withFriendlyName(FRIENDLY_NAME);

    @Mock
    private Converter<EulaModel, EulaData> eula2EulaDataConverter;

    @Mock
    private Converter<IoTCompanyModel, IotCompanyData> cisIotCompanyDataConverter;

    private ContactDataPopulator contactDataPopulator;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
        contactDataPopulator = new ContactDataPopulator(eula2EulaDataConverter, cisIotCompanyDataConverter);
    }

    @DataProvider
    public static Object[][] appData() {
        return new Object[][] {
            { null, null, null, null, null,
                null, null, null, null, null, null },
            { APP_EMAIL_ADDRESS, APP_URL, SUPPORT_PHONE_NUMBER, PRIVACY_POLICY_URL, STANDARD_EULA,
                APP_EMAIL_ADDRESS, HTTP + APP_URL, SUPPORT_PHONE_NUMBER, HTTP + PRIVACY_POLICY_URL, STANDARD_EULA_DATA, IOT_COMPANY_DATA },

            { APP_EMAIL_ADDRESS, HTTP + APP_URL, SUPPORT_PHONE_NUMBER, HTTP + PRIVACY_POLICY_URL, CUSTOM_EULA,
                APP_EMAIL_ADDRESS, HTTP + APP_URL, SUPPORT_PHONE_NUMBER, HTTP + PRIVACY_POLICY_URL, CUSTOM_EULA_DATA, IOT_COMPANY_DATA },
        };
    }

    @Test
    @UseDataProvider("appData")
    public void populate_withApp_correctlyPopulated(
        String appEmailAddress, String supportPageUrl, String supportPhoneNumber,
        String privacyPolicyUrl, EulaModel eula, String expectedEmailAddress,
        String expectedSupportPageUrl, String expectedSupportPhoneNumber, String expectedPrivacyPolicyUrl,
        EulaData eulaData, IotCompanyData companyData) {
        ProductData productData = new ProductData();

        IoTCompanyModel company = createIotCompany(NAME, FRIENDLY_NAME);
        AppModel app = AppBuilder.generate()
            .withSupportPhoneNumber(supportPhoneNumber)
            .withPrivacyPolicyUrl(privacyPolicyUrl)
            .withEmailAddress(appEmailAddress)
            .withSupportPageUrl(supportPageUrl)
            .withCompany(company)
            .withEula(eula)
            .buildMockInstance();

        when(eula2EulaDataConverter.convert(eula)).thenReturn(eulaData);
        when(cisIotCompanyDataConverter.convert(company)).thenReturn(companyData);

        contactDataPopulator.populate(app, productData);

        if (companyData != null) {
            IotCompanyData convertedCompanyData = productData.getCompany();
            assertThat(convertedCompanyData.getName()).isEqualTo(NAME);
            assertThat(convertedCompanyData.getFriendlyName()).isEqualTo(FRIENDLY_NAME);
        }

        AppContactData appContactInfo = productData.getAppContactInfo();
        assertThat(appContactInfo.getSupportPhoneNumber()).isEqualTo(expectedSupportPhoneNumber);
        assertThat(appContactInfo.getEula()).isEqualTo(eulaData);
        assertThat(appContactInfo.getPrivacyPolicyUrl()).isEqualTo(expectedPrivacyPolicyUrl);
        assertThat(appContactInfo.getSupportPageUrl()).isEqualTo(expectedSupportPageUrl);
        assertThat(appContactInfo.getEmailAddress()).isEqualTo(expectedEmailAddress);
        if (eulaData != null && EulaType.CUSTOM.equals(eulaData.getType())) {
            assertThat(appContactInfo.getEula().getCustomUrl()).startsWith("https://");
        }
    }

    @Test
    public void populate_withUnknownProduct_correctlyPopulated() {
        ProductModel product = ProductBuilder.generate().buildMockInstance();

        ProductData actualResult = new ProductData();
        contactDataPopulator.populate(product, actualResult);

        ProductData expectedResult = new ProductData();

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);

    }

    private IoTCompanyModel createIotCompany(String name, String friendlyName) {
        IoTCompanyModel ioTCompanyModel = IoTCompanyBuilder.generate()
            .withName(name)
            .withFriendlyName(friendlyName)
            .buildMockInstance();
        return ioTCompanyModel;
    }

}
