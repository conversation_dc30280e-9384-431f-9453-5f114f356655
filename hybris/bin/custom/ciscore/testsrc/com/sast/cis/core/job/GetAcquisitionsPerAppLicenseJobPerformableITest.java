package com.sast.cis.core.job;

import com.sast.cis.core.dao.CatalogVersion;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.CronjobConfigurationModel;
import com.sast.cis.core.service.AppLicenseService;
import com.sast.cis.core.service.AppService;
import com.sast.cis.core.service.AppSyncService;
import com.sast.cis.test.utils.SampleDataCreator;
import com.sast.cis.test.utils.SessionCatalogRule;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.catalog.CatalogVersionService;
import de.hybris.platform.catalog.enums.ArticleApprovalStatus;
import de.hybris.platform.catalog.enums.SyncItemStatus;
import de.hybris.platform.catalog.model.CatalogVersionModel;
import de.hybris.platform.catalog.model.SyncItemJobModel;
import de.hybris.platform.catalog.synchronization.CatalogSynchronizationService;
import de.hybris.platform.catalog.synchronization.SyncItemInfo;
import de.hybris.platform.catalog.synchronization.SynchronizationStatusService;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.model.ItemModel;
import de.hybris.platform.core.model.order.OrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.cronjob.enums.CronJobResult;
import de.hybris.platform.cronjob.enums.CronJobStatus;
import de.hybris.platform.cronjob.model.CronJobModel;
import de.hybris.platform.servicelayer.ServicelayerTest;
import de.hybris.platform.servicelayer.cronjob.CronJobService;
import de.hybris.platform.servicelayer.cronjob.PerformResult;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.user.UserService;
import de.hybris.platform.store.BaseStoreModel;
import de.hybris.platform.store.services.BaseStoreService;
import de.hybris.platform.testframework.HybrisJUnit4ClassRunner;
import de.hybris.platform.testframework.RunListeners;
import de.hybris.platform.testframework.runlistener.ItemCreationListener;
import generated.com.sast.cis.core.model.CronjobConfigurationBuilder;
import generated.de.hybris.platform.cronjob.model.CronJobBuilder;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

import javax.annotation.Resource;

import static com.sast.cis.core.constants.CiscoreConstants.CIS_PRODUCT_CATALOG;
import static com.sast.cis.core.constants.CiscoreConstants.EVALUATION_SUFFIX;
import static com.sast.cis.core.dao.CatalogVersion.ONLINE;
import static com.sast.cis.core.dao.CatalogVersion.STAGED;
import static com.sast.cis.core.enums.LicenseType.EVALUATION;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
@RunWith(HybrisJUnit4ClassRunner.class)
@RunListeners({ ItemCreationListener.class })
public class GetAcquisitionsPerAppLicenseJobPerformableITest extends ServicelayerTest {
    private static final String APP_LICENSE_CODE_1 = "App1TitleAppLicense";
    private static final String APP_LICENSE_CODE_2 = "App2TitleAppLicense";
    private static final int INITIAL_AQUISITION_COUNT = 5;

    @Resource(name = "getAcquisitionsPerAppLicenseJobPerformable")
    private GetAcquisitionsPerAppLicenseJobPerformable performable;

    @Resource
    private ModelService modelService;

    @Resource
    private CronJobService cronJobService;

    @Resource
    private AppService appService;

    @Resource
    private AppLicenseService appLicenseService;

    @Resource
    private AppSyncService appSyncService;

    @Resource
    private SynchronizationStatusService synchronizationStatusService;

    @Resource
    private CatalogVersionService catalogVersionService;

    @Resource
    private CatalogSynchronizationService catalogSynchronizationService;

    @Resource
    private BaseStoreService baseStoreService;

    @Resource
    private UserService userService;

    @Rule
    public SessionCatalogRule sessionCatalogRule = SessionCatalogRule.stagedCatalog();

    private final SampleDataCreator sampleDataCreator = new SampleDataCreator();
    private AppModel app1;
    private AppModel app2;
    private AppModel app1Staged;
    private AppModel app2Staged;
    private AppLicenseModel fullAppLicense1;
    private AppLicenseModel fullAppLicense2;
    private AppLicenseModel evalAppLicense;
    private BaseStoreModel baseStoreModel;

    @Before
    public void setup() {
        baseStoreModel = baseStoreService.getBaseStoreForUid("iotstore");
        app1Staged = sampleDataCreator.createApp("appCode1", "packageName1", STAGED);
        app2Staged = sampleDataCreator.createApp("appCode2", "packageName2", STAGED);
        AppLicenseModel fullAppLicenseStaged1 = createAppLicense(APP_LICENSE_CODE_1, app1Staged, LicenseType.FULL);
        AppLicenseModel evalAppLicenseStaged = createAppLicense(APP_LICENSE_CODE_1 + EVALUATION_SUFFIX, app1Staged, EVALUATION);
        AppLicenseModel fullAppLicenseStaged2 = createAppLicense(APP_LICENSE_CODE_2, app2Staged, LicenseType.FULL);
        fullAppLicenseStaged1.setAcquisitionCount(INITIAL_AQUISITION_COUNT);
        modelService.saveAll(fullAppLicenseStaged1, app1Staged, app2Staged);
        app1 = createOnlineApp(app1Staged);
        app2 = createOnlineApp(app2Staged);
    }

    @Test
    public void updateAcquisitionCountOfAppLicenses() {
        fullAppLicense1 = appLicenseService.getFullAppLicenseForAzenaTenant(app1).get();
        fullAppLicense2 = appLicenseService.getFullAppLicenseForAzenaTenant(app2).get();
        evalAppLicense = appLicenseService.getEvaluationLicense(app1).get();

        assertThat(fullAppLicense1).isNotNull();
        assertThat(fullAppLicense2).isNotNull();
        assertThat(evalAppLicense).isNotNull();

        assertThat(fullAppLicense1.getAcquisitionCount()).isEqualTo(INITIAL_AQUISITION_COUNT);

        createOrderEntryAndOrder(fullAppLicense1, 12);
        createOrderEntryAndOrder(evalAppLicense, 5);
        createOrderEntryAndOrder(fullAppLicense2, 8);
        createOrderEntryAndOrder(fullAppLicense1, 7);
        CatalogVersionModel catalogVersion = catalogVersionService.getCatalogVersion(CIS_PRODUCT_CATALOG, STAGED.getVersionName());
        CronjobConfigurationModel configuration = CronjobConfigurationBuilder.generate()
            .withCode("getAcquisitionsPerAppLicenseJob_config")
            .withBaseStore(baseStoreModel)
            .withProductCatalogVersion(catalogVersion).buildIntegrationInstance();
        CronJobModel cronJob = CronJobBuilder.generate()
            .withCode("TestGetInstallsPerAppCronJob")
            .withJob(cronJobService.getJob("getAcquisitionsPerAppLicenseJob"))
            .withSessionUser(userService.getAdminUser())
            .withConfiguration(configuration)
            .buildIntegrationInstance();
        modelService.save(cronJob);
        PerformResult result = performable.perform(cronJob);

        assertThat(result).extracting(PerformResult::getStatus).isEqualTo(CronJobStatus.FINISHED);
        assertThat(result).extracting(PerformResult::getResult).isEqualTo(CronJobResult.SUCCESS);

        modelService.refresh(fullAppLicense1);
        modelService.refresh(fullAppLicense2);
        modelService.refresh(evalAppLicense);

        assertThat(fullAppLicense1.getAcquisitionCount()).isEqualTo(19);
        assertThat(fullAppLicense2.getAcquisitionCount()).isEqualTo(8);
        assertThat(evalAppLicense.getAcquisitionCount()).isEqualTo(5);
    }

    private AppLicenseModel createAppLicense(String licenseCode, AppModel app, LicenseType licenseType) {
        return sampleDataCreator.createAppLicense(licenseCode, app, ArticleApprovalStatus.APPROVED, licenseType);
    }

    private void createOrderEntryAndOrder(AppLicenseModel appLicense, int quantity) {
        OrderModel order = sampleDataCreator.createOrder(OrderStatus.COMPLETED);
        order.setStore(baseStoreModel);
        OrderEntryModel orderEntry = sampleDataCreator.createOrderEntry(order, appLicense, (long) quantity);
        modelService.saveAll(appLicense, order, orderEntry);
    }

    private SyncItemStatus getSyncStatus(ItemModel theItem) {
        SyncItemInfo syncInfo = synchronizationStatusService.getSyncInfo(theItem, findSyncItemJob());
        return syncInfo.getSyncStatus();
    }

    private SyncItemJobModel findSyncItemJob() {
        CatalogVersionModel stagedProductCatalog = catalogVersionService.getCatalogVersion(CIS_PRODUCT_CATALOG, STAGED.getVersionName());
        CatalogVersionModel onlineProductCatalog = catalogVersionService.getCatalogVersion(CIS_PRODUCT_CATALOG, ONLINE.getVersionName());
        return catalogSynchronizationService.getSyncJob(stagedProductCatalog, onlineProductCatalog, null);
    }

    private AppModel createOnlineApp(AppModel appStaged) {
        appSyncService.performSync(appSyncService.getItemsForSync(appStaged), CIS_PRODUCT_CATALOG);

        AppModel appOnline = appService.getOnlineAppForCode(appStaged.getCode()).get();

        assertThat(getSyncStatus(appStaged)).isEqualTo(SyncItemStatus.IN_SYNC);
        assertThat(getSyncStatus(appOnline)).isEqualTo(SyncItemStatus.IN_SYNC);
        return appOnline;
    }
}
