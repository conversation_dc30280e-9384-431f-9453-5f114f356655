package com.sast.cis.core.converter;

import com.sast.cis.core.data.ExportInformationEntryData;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.AppVersionModel;
import com.sast.cis.core.resolver.DetailUrlResolver;
import de.hybris.bootstrap.annotations.UnitTest;
import generated.com.sast.cis.core.model.AppBuilder;
import generated.com.sast.cis.core.model.AppVersionBuilder;
import generated.com.sast.cis.core.model.IoTCompanyBuilder;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnit;
import org.mockito.junit.MockitoRule;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
public class ExportInformationEntryDataPopulatorUnitTest {

    private static final String ECCN = "eccn";
    private static final String APP_NAME = "App Name";
    private static final String COMPANY_NAME = "Company Name";
    private static final String APP_LINK = "p_A1234";

    @Rule
    public MockitoRule mockitoRule = MockitoJUnit.rule();

    @Mock
    private DetailUrlResolver detailUrlResolver;

    @InjectMocks
    private ExportInformationEntryDataPopulator populator;

    @Test
    public void populateTheRightThings() {
        AppModel app = AppBuilder.generate()
            .withName(APP_NAME)
            .withCompany(IoTCompanyBuilder.generate().withName(COMPANY_NAME).buildMockInstance())
            .buildMockInstance();
        AppVersionModel version = AppVersionBuilder.generate()
            .withEccn(ECCN)
            .withApp(app)
            .buildMockInstance();
        when(detailUrlResolver.resolve(app)).thenReturn(APP_LINK);

        ExportInformationEntryData data = new ExportInformationEntryData();

        ExportInformationEntryData expectedData = new ExportInformationEntryData()
            .withEccn(ECCN)
            .withAppName(APP_NAME)
            .withCompanyName(COMPANY_NAME)
            .withLink(APP_LINK);

        populator.populate(version, data);

        assertThat(data).usingRecursiveComparison().isEqualTo(expectedData);
    }
}
