package com.sast.cis.core.service.singlesignon;

import com.sast.cis.core.BaseIntegrationTest;
import com.sast.cis.core.config.BaseStoreConfigService;
import com.sast.cis.core.constants.BaseStoreEnum;
import com.sast.cis.core.constants.CiscoreConstants;
import com.sast.cis.core.model.DeveloperModel;
import com.sast.cis.core.model.IntegratorModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.model.IoTCustomerModel;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.test.utils.UmpWireMockRule;
import de.hybris.platform.core.model.security.PrincipalGroupModel;
import de.hybris.platform.servicelayer.user.UserService;
import de.hybris.platform.site.BaseSiteService;
import org.junit.Before;
import org.junit.Rule;

import javax.annotation.Resource;

import static com.sast.cis.core.constants.CiscoreConstants.IOT_STORE_BASE_SITE_UID;
import static com.sast.cis.test.utils.UmpWireMockRule.UMP_BASE_URL_KEY;
import static org.assertj.core.api.Assertions.assertThat;

public abstract class BaseSsoServiceITest extends BaseIntegrationTest {
    protected static final String SSO_ACCOUNT_ID = "1234-1234-1234-1234";
    protected static final String COMPANY_ID = "**************-4321";
    protected static final String NAME = "Hans Dampf";
    protected static final String EMAIL = "<EMAIL>";
    @Rule
    public UmpWireMockRule umpWireMockRule = new UmpWireMockRule();
    @Resource
    protected UserService userService;
    @Resource
    protected IotCompanyService iotCompanyService;
    @Resource
    protected BaseSiteService baseSiteService;
    @Resource
    protected BaseStoreConfigService baseStoreConfigService;
    protected IoTCompanyModel company;
    protected PrincipalGroupModel developerGroup;
    protected PrincipalGroupModel integratorGroup;

    @Before
    public void setup() {
        String configPrefixForBaseStore = baseStoreConfigService.getConfigPrefixForBaseStore(BaseStoreEnum.AZENA);
        String key = String.format(UMP_BASE_URL_KEY, configPrefixForBaseStore);
        configurationService.getConfiguration().setProperty(key, umpWireMockRule.getUmpUrl());

        developerGroup = userService.getUserGroupForUID(CiscoreConstants.DEVELOPER_GROUP);
        integratorGroup = userService.getUserGroupForUID(CiscoreConstants.INTEGRATOR_GROUP);
        baseSiteService.setCurrentBaseSite(baseSiteService.getBaseSiteForUID(IOT_STORE_BASE_SITE_UID), true);

    }

    protected SsoUserDetails getTokenUserDetails(String ssoUid, String companyId) {
        return SsoUserDetails.builder()
            .name(NAME)
            .email(EMAIL)
            .preferredUsername(ssoUid)
            .companyId(companyId)
            .build();
    }

    protected <T extends IoTCustomerModel> void verifyTokenDataInUsers(T givenUser, SsoUserDetails tokenUserDetails) {
        DeveloperModel developer;
        IntegratorModel integrator;

        if (givenUser instanceof DeveloperModel) {
            developer = (DeveloperModel) givenUser;
            integrator = developer.getIntegrator();
        } else if (givenUser instanceof IntegratorModel) {
            integrator = (IntegratorModel) givenUser;
            developer = integrator.getDeveloper();
        } else {
            throw new RuntimeException(String.format("Whoopsie, given user is of type %s, i can't verify that",
                givenUser.getClass().getSimpleName()));
        }

        assertThat(integrator.getUid()).isEqualTo(tokenUserDetails.getPreferredUsername() + CiscoreConstants.INTEGRATOR_SUFFIX);
        assertThat(integrator.getCompany()).extracting(IoTCompanyModel::getUid).isEqualTo(tokenUserDetails.getCompanyId());
        assertThat(integrator.getEmailAddress()).isEqualTo(tokenUserDetails.getEmail());
        assertThat(integrator.getName()).isEqualTo(tokenUserDetails.getName());
        assertThat(integrator.getGroups()).containsExactlyInAnyOrder(integrator.getCompany(), integratorGroup);

        assertThat(developer.getUid()).isEqualTo(tokenUserDetails.getPreferredUsername() + CiscoreConstants.DEVELOPER_SUFFIX);
        assertThat(developer.getCompany()).extracting(IoTCompanyModel::getUid).isEqualTo(tokenUserDetails.getCompanyId());
        assertThat(developer.getEmailAddress()).isEqualTo(tokenUserDetails.getEmail());
        assertThat(developer.getName()).isEqualTo(tokenUserDetails.getName());
        assertThat(developer.getGroups()).containsExactlyInAnyOrder(developer.getCompany(), developerGroup);
    }
}
