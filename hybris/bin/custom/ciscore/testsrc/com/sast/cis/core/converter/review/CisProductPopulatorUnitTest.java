package com.sast.cis.core.converter.review;

import com.sast.cis.core.converter.CisProductPopulator;
import com.sast.cis.core.data.AppVideoData;
import com.sast.cis.core.data.IotCompanyData;
import com.sast.cis.core.data.PublicAppIntegrationData;
import com.sast.cis.core.enums.AppVideoStatus;
import com.sast.cis.core.model.ApkMediaModel;
import com.sast.cis.core.model.AppIntegrationModel;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.AppVersionModel;
import com.sast.cis.core.model.AppVideoModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.AppLicenseService;
import com.sast.cis.core.service.FeatureToggleService;
import com.sast.cis.core.service.app.AppIntegrationService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.commercefacades.user.data.CountryData;
import de.hybris.platform.commerceservices.url.UrlResolver;
import de.hybris.platform.converters.Populator;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import generated.com.sast.cis.core.model.ApkMediaBuilder;
import generated.com.sast.cis.core.model.AppBuilder;
import generated.com.sast.cis.core.model.AppLicenseBuilder;
import generated.com.sast.cis.core.model.AppVersionBuilder;
import generated.com.sast.cis.core.model.AppVideoBuilder;
import generated.com.sast.cis.core.model.IoTCompanyBuilder;
import generated.de.hybris.platform.core.model.product.ProductBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CisProductPopulatorUnitTest {

    private static final String COMPANY_NAME = "Test company name";
    private static final String COMPANY_FRIENDLY_NAME = "Test company friendly name";
    private static final String APP_NAME = "Test app";
    private static final String APP_DESCRIPTION = "Test test test. Test test test";
    private static final String APP_SUMMARY = "Test test test";
    private static final String PRODUCT_NOTE = "I am a note to a product";

    private static final String APP_VERSION_NAME = "App version test";
    private static final Integer APP_VERSION_SDK_ADDON_VERSION = 1;

    @Mock
    private UrlResolver<ProductModel> productModelUrlResolver;

    @Mock
    private Populator<ProductModel, ProductData> productBasicPopulator;

    @Mock
    private Populator<ProductModel, ProductData> variantSelectedPopulator;

    @Mock
    private Populator<ProductModel, ProductData> productPrimaryImagePopulator;

    @Spy
    private Converter<AppIntegrationModel, PublicAppIntegrationData> publicAppIntegrationDataConverter;

    @Mock
    private Converter<AppVideoModel, AppVideoData> appVideoConverter;

    @Mock
    private FeatureToggleService featureToggleService;

    @Mock
    private AppLicenseService appLicenseService;

    @Mock
    private AppIntegrationService appIntegrationService;

    @Mock
    private Converter<IoTCompanyModel, IotCompanyData> cisIotCompanyDataConverter;

    @Mock
    private Converter<CountryModel, CountryData> countryConverter;

    @InjectMocks
    private CisProductPopulator cisProductPopulator;

    private AppVersionModel appVersion;

    private IoTCompanyModel company;

    @Mock
    private CountryModel countryDE;

    @Mock
    private CountryModel countryEN;

    @Mock
    private CountryData convertedCountryDE;

    @Mock
    private CountryData convertedCountryEN;

    @Mock
    private PublicAppIntegrationData publicAppIntegrationData;

    @Before
    public void setUp() {

        when(countryConverter.convert(countryDE)).thenReturn(convertedCountryDE);
        when(countryConverter.convert(countryEN)).thenReturn(convertedCountryEN);
        when(publicAppIntegrationDataConverter.convert(any())).thenReturn(publicAppIntegrationData);

        ApkMediaModel apk = ApkMediaBuilder.generate()
            .withVersionName(APP_VERSION_NAME)
            .withSdkAddonVersion(APP_VERSION_SDK_ADDON_VERSION)
            .buildMockInstance();

        appVersion = AppVersionBuilder.generate().withApk(apk).buildMockInstance();

        company = IoTCompanyBuilder.generate().withName(COMPANY_NAME).withFriendlyName(COMPANY_FRIENDLY_NAME).buildMockInstance();
    }

    @Test
    public void populate_withAppLicense_correctlyPopulated() {
        AppModel app = getDefaultApp().buildMockInstance();
        AppLicenseModel appLicense = getDefaultAppLicense(app).buildMockInstance();

        when(appLicenseService.getAllLicenses(app)).thenReturn(List.of(appLicense));
        when(app.getProductNote()).thenReturn(PRODUCT_NOTE);

        ProductData actualResult = new ProductData();
        cisProductPopulator.populate(appLicense, actualResult);

        ProductData expectedResult = getDefaultExpectedResult();

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withApp_correctlyPopulated() {
        AppModel app = getDefaultApp().buildMockInstance();
        AppLicenseModel appLicense = getDefaultAppLicense(app).buildMockInstance();

        when(appLicenseService.getAllLicenses(app)).thenReturn(List.of(appLicense));
        when(app.getProductNote()).thenReturn(PRODUCT_NOTE);

        ProductData actualResult = new ProductData();
        cisProductPopulator.populate(app, actualResult);

        ProductData expectedResult = getDefaultExpectedResult();

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withUnknownProduct_resultIsEmpty() {

        ProductData actualResult = new ProductData();
        cisProductPopulator.populate(ProductBuilder.generate().buildMockInstance(), actualResult);

        ProductData expectedResult = new ProductData();

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withMultipleEnabledCountries_correctlyPopulated() {
        AppModel app = getDefaultApp().buildMockInstance();
        AppLicenseModel appLicense = getDefaultAppLicense(app).withEnabledCountries(Set.of(countryDE, countryEN)).buildMockInstance();

        when(appLicenseService.getAllLicenses(app)).thenReturn(List.of(appLicense));
        when(app.getProductNote()).thenReturn(PRODUCT_NOTE);

        ProductData actualResult = new ProductData();
        cisProductPopulator.populate(app, actualResult);

        ProductData expectedResult = getDefaultExpectedResult()
            .withEnabledCountries(Set.of(
                convertedCountryDE,
                convertedCountryEN
            ));

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withoutEnabledCountries_noEnabledCountriesInResult() {
        AppModel app = getDefaultApp().buildMockInstance();
        AppLicenseModel appLicense = getDefaultAppLicense(app).withEnabledCountries(Set.of()).buildMockInstance();

        when(appLicenseService.getAllLicenses(app)).thenReturn(List.of(appLicense));
        when(app.getProductNote()).thenReturn(PRODUCT_NOTE);

        ProductData actualResult = new ProductData();
        cisProductPopulator.populate(app, actualResult);

        ProductData expectedResult = getDefaultExpectedResult()
            .withEnabledCountries(Set.of());

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withoutLastVersion_relatedFieldsAreEmpty() {
        AppModel app = getDefaultApp().withLatestVersion(null).buildMockInstance();
        AppLicenseModel appLicense = getDefaultAppLicense(app).buildMockInstance();

        when(appLicenseService.getAllLicenses(app)).thenReturn(List.of(appLicense));
        when(app.getProductNote()).thenReturn(PRODUCT_NOTE);

        ProductData actualResult = new ProductData();
        cisProductPopulator.populate(appLicense, actualResult);

        ProductData expectedResult = getDefaultExpectedResult().withVersionName(null).withSdkAddonVersion(null);

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withoutApk_relatedFieldsAreEmpty() {
        AppVersionModel appVersionWithoutApk = AppVersionBuilder.generate().buildMockInstance();
        AppModel app = getDefaultApp().withLatestVersion(appVersionWithoutApk).buildMockInstance();
        AppLicenseModel appLicense = getDefaultAppLicense(app).buildMockInstance();

        when(appLicenseService.getAllLicenses(app)).thenReturn(List.of(appLicense));
        when(app.getProductNote()).thenReturn(PRODUCT_NOTE);

        ProductData actualResult = new ProductData();
        cisProductPopulator.populate(appLicense, actualResult);

        ProductData expectedResult = getDefaultExpectedResult().withVersionName(null).withSdkAddonVersion(null);

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withoutVideo() {
        AppModel app = getDefaultApp().withVideo(AppVideoBuilder.generate()
                .withStatus(AppVideoStatus.NOT_FOUND).buildMockInstance()).
            buildMockInstance();
        AppLicenseModel appLicense = getDefaultAppLicense(app).buildMockInstance();
        when(appLicenseService.getAllLicenses(app)).thenReturn(List.of(appLicense));
        when(app.getProductNote()).thenReturn(PRODUCT_NOTE);

        ProductData actualResult = new ProductData();
        cisProductPopulator.populate(appLicense, actualResult);

        ProductData expectedResult = getDefaultExpectedResult().withVideo(null);
        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withVideo() {
        AppModel app = getDefaultApp().withVideo(AppVideoBuilder.generate()
                .withStatus(AppVideoStatus.FOUND).buildMockInstance()).
            buildMockInstance();
        AppLicenseModel appLicense = getDefaultAppLicense(app).buildMockInstance();
        AppVideoData videoData = new AppVideoData().withSource("source");
        when(appLicenseService.getAllLicenses(app)).thenReturn(List.of(appLicense));
        when(app.getProductNote()).thenReturn(PRODUCT_NOTE);
        when(appVideoConverter.convert(app.getVideo())).thenReturn(videoData);

        ProductData actualResult = new ProductData();
        cisProductPopulator.populate(appLicense, actualResult);

        ProductData expectedResult = getDefaultExpectedResult().withVideo(videoData);
        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withCompanyData() {
        AppModel app = getDefaultApp().withCompany(company).buildMockInstance();
        AppLicenseModel appLicense = getDefaultAppLicense(app).buildMockInstance();
        IotCompanyData iotCompanyData = new IotCompanyData().withName(COMPANY_NAME).withFriendlyName(COMPANY_FRIENDLY_NAME);
        when(appLicenseService.getAllLicenses(app)).thenReturn(List.of(appLicense));
        when(app.getProductNote()).thenReturn(PRODUCT_NOTE);
        when(cisIotCompanyDataConverter.convert(company)).thenReturn(iotCompanyData);

        ProductData actualResult = new ProductData();
        cisProductPopulator.populate(appLicense, actualResult);

        ProductData expectedResult = getDefaultExpectedResult().withCompany(iotCompanyData);
        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    private AppBuilder<?, ?> getDefaultApp() {
        return AppBuilder.generate()
            .withName(APP_NAME)
            .withDescription(APP_DESCRIPTION)
            .withSummary(APP_SUMMARY)
            .withCompany(company)
            .withLatestVersion(appVersion);
    }

    private AppLicenseBuilder<?, ?> getDefaultAppLicense(AppModel app) {
        return AppLicenseBuilder.generate()
            .withBaseProduct(app)
            .withEnabledCountries(Set.of(countryDE));
    }

    private ProductData getDefaultExpectedResult() {
        return new ProductData()
            .withName(APP_NAME)
            .withDescription(APP_DESCRIPTION)
            .withSummary(APP_SUMMARY)
            .withEnabledCountries(Set.of(convertedCountryDE))
            .withVersionName(APP_VERSION_NAME)
            .withSdkAddonVersion(APP_VERSION_SDK_ADDON_VERSION)
            .withAppIntegrations(Collections.emptyList())
            .withProductNote(PRODUCT_NOTE);
    }

}
