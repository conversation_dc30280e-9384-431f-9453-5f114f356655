package com.sast.cis.core.job;

import com.sast.cis.core.enums.InvoiceStatus;
import com.sast.cis.core.model.InvoiceModel;
import com.sast.cis.core.service.invoice.InvoiceService;
import com.sast.cis.test.utils.SampleDataCreator;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.core.enums.OrderStatus;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.cronjob.enums.CronJobResult;
import de.hybris.platform.cronjob.enums.CronJobStatus;
import de.hybris.platform.cronjob.model.CronJobModel;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.cronjob.CronJobService;
import de.hybris.platform.servicelayer.cronjob.PerformResult;
import de.hybris.platform.servicelayer.model.ModelService;
import generated.com.sast.cis.core.model.InvoiceBuilder;
import generated.de.hybris.platform.cronjob.model.CronJobBuilder;
import org.apache.commons.lang3.time.DateUtils;
import org.assertj.core.util.DateUtil;
import org.junit.Test;

import javax.annotation.Resource;

import java.util.Date;
import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class InvoiceStatusOverdueJobPerformableITest extends ServicelayerTransactionalTest {

    @Resource
    private InvoiceStatusOverdueJobPerformable invoiceStatusOverdueJobPerformable;

    @Resource
    private ModelService modelService;

    @Resource
    private InvoiceService invoiceService;

    @Resource
    private CronJobService cronJobService;

    private final SampleDataCreator sampleDataCreator = new SampleDataCreator();

    @Test
    public void testInvoiceStatusOverdueJob() {
        final Date overdueDate = DateUtils.addDays(new Date(), -invoiceService.getTermOfPaymentInDays() - 1);
        final InvoiceModel overdueInvoice = createTestInvoice(InvoiceStatus.PENDING, overdueDate);
        final InvoiceModel pendingInvoice = createTestInvoice(InvoiceStatus.PENDING, DateUtil.now());
        final InvoiceModel paidInvoice = createTestInvoice(InvoiceStatus.PAID, overdueDate);

        final CronJobModel cronJob = CronJobBuilder.generate()
            .withCode("testInvoiceStatusOverdueJob")
            .withJob(cronJobService.getJob("invoiceStatusOverdueJob"))
            .buildIntegrationInstance();
        modelService.save(cronJob);
        final PerformResult result = invoiceStatusOverdueJobPerformable.perform(cronJob);

        assertThat(result).extracting(PerformResult::getStatus).isEqualTo(CronJobStatus.FINISHED);
        assertThat(result).extracting(PerformResult::getResult).isEqualTo(CronJobResult.SUCCESS);

        modelService.refresh(overdueInvoice);
        modelService.refresh(pendingInvoice);
        modelService.refresh(paidInvoice);

        assertThat(overdueInvoice.getStatus()).isEqualTo(InvoiceStatus.OVERDUE);
        assertThat(pendingInvoice.getStatus()).isEqualTo(InvoiceStatus.PENDING);
        assertThat(paidInvoice.getStatus()).isEqualTo(InvoiceStatus.PAID);
    }

    private InvoiceModel createTestInvoice(final InvoiceStatus status, final Date invoiceDate) {
        final InvoiceModel invoice = InvoiceBuilder.generate()
            .withExternalId(UUID.randomUUID().toString())
            .withStatus(status)
            .withInvoiceDate(invoiceDate)
            .withOrder(Set.of(createTestOrder()))
            .buildIntegrationInstance();
        modelService.save(invoice);
        return invoice;
    }

    private OrderModel createTestOrder() {
        final OrderModel order = sampleDataCreator.createOrder(OrderStatus.COMPLETED);
        order.setExternalOrderId(UUID.randomUUID().toString());
        order.setCode(UUID.randomUUID().toString());
        order.setTotalPrice(100d);
        modelService.save(order);
        return order;
    }
}
