package com.sast.cis.core.converter.product;

import com.sast.cis.core.data.PdfData;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.PdfMediaModel;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import generated.com.sast.cis.core.model.AppBuilder;
import generated.com.sast.cis.core.model.AppLicenseBuilder;
import generated.com.sast.cis.core.model.PdfMediaBuilder;
import generated.de.hybris.platform.core.model.product.ProductBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class ProductPdfsPopulatorUnitTest {

    private static final String PDF_ONE_CODE = "testPdfOneCode";
    private static final String PDF_TWO_CODE = "testPdfTwoCode";

    @Mock
    private Converter<PdfMediaModel, PdfData> pdfDataConverter;

    @InjectMocks
    private ProductPdfsPopulator populator;

    private PdfMediaModel documentOne;
    private PdfMediaModel documentTwo;
    private PdfData pdfDataOne;
    private PdfData pdfDataTwo;

    @Before
    public void setUp() {
        documentOne = PdfMediaBuilder.generate().withCode(PDF_ONE_CODE).buildMockInstance();
        documentTwo = PdfMediaBuilder.generate().withCode(PDF_TWO_CODE).buildMockInstance();

        pdfDataOne = new PdfData().withCode(PDF_ONE_CODE);
        pdfDataTwo = new PdfData().withCode(PDF_TWO_CODE);

        when(pdfDataConverter.convertAll(List.of(documentOne, documentTwo))).thenReturn(List.of(pdfDataOne, pdfDataTwo));
    }

    @Test
    public void populate_WithApp_properlyPopulated() {
        AppModel app = createDefaultApp(List.of(documentOne, documentTwo));

        when(pdfDataConverter.convertAll(List.of(documentOne, documentTwo))).thenReturn(List.of(pdfDataOne, pdfDataTwo));

        ProductData actualResult = new ProductData();
        populator.populate(app, actualResult);

        ProductData expectedResult = new ProductData().withPdfDocuments(List.of(pdfDataOne, pdfDataTwo));

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_WithAppLicense_properlyPopulated() {
        AppModel app = createDefaultApp(List.of(documentOne, documentTwo));
        AppLicenseModel appLicense = AppLicenseBuilder.generate().withBaseProduct(app).buildMockInstance();

        when(pdfDataConverter.convertAll(List.of(documentOne, documentTwo))).thenReturn(List.of(pdfDataOne, pdfDataTwo));

        ProductData actualResult = new ProductData();
        populator.populate(appLicense, actualResult);

        ProductData expectedResult = new ProductData().withPdfDocuments(List.of(pdfDataOne, pdfDataTwo));

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_WithUnknownProduct_nothingPopulated() {
        ProductModel product = ProductBuilder.generate().buildMockInstance();

        ProductData actualResult = new ProductData();
        populator.populate(product, actualResult);

        ProductData expectedResult = new ProductData();

        verify(pdfDataConverter, never()).convertAll(any());
        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    private AppModel createDefaultApp(List<PdfMediaModel> docs) {
        return AppBuilder.generate().withDocumentationFiles(docs).buildMockInstance();
    }

}
