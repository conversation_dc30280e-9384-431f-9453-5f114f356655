package com.sast.cis.core.converter.payment;

import com.sast.cis.core.data.InvoicePaymentInfoData;
import com.sast.cis.core.enums.PaymentMethodType;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.order.payment.InvoicePaymentInfoModel;
import de.hybris.platform.core.model.user.CustomerModel;
import de.hybris.platform.servicelayer.dto.converter.ConversionException;
import generated.de.hybris.platform.core.model.order.payment.InvoicePaymentInfoBuilder;
import generated.de.hybris.platform.core.model.user.CustomerBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class InvoicePaymentInfoPopulatorUnitTest {

    private static final boolean SAVED = true;

    @InjectMocks
    private InvoicePaymentInfoPopulator populator;

    @Test
    public void convert_SourceIsNull_throwsException() {
        InvoicePaymentInfoData actualTarget = new InvoicePaymentInfoData();
        assertThatThrownBy(() -> populator.populate(null, actualTarget)).isInstanceOf(ConversionException.class);
    }

    @Test
    public void convert_targetIsNull_throwsException() {
        InvoicePaymentInfoModel invoicePaymentInfoModel = InvoicePaymentInfoBuilder.generate().buildInstance();
        assertThatThrownBy(() -> populator.populate(invoicePaymentInfoModel, null)).isInstanceOf(ConversionException.class);
    }

    @Test
    public void convert_SourceIsFullAndDefault_returnsFullEntry() {
        InvoicePaymentInfoModel source = InvoicePaymentInfoBuilder.generate()
            .withSaved(SAVED).buildInstance();
        CustomerModel customer = CustomerBuilder.generate()
            .withDefaultPaymentInfo(source)
            .buildInstance();
        source.setUser(customer);
        InvoicePaymentInfoData actualTarget = new InvoicePaymentInfoData();

        populator.populate(source, actualTarget);

        InvoicePaymentInfoData expectedTarget = new InvoicePaymentInfoData();
        expectedTarget.setId(source.getPk().toString());
        expectedTarget.setSaved(SAVED);
        expectedTarget.setDefaultPaymentInfo(true);
        expectedTarget.setPaymentMethod(PaymentMethodType.INVOICE);

        assertThat(actualTarget).usingRecursiveComparison().isEqualTo(expectedTarget);
    }

    @Test
    public void convert_SourceIsFull_returnsFullEntry() {
        InvoicePaymentInfoModel source = InvoicePaymentInfoBuilder.generate()
            .withSaved(SAVED).buildInstance();
        CustomerModel customer = CustomerBuilder.generate()
            .withDefaultPaymentInfo(null)
            .buildInstance();
        source.setUser(customer);
        InvoicePaymentInfoData actualTarget = new InvoicePaymentInfoData();

        populator.populate(source, actualTarget);

        InvoicePaymentInfoData expectedTarget = new InvoicePaymentInfoData();
        expectedTarget.setId(source.getPk().toString());
        expectedTarget.setSaved(SAVED);
        expectedTarget.setDefaultPaymentInfo(false);
        expectedTarget.setPaymentMethod(PaymentMethodType.INVOICE);

        assertThat(actualTarget).usingRecursiveComparison().isEqualTo(expectedTarget);
    }
}
