package com.sast.cis.core.products.availability.service;

import com.sast.cis.core.customergroup.CustomerGroupService;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.service.AppSyncService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.catalog.model.CatalogVersionModel;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.user.UserService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static com.sast.cis.core.dao.CatalogVersion.ONLINE;
import static com.sast.cis.core.dao.CatalogVersion.STAGED;
import static com.sast.cis.core.enums.StoreAvailabilityMode.PUBLIC;
import static com.sast.cis.core.enums.StoreAvailabilityMode.RESTRICTED_BUYER_GROUP;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class AvailabilityToPricesSyncServiceUnitTest {

    @InjectMocks
    private AvailabilityToPricesSyncService availabilityToPricesSyncService;

    @Mock
    private AppModel app;

    @Mock
    private CatalogVersionModel catalogVersion;

    private final String appCode = "AA2_040111111";

    @Before
    public void setUp() {
        when(app.getCode()).thenReturn(appCode);
        when(app.getStoreAvailabilityMode()).thenReturn(RESTRICTED_BUYER_GROUP);
        when(app.getCatalogVersion()).thenReturn(catalogVersion);

        when(catalogVersion.getVersion()).thenReturn(STAGED.getVersionName());
    }

    @Test
    public void givenAppWithAvailabilityModeOtherThanRestrictedBuyerGroup_whenVerifyAppAvailability_thenThrowException() {
        when(app.getStoreAvailabilityMode()).thenReturn(PUBLIC);

        assertThatThrownBy(() -> availabilityToPricesSyncService.verifyAppAvailability(app))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("App with code '%s' is not a user group restricted product", appCode);
    }

    @Test
    public void givenAppFromOnlineCatalogVersion_whenVerifyAppAvailability_thenThrowException() {
        when(catalogVersion.getVersion()).thenReturn(ONLINE.getVersionName());

        assertThatThrownBy(() -> availabilityToPricesSyncService.verifyAppAvailability(app))
            .isInstanceOf(IllegalArgumentException.class)
            .hasMessage("App with code '%s' should be from the STAGED catalog version", appCode);
    }
}
