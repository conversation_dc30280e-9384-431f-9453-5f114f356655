package com.sast.cis.core.facade;

import com.google.common.collect.ImmutableList;
import com.sast.cis.core.SpringContextServiceMockHelper;
import com.sast.cis.core.config.keycloak.SiteUmpAdapterConfigResolutionService;
import com.sast.cis.core.constants.devcon.DevconInputField;
import com.sast.cis.core.dao.CatalogVersion;
import com.sast.cis.core.dao.IndustryDao;
import com.sast.cis.core.data.*;
import com.sast.cis.core.enums.AppIntegrationStatus;
import com.sast.cis.core.enums.AppIntegrationType;
import com.sast.cis.core.enums.AppVideoStatus;
import com.sast.cis.core.enums.EulaType;
import com.sast.cis.core.enums.Feature;
import com.sast.cis.core.exceptions.web.BadRequestException;
import com.sast.cis.core.exceptions.web.NotFoundException;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.DeveloperModel;
import com.sast.cis.core.model.IndustryModel;
import com.sast.cis.core.model.ProductContainerModel;
import com.sast.cis.core.model.StoreContentDraftModel;
import com.sast.cis.core.model.UseCaseModel;
import com.sast.cis.core.service.SpringContextService;
import com.sast.cis.core.service.TranslationService;
import com.sast.cis.core.service.UseCaseService;
import com.sast.cis.core.service.customer.developer.DeveloperService;
import com.sast.cis.core.service.media.CisMediaContainerService;
import com.sast.cis.test.utils.FeatureToggleRule;
import com.sast.cis.test.utils.LoginUtil;
import com.sast.cis.test.utils.SampleDataCreator;
import com.sast.cis.test.utils.TestDataConstants;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.catalog.CatalogVersionService;
import de.hybris.platform.catalog.enums.ArticleApprovalStatus;
import de.hybris.platform.catalog.model.CatalogVersionModel;
import de.hybris.platform.core.model.ItemModel;
import de.hybris.platform.core.model.media.MediaContainerModel;
import de.hybris.platform.core.model.media.MediaModel;
import de.hybris.platform.servicelayer.ServicelayerTest;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.exceptions.ModelNotFoundException;
import de.hybris.platform.servicelayer.media.MediaService;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import de.hybris.platform.servicelayer.user.UserService;
import de.hybris.platform.testframework.HybrisJUnit4ClassRunner;
import de.hybris.platform.testframework.RunListeners;
import de.hybris.platform.testframework.Transactional;
import de.hybris.platform.testframework.runlistener.ItemCreationListener;
import org.apache.commons.beanutils.BeanUtils;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sast.cis.core.constants.CiscoreConstants.CIS_PRODUCT_CATALOG;
import static com.sast.cis.core.dao.CatalogVersion.ONLINE;
import static com.sast.cis.core.dao.CatalogVersion.STAGED;
import static com.sast.cis.test.utils.SampleDataCreator.*;
import static com.sast.cis.test.utils.TestDataConstants.PRODUCT_CONTAINER.*;
import static com.sast.cis.test.utils.TestDataConstants.*;
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.APPROVED;
import static java.util.Collections.emptyList;
import static java.util.Locale.ENGLISH;
import static java.util.Locale.GERMAN;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.fail;

/**
 * This integration test intentionally does not extend {@link ServicelayerTransactionalTest},
 * because we want to test the transaction rollback mechanism with {@link TransactionTemplate} and because
 * we need the cron job of the catalog sync to work (will be launched in a different thread).
 * This interferes with the {@link Transactional} annotation.
 * Test methods which need transactional behavior should be annotated explicitly
 */
@IntegrationTest
@RunWith(HybrisJUnit4ClassRunner.class)
@RunListeners({ ItemCreationListener.class })
public class StoreContentFacadeITest extends ServicelayerTest {

    private static final String STORE_CONTENT_DRAFT_CODE_PREFIX = "SCD_";
    private static final String INVALID_CODE = "noContainer";
    private static final String APP_TITLE = "app title";
    private static final String INVALID_PRODUCT_CONTAINER_CODE = "Unknown product container code";
    private static final String PRODUCT_CONTAINER_NOT_FOUND_CODE = "productcontainer.not.found";
    private static final String SCREENSHOT_TO_BE_KEPT = "screenshotToBeKept";
    private static final String SCREENSHOT_CONTAINER_QUALIFIER = SCREENSHOT_TO_BE_KEPT + MEDIA_CONTAINER_CODE_SUFFIX;
    private static final int NUMBER_UPLOADED_SCREENSHOTS = 2;
    private static final int NUMBER_UPLOADED_ICON = 1;

    @Resource
    private UserService userService;

    @Resource
    private DeveloperService developerService;

    @Resource
    private StoreContentFacade storeContentFacade;

    @Resource
    private MediaService mediaService;

    @Resource
    private ModelService modelService;

    @Resource
    private FlexibleSearchService flexibleSearchService;

    @Resource
    private CatalogVersionService catalogVersionService;

    @Resource
    private CisMediaContainerService cisMediaContainerService;

    @Resource
    private TranslationService translationService;

    @Resource
    private RestTemplate cisRestTemplate;

    @Resource
    private SiteUmpAdapterConfigResolutionService siteUmpAdapterConfigResolutionService;

    @Resource
    private IndustryDao industryDao;

    @Resource
    private UseCaseService useCaseService;

    @Rule
    public ExpectedException expectedException = ExpectedException.none();

    @Rule
    public FeatureToggleRule featureToggleRule = new FeatureToggleRule();

    private IndustryModel industry;

    private DeveloperModel developerFromCompanyA;
    private final SampleDataCreator sampleDataCreator = new SampleDataCreator();
    private StoreContentData expectedStoreContentData;
    private StoreContentData expectedEmptyStoreContentData;
    private StoreContentData completeStoreContentData;
    private ProductContainerRelatedData expectedStoreContentDataFromApp;
    private ClientImageData clientImageData;
    private UseCaseModel useCaseModel;

    @Before
    public void setup() throws Exception {
        catalogVersionService.setSessionCatalogVersion(CIS_PRODUCT_CATALOG, STAGED.getVersionName());

        ClientImageData screenshotToBeKept = new ClientImageData()
            .withQualifier(SCREENSHOT_CONTAINER_QUALIFIER);

        LoginUtil loginUtil = new LoginUtil(siteUmpAdapterConfigResolutionService, cisRestTemplate);
        SecurityContextHolder.getContext().setAuthentication(loginUtil.generateKeycloakAuthenticationObject());

        developerFromCompanyA = developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID);
        userService.setCurrentUser(developerFromCompanyA);

        expectedEmptyStoreContentData = new StoreContentData()
            .withDescriptionByIsocode(mutableCopyOf(Map.of("de", "", "en", "")))
            .withNameByIsocode(mutableCopyOf(Map.of("de", "", "en", APP_NAME)))
            .withDocumentationFiles(emptyList());

        expectedEmptyStoreContentData.withProductContainerCode(getProductContainerCodeFromTitle(APP_NAME))
            .withDraft(true)
            .withEnabledInStoreSidebarFlag(true)
            .withAppEditable(true);

        expectedStoreContentData = ((StoreContentData) BeanUtils.cloneBean(expectedEmptyStoreContentData))
            .withNameByIsocode(mutableCopyOf(Map.of("de", NAME_GERMAN, "en", APP_NAME)))
            .withDescriptionByIsocode(mutableCopyOf(Map.of("de", DESCRIPTION_GERMAN, "en", DESCRIPTION_ENGLISH)))
            .withSupportPageUrl(SUPPORT_PAGE)
            .withProductWebsiteUrl(PRODUCT_WEBSITE)
            .withEmailAddress(EMAIL)
            .withSupportPhoneNumber(SUPPORT_PHONE)
            .withPrivacyPolicyUrl(TestDataConstants.PRODUCT_CONTAINER.PRIVACY_POLICY)
            .withVideoUrl(APP_VIDEO_SOURCE)
            .withVideoStatus(AppVideoStatus.NOT_FOUND);

        clientImageData = new ClientImageData().withQualifier("TestIcon-container");
        sampleDataCreator.createUploadedIcon("TestIcon");

        completeStoreContentData = ((StoreContentData) BeanUtils.cloneBean(expectedStoreContentData))
            .withNameByIsocode(mutableCopyOf(Map.of("de", "  " + NAME_GERMAN + "  ", "en", "  " + APP_NAME + "  ")))
            .withIcon(Collections.singletonList(clientImageData))
            .withScreenshots(List.of(screenshotToBeKept));

        expectedStoreContentDataFromApp = new StoreContentData()
            .withNameByIsocode(mutableCopyOf(Map.of("en", "name_" + SampleDataCreator.APP_CODE_PREFIX + APP_TITLE,
                "de", "Name_" + SampleDataCreator.APP_CODE_PREFIX + APP_TITLE)))
            .withDescriptionByIsocode(mutableCopyOf(Map.of("en", SampleDataCreator.APP_DESCRIPTION_EN,
                "de", SampleDataCreator.APP_DESCRIPTION_DE)))
            .withEmailAddress(SampleDataCreator.APP_CONTACT_EMAIL)
            .withSupportPhoneNumber(SampleDataCreator.APP_PHONE_NUMBER)
            .withSupportPageUrl(SampleDataCreator.APP_URL)
            .withPrivacyPolicyUrl(SampleDataCreator.PRIVACY_POLICY)
            .withVideoUrl(APP_VIDEO_SOURCE)
            .withVideoStatus(AppVideoStatus.NOT_FOUND)
            .withAppApprovalStatus(ArticleApprovalStatus.DRAFT);

        expectedStoreContentDataFromApp.withProductContainerCode(SampleDataCreator.PRODUCT_CONTAINER_CODE_PREFIX + APP_TITLE)
            .withApproved(true)
            .withEnabledInStoreSidebarFlag(true);

        industry = industryDao.getAllEnabledIndustries().stream().findFirst().get();

        useCaseModel = useCaseService.getAllEnabledUseCases().stream().findFirst().get();
        SpringContextService springContextService = new SpringContextServiceMockHelper().newSpringContextServiceMock();
        ReflectionTestUtils.setField(translationService, "springContextService", springContextService);
    }

    private void createUploadedScreenshots() {
        CatalogVersionModel catalogVersion = sampleDataCreator.getOrCreateProductCatalogVersion(CatalogVersion.STAGED);
        sampleDataCreator.createScreenshotInMediaContainer(SCREENSHOT_TO_BE_KEPT, catalogVersion, false);
    }

    @Test
    public void getStoreContentData_givenProductContainerWithDraft_returnsStoreContent() {
        ProductContainerModel productContainer = sampleDataCreator
            .createProductContainerWithAppDraft(APP_NAME, developerFromCompanyA);
        createUploadedScreenshots();
        StoreContentData storeContentData = storeContentFacade.getStoreContentData(productContainer.getCode()).orElseGet(() -> {
            fail("Expected StoreContentData to be present but was not.");
            return null;
        });

        assertThat(storeContentData).isEqualToIgnoringNullFields(expectedStoreContentData);
        assertThat(storeContentData.getIcon().get(0).getUrl()).startsWith(toIconUrlPrefix(APP_NAME));
        assertPresenceOfScreenshot(storeContentData, APP_NAME);
    }

    private String toIconUrlPrefix(String containerTitle) {
        return "/images/" + ICON_PREFIX + toUrlPart(containerTitle) + "_118Wx118H";
    }

    private String toUrlPart(String input) {
        return URLEncoder.encode(input, Charset.defaultCharset());
    }

    private void assertPresenceOfScreenshot(StoreContentData storeContentData, String qualifier) {
        assertThat(storeContentData.getScreenshots()).hasSize(1);

        ClientImageData screenshotData = storeContentData.getScreenshots().get(0);

        ClientImageData expectedScreenshotData = new ClientImageData()
            .withQualifier(SCREENSHOT_PREFIX + qualifier + MEDIA_CONTAINER_CODE_SUFFIX);
        assertThat(screenshotData).isEqualToIgnoringGivenFields(expectedScreenshotData, "url");
        assertThat(screenshotData.getUrl()).startsWith("/images/" + SCREENSHOT_PREFIX + toUrlPart(qualifier) + "_210Wx118H");
    }

    @Test
    public void getStoreContentData_givenProductContainerWithoutDraft_returnsEmptyStoreContent() {
        ProductContainerModel productContainer = sampleDataCreator.createProductContainerWithAppDraft(APP_NAME,
            developerFromCompanyA.getCompany());
        Optional<StoreContentData> storeContentData = storeContentFacade.getStoreContentData(productContainer.getCode());

        assertThat(storeContentData).isPresent();
        assertThat(storeContentData.get()).isEqualToIgnoringNullFields(expectedEmptyStoreContentData);
    }

    @Test
    public void getStoreContentData_givenExistingProductContainerAndDifferentDeveloperOfSameCompany_returnsStoreContent() {
        ProductContainerModel productContainer = sampleDataCreator.createProductContainerWithAppDraft(APP_NAME,
            developerFromCompanyA);
        createUploadedScreenshots();

        useDifferentUserOfSameCompany();
        Optional<StoreContentData> storeContentData = storeContentFacade.getStoreContentData(productContainer.getCode());

        assertThat(storeContentData).isPresent();
        assertThat(storeContentData.get()).isEqualToIgnoringNullFields(expectedStoreContentData);
    }

    private void useDifferentUserOfSameCompany() {
        DeveloperModel anotherDeveloperFromCompanyA = developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A2_UID);
        userService.setCurrentUser(anotherDeveloperFromCompanyA);
    }

    @Test
    public void getStoreContentData_givenNonExistingProductContainerCode_returnsEmpty() {
        ProductContainerModel productContainer = sampleDataCreator.createProductContainerWithAppDraft(APP_NAME,
            developerFromCompanyA);
        createUploadedScreenshots();

        useUserOfDifferentCompany();
        Optional<StoreContentData> storeContentData = storeContentFacade.getStoreContentData(productContainer.getCode());

        assertThat(storeContentData).isEmpty();
    }

    private void useUserOfDifferentCompany() {
        DeveloperModel developerFromCompanyB = developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_B1_UID);
        userService.setCurrentUser(developerFromCompanyB);
    }

    @Test
    public void getStoreContentData_givenContainerOfDifferentCompany_returnsEmpty() {
        assertThat(storeContentFacade.getStoreContentData(INVALID_PRODUCT_CONTAINER_CODE)).isEmpty();
    }

    @Test
    public void getStoreContentData_givenEmptyDraft_returnsEmptyStoreContent() {
        String productContainerCode = sampleDataCreator
            .createProductContainerWithEmptyStoreContentDraft(APP_NAME, developerFromCompanyA).getCode();

        Optional<StoreContentData> storeContentData = storeContentFacade.getStoreContentData(productContainerCode);

        assertThat(storeContentData).isPresent();
    }

    @Test
    public void getStoreContentData_givenValidApprovedApp_returnsDataFilledFromApp() {
        ProductContainerModel productContainerWithApp = sampleDataCreator.createProductContainerWithApp(APP_TITLE, developerFromCompanyA);
        createUploadedScreenshots();

        Optional<StoreContentData> storeContentData = storeContentFacade.getStoreContentData(productContainerWithApp.getCode());

        assertThat(storeContentData).isPresent();
        assertThat(storeContentData.get()).isEqualToIgnoringNullFields(expectedStoreContentDataFromApp
            .withAppEditable(true)
            .withAppApprovalStatus(APPROVED)
        );

        assertThat(storeContentData.get().getIcon().get(0).getUrl()).startsWith(toIconUrlPrefix(APP_CODE_PREFIX + APP_TITLE));
        assertPresenceOfScreenshot(storeContentData.get(), APP_CODE_PREFIX + APP_TITLE);

        ReleasabilityStateData appReleasability = storeContentData.get().getReleasabilityState();
        assertThat(appReleasability.isPublicListingReleasable()).isTrue();
        assertThat(appReleasability.isApkReleasable()).isTrue();
        assertThat(appReleasability.isPricingReleasable()).isTrue();
        assertThat(appReleasability.isAvailabilityReleasable()).isTrue();
    }

    @Test
    public void createOrUpdateDraft_givenStoreContentData_modelIsFilled() {
        ProductContainerModel productContainer = sampleDataCreator
            .createProductContainerWithAppDraft(APP_NAME, developerFromCompanyA.getCompany());

        assertThat(productContainer.getAppDraft().getStoreContentDraft().getVideo().getSource()).isNotEmpty();

        completeStoreContentData.withUseCases(getUseCaseData());
        completeStoreContentData.withIndustries(getIndustryData());
        completeStoreContentData.withVideoUrl("https://www.youtube.com/watch?v=42424242");
        completeStoreContentData.setEula(new EulaData().withType(EulaType.STANDARD));

        createUploadedScreenshots();

        storeContentFacade.createOrUpdateDraft(completeStoreContentData);
        modelService.refresh(productContainer);

        assertThat(productContainer.getAppDraft()).isNotNull();
        modelService.refresh(productContainer.getAppDraft());

        StoreContentDraftModel storeContentDraft = productContainer.getAppDraft().getStoreContentDraft();
        assertThat(storeContentDraft)
            .isNotNull()
            .extracting(StoreContentDraftModel::getCode)
            .isEqualTo(STORE_CONTENT_DRAFT_CODE_PREFIX + completeStoreContentData.getProductContainerCode());
        assertStoreContentDataIsUpdated(storeContentDraft);
        assertVideoDataIsReset(storeContentDraft);
    }

    private void assertVideoDataIsReset(StoreContentDraftModel storeContentDraft) {
        assertThat(storeContentDraft.getVideo().getSource()).isEqualTo(completeStoreContentData.getVideoUrl());
        assertThat(storeContentDraft.getVideo().getStatus()).isNull();
    }

    private void assertVideoDataIsNotReset(StoreContentDraftModel storeContentDraft) {
        assertThat(storeContentDraft.getVideo().getSource()).isEqualTo(completeStoreContentData.getVideoUrl());
        assertThat(storeContentDraft.getVideo().getStatus()).isEqualTo(completeStoreContentData.getVideoStatus());
    }

    private void assertStoreContentDataIsUpdated(StoreContentDraftModel storeContentDraft) {
        assertThat(storeContentDraft.getEmailAddress()).isEqualTo(completeStoreContentData.getEmailAddress());
        assertThat(storeContentDraft.getPrivacyPolicyUrl()).isEqualTo(completeStoreContentData.getPrivacyPolicyUrl());
        assertThat(storeContentDraft.getSupportPhoneNumber()).isEqualTo(completeStoreContentData.getSupportPhoneNumber());
        assertThat(storeContentDraft.getSupportPageUrl()).isEqualTo(completeStoreContentData.getSupportPageUrl());

        completeStoreContentData.getDescriptionByIsocode()
            .forEach((isocode, description) -> assertThat(storeContentDraft.getDescription(new Locale(isocode))).isEqualTo(description));
        completeStoreContentData.getNameByIsocode()
            .forEach((isocode, name) -> assertThat(storeContentDraft.getName(new Locale(isocode))).isEqualTo(name.trim()));

        assertThat(storeContentDraft.getIcon().getQualifier()).isEqualTo(clientImageData.getQualifier());

        List<MediaContainerModel> persistedScreenshots = storeContentDraft.getScreenshots();
        assertThat(persistedScreenshots).hasSize(1);
        assertThat(persistedScreenshots.get(0).getQualifier()).isEqualTo(SCREENSHOT_CONTAINER_QUALIFIER);
        Set<UseCaseModel> useCaseModels = storeContentDraft.getUseCases();
        assertThat(useCaseModels).isNotEmpty();
        assertThat(useCaseModels.iterator().next()).isEqualTo(useCaseModel);

        Set<IndustryModel> industries = storeContentDraft.getIndustries();
        assertThat(industries).isNotEmpty();
        IndustryModel persistedIndustry = industries.iterator().next();
        assertThat(industry).isEqualTo(persistedIndustry);
    }

    @Test
    public void validateDraft_givenExistingDraft_validationRuns() {
        ProductContainerModel productContainer = sampleDataCreator.createProductContainerWithAppDraft(APP_NAME,
            developerFromCompanyA);

        completeStoreContentData.setProductContainerCode(productContainer.getCode());
        createUploadedScreenshots();
        completeStoreContentData.setEula(new EulaData().withType(EulaType.STANDARD));

        completeStoreContentData.withAppIntegrations(List.of(
            new AppIntegrationData()
                .withType(AppIntegrationType.CUSTOM)
                .withName("Name too too too too too long!!!")
                .withCode(null)
                .withStatus(AppIntegrationStatus.ENABLED)
        ));

        Set<ErrorMessageData> results = storeContentFacade.validateDraft(completeStoreContentData);
        assertThat(results.stream()
            .filter(message -> DevconInputField.APP_INTEGRATIONS_NAME.getValue().equals(message.getInputField()))
        ).hasSize(1);
    }

    @Test
    public void createOrUpdateDraft_givenExistingDraft_modelIsUpdated() {
        ProductContainerModel productContainer = sampleDataCreator.createProductContainerWithAppDraft(APP_NAME,
            developerFromCompanyA);

        assertThat(productContainer.getAppDraft().getStoreContentDraft().getVideo().getSource()).isNotEmpty();

        Date originalModifiedTime = sampleDataCreator.ensureProductContainerModifiedTimeIsInThePast(productContainer);
        createUploadedScreenshots();
        completeStoreContentData.setUseCases(getUseCaseData());
        completeStoreContentData.setIndustries(getIndustryData());
        completeStoreContentData.withVideoUrl(productContainer.getAppDraft().getStoreContentDraft().getVideo().getSource());
        completeStoreContentData.withAppIntegrations(getAppIntegrationData());
        completeStoreContentData.setEula(new EulaData().withType(EulaType.CUSTOM).withCustomUrl("www.terms.com"));

        storeContentFacade.createOrUpdateDraft(completeStoreContentData);

        assertDraftIsUpdated(productContainer);

        modelService.refresh(productContainer);
        assertThat(productContainer.getModifiedtime()).as("Product container modification date should be updated")
            .isAfter(originalModifiedTime);
        assertThat(productContainer.getAppDraft().getStoreContentDraft().getEula().getType()).isEqualTo(EulaType.CUSTOM);
        assertThat(productContainer.getAppDraft().getStoreContentDraft().getEula().getCustomUrl()).isEqualTo("www.terms.com");
    }

    private static List<AppIntegrationData> getAppIntegrationData() {
        return List.of(
            new AppIntegrationData()
                .withType(AppIntegrationType.CUSTOM)
                .withName("My name")
                .withCode(null)
                .withStatus(AppIntegrationStatus.ENABLED),

            new AppIntegrationData()
                .withType(AppIntegrationType.CUSTOM)
                .withName("My name 2")
                .withCode("")
                .withStatus(AppIntegrationStatus.ENABLED),

            new AppIntegrationData()
                .withType(AppIntegrationType.STANDARD)
                .withStatus(AppIntegrationStatus.DISABLED)
                .withCode("doesn't exist")
        );
    }

    private void assertDraftIsUpdated(ProductContainerModel productContainer) {
        modelService.refresh(productContainer);

        assertThat(productContainer.getAppDraft()).isNotNull();
        modelService.refresh(productContainer.getAppDraft());

        StoreContentDraftModel storeContentDraft = productContainer.getAppDraft().getStoreContentDraft();
        assertThat(storeContentDraft).isNotNull();
        assertThat(storeContentDraft.getCode()).isEqualTo(STORE_CONTENT_DRAFT_PREFIX + APP_NAME);
        assertStoreContentDataIsUpdated(storeContentDraft);
        assertVideoDataIsNotReset(storeContentDraft);

        List<MediaContainerModel> persistedScreenshots = storeContentDraft.getScreenshots();
        assertThat(persistedScreenshots).hasSize(1);
        assertThat(persistedScreenshots.get(0).getQualifier()).isEqualTo(SCREENSHOT_CONTAINER_QUALIFIER);

        assertThat(storeContentDraft.getAppIntegrations()).hasSize(2);
        assertThat(storeContentDraft.getAppIntegrations().get(0).getName()).isEqualTo("My name");
        assertThat(storeContentDraft.getAppIntegrations().get(1).getName()).isEqualTo("My name 2");
    }

    @Test
    public void createOrUpdateDraft_incorrectCode_errorMessageIsAdded() {
        expectedException.expect(NotFoundException.class);
        storeContentFacade.createOrUpdateDraft((StoreContentData) completeStoreContentData.withProductContainerCode(INVALID_CODE));
    }

    @Test
    public void createOrUpdateDraft_productContainerIsNotDraft_errorMessageIsAdded() {
        expectedException.expect(NotFoundException.class);
        ProductContainerModel productContainer = sampleDataCreator.createProductContainerWithApp("myTestApp",
            developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID));

        storeContentFacade
            .createOrUpdateDraft((StoreContentData) completeStoreContentData.withProductContainerCode(productContainer.getCode()));
    }

    @Test
    public void updateStoreContentForApp_givenValidData_changesAreSyncedToOnlineCatalog() {
        userService.setCurrentUser(developerFromCompanyA);
        ProductContainerModel productContainer = sampleDataCreator.createProductContainerWithApp("myTestApp",
            developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID));
        createUploadedScreenshots();

        ClientImageData screenshotToBeKept = new ClientImageData().withQualifier(SCREENSHOT_CONTAINER_QUALIFIER);
        StoreContentData storeContentData = basicStoreContent()
            .withNameByIsocode(Map.of("de", "  " + NAME_GERMAN, "en", " " + APP_NAME + "  "))
            .withScreenshots(List.of(screenshotToBeKept))
            .withIcon(Collections.singletonList(clientImageData));
        storeContentData.setProductContainerCode(productContainer.getCode());

        storeContentFacade.updateStoreContentForApp(storeContentData);

        assertAppIsUpdated(storeContentData, getOnlineApp(productContainer));
    }

    private AppModel getOnlineApp(ProductContainerModel productContainer) {
        catalogVersionService.setSessionCatalogVersion(CIS_PRODUCT_CATALOG, ONLINE.getVersionName());
        CatalogVersionModel onlineCatalog = sampleDataCreator.getOrCreateProductCatalogVersion(CatalogVersion.ONLINE);
        AppModel searchExample = new AppModel();
        searchExample.setCode(productContainer.getApp().getCode());
        searchExample.setCatalogVersion(onlineCatalog);
        AppModel onlineApp;
        try {
            onlineApp = flexibleSearchService.getModelByExample(searchExample);
        } catch (ModelNotFoundException e) {
            throw new RuntimeException("App was not sync'd to online catalog", e);
        }
        return onlineApp;
    }

    private void assertAppIsUpdated(StoreContentData storeContentData, AppModel app) {
        assertTextFieldsUpdated(storeContentData, app);
        MediaContainerModel icon = app.getIcon();
        assertThat(icon).isNotNull().extracting(MediaContainerModel::getQualifier).isEqualTo(clientImageData.getQualifier());
        assertThat(icon.getMedias()).hasSize(1);
        assertThat(app.getGalleryImages()).extracting(MediaContainerModel::getQualifier).containsExactly(SCREENSHOT_CONTAINER_QUALIFIER);
    }

    private void assertTextFieldsUpdated(StoreContentData storeContentData, AppModel app) {
        assertThat(app.getName(GERMAN).trim()).isEqualTo(storeContentData.getNameByIsocode().get("de"));
        assertThat(app.getName(ENGLISH).trim()).isEqualTo(storeContentData.getNameByIsocode().get("en"));
        assertThat(app.getDescription(GERMAN)).isEqualTo(storeContentData.getDescriptionByIsocode().get("de"));
        assertThat(app.getDescription(ENGLISH)).isEqualTo(storeContentData.getDescriptionByIsocode().get("en"));
        assertThat(app.getEmailAddress()).isEqualTo(storeContentData.getEmailAddress());
        assertThat(app.getSupportPhoneNumber()).isEqualTo(storeContentData.getSupportPhoneNumber());
        assertThat(app.getSupportPageUrl()).isEqualTo(storeContentData.getSupportPageUrl());
        assertThat(app.getProductWebsiteUrl()).isEqualTo(storeContentData.getProductWebsiteUrl());
        assertThat(app.getPrivacyPolicyUrl()).isEqualTo(storeContentData.getPrivacyPolicyUrl());
        assertThat(app.getEula().getType()).isEqualTo(storeContentData.getEula().getType());
        assertThat(app.getEula().getCustomUrl()).isEqualTo(storeContentData.getEula().getCustomUrl());
    }

    @Test
    public void updateStoreContentForApp_givenInvalidCode_errorIsReturned() {
        expectedException.expect(NotFoundException.class);
        expectedException.expectMessage(PRODUCT_CONTAINER_NOT_FOUND_CODE);
        StoreContentData storeContentData = (StoreContentData) basicStoreContent().withProductContainerCode(INVALID_PRODUCT_CONTAINER_CODE);
        storeContentFacade.updateStoreContentForApp(storeContentData);
    }

    @Test
    public void createOrUpdateDraft_calledByDifferentUserFromSameCompany_succeeds() {
        ProductContainerModel productContainer = sampleDataCreator.createProductContainerWithAppDraft(APP_NAME,
            developerFromCompanyA);
        createUploadedScreenshots();
        userService.setCurrentUser(developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A2_UID));
        completeStoreContentData.setUseCases(getUseCaseData());
        completeStoreContentData.setIndustries(getIndustryData());
        completeStoreContentData.setAppIntegrations(getAppIntegrationData());
        completeStoreContentData.setEula(new EulaData().withType(EulaType.STANDARD));

        storeContentFacade.createOrUpdateDraft(completeStoreContentData);

        assertDraftIsUpdated(productContainer);
    }

    @Test
    public void createOrUpdateDraft_calledByUserFromDifferentCompany_fails() {
        expectedException.expect(NotFoundException.class);
        expectedException.expectMessage(PRODUCT_CONTAINER_NOT_FOUND_CODE);
        sampleDataCreator.createProductContainerWithAppDraft(APP_NAME, developerFromCompanyA);
        createUploadedScreenshots();
        userService.setCurrentUser(developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_B1_UID));

        storeContentFacade.createOrUpdateDraft(completeStoreContentData);
    }

    @Test
    public void updateStoreContentForApp_exceptionInTransaction_nothingIsPersisted() {
        ProductContainerModel productContainer = sampleDataCreator.createProductContainerWithApp("myTestApp",
            developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID));
        createUploadedScreenshots();

        AppModel app = productContainer.getApp();
        String originalGermanName = app.getName(GERMAN);
        String originalEnglishName = app.getName(ENGLISH);
        String originalGermanDescription = app.getDescription(GERMAN);
        String originalEnglishDescription = app.getDescription(ENGLISH);
        String originalSupportUrl = app.getSupportPageUrl();
        String originalEmail = app.getEmailAddress();
        String originalPhone = app.getSupportPhoneNumber();
        String originalPrivacyPolicy = app.getPrivacyPolicyUrl();
        List<byte[]> originalIconData = app.getIcon().getMedias().stream().map(mediaService::getDataFromMedia).collect(Collectors.toList());
        List<byte[]> originalGalleryImageData = app.getGalleryImages().stream().map(MediaContainerModel::getMedias)
            .flatMap(Collection::stream).map(mediaService::getDataFromMedia)
            .collect(Collectors.toList());

        StoreContentData storeContentData = basicStoreContent()
            .withDescriptionByIsocode(Map.of("invalid isocode", "some description"));
        storeContentData.setProductContainerCode(productContainer.getCode());
        storeContentData.withIcon(Collections.singletonList(new ClientImageData().withQualifier("TestIcon-container")));
        storeContentData.withScreenshots(Collections.singletonList(new ClientImageData().withQualifier("TestScreenshot-container")));

        expectedException.expect(BadRequestException.class);
        storeContentFacade.updateStoreContentForApp(storeContentData);

        modelService.refresh(app);

        assertThat(app.getName(GERMAN)).isNotEqualTo(storeContentData.getNameByIsocode().get("de")).isEqualTo(originalGermanName);
        assertThat(app.getName(ENGLISH)).isNotEqualTo(storeContentData.getNameByIsocode().get("en")).isEqualTo(originalEnglishName);
        assertThat(app.getDescription(GERMAN)).isNotEqualTo(storeContentData.getDescriptionByIsocode().get("de"))
            .isEqualTo(originalGermanDescription);
        assertThat(app.getDescription(ENGLISH)).isNotEqualTo(storeContentData.getDescriptionByIsocode().get("en"))
            .isEqualTo(originalEnglishDescription);
        assertThat(app.getSupportPageUrl()).isNotEqualTo(storeContentData.getSupportPageUrl())
            .isEqualTo(originalSupportUrl);
        assertThat(app.getEmailAddress()).isNotEqualTo(storeContentData.getEmailAddress()).isEqualTo(originalEmail);
        assertThat(app.getSupportPhoneNumber()).isNotEqualTo(storeContentData.getSupportPhoneNumber()).isEqualTo(originalPhone);
        assertThat(app.getPrivacyPolicyUrl()).isNotEqualTo(storeContentData.getPrivacyPolicyUrl()).isEqualTo(originalPrivacyPolicy);
        assertThat(app.getIcon())
            .extracting(mediaContainer -> mediaService.getDataFromMedia(mediaContainer.getMedias().iterator().next()))
            .as("Logo not saved properly")
            .isIn(originalIconData);
        assertThat(app.getGalleryImages()).flatExtracting(MediaContainerModel::getMedias).extracting(mediaService::getDataFromMedia)
            .as("Screenshot not saved properly").isNotEmpty().containsExactlyElementsOf(originalGalleryImageData);
    }

    @Test
    public void addScreenshotForProductContainer_screenshotIsUploaded_correctQualifiersAreReturned() {
        ProductContainerModel productContainer = sampleDataCreator.createProductContainerWithApp("myTestApp",
            developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID));
        Collection<MultipartFile> screenshotFiles = ImmutableList
            .of(new MockMultipartFile("screenshot1", sampleDataCreator.getImageTestData()),
                new MockMultipartFile("screenshot2", sampleDataCreator.getAlternativeImageTestData()));

        Optional<MediaUploadData> screenshotUploadData = storeContentFacade.persistScreenshots(screenshotFiles, productContainer.getCode());
        assertThat(screenshotUploadData).isNotEmpty();
        List<ClientImageData> persistedScreenshots = screenshotUploadData.get().getPersistedMedia();
        assertThat(persistedScreenshots).hasSize(NUMBER_UPLOADED_SCREENSHOTS);

        CatalogVersionModel stagedProductCatalog = catalogVersionService.getCatalogVersion(CIS_PRODUCT_CATALOG, STAGED.getVersionName());
        List<MediaContainerModel> mediaContainers = persistedScreenshots.stream()
            .map(ClientImageData::getQualifier)
            .map(qualifier -> getMediaContainerForQualifier(qualifier, stagedProductCatalog))
            .collect(Collectors.toList());
        List<byte[]> screenshots = mediaContainers.stream()
            .map(MediaContainerModel::getMedias)
            .flatMap(s -> getScreenshotsFromCatalog(stagedProductCatalog, s))
            .collect(Collectors.toList());

        assertThat(mediaContainers).extracting(ItemModel::getOwner).allMatch(s -> s.equals(productContainer));
        assertThat(screenshots).hasSize(4)
            .containsAll(Lists.newArrayList(sampleDataCreator.getImageTestData(), sampleDataCreator.getAlternativeImageTestData()));
    }

    @Test
    public void addIconForProductContainer_iconIsPersisted_correctQualifiersAreReturned() {
        ProductContainerModel productContainer = sampleDataCreator.createProductContainerWithApp("myTestApp",
            developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID));
        MultipartFile icon = new MockMultipartFile("screenshot1", sampleDataCreator.getImageTestData());

        Optional<MediaUploadData> mediaUploadData = storeContentFacade.persistIcon(icon, productContainer.getCode());
        assertThat(mediaUploadData).isNotEmpty();
        List<ClientImageData> persistedMedia = mediaUploadData.get().getPersistedMedia();
        assertThat(persistedMedia).hasSize(NUMBER_UPLOADED_ICON);
        MediaContainerModel mediaContainer = cisMediaContainerService
            .getStagedMediaContainerForQualifier(persistedMedia.get(0).getQualifier());
        assertThat(mediaContainer.getOwner()).isEqualTo(productContainer);
        assertThat(mediaContainer.getMaster().getOwner()).isEqualTo(productContainer);
        assertThat(mediaContainer.getQualifier()).contains(productContainer.getCode());
    }

    private Stream<byte[]> getScreenshotsFromCatalog(CatalogVersionModel stagedProductCatalog, Collection<MediaModel> s) {
        return s.stream()
            .filter(t -> t.getCatalogVersion().equals(stagedProductCatalog))
            .map(mediaService::getDataFromMedia);
    }

    private MediaContainerModel getMediaContainerForQualifier(String qualifier, CatalogVersionModel catalog) {
        MediaContainerModel mediaContainer = new MediaContainerModel();
        mediaContainer.setQualifier(qualifier);
        mediaContainer.setCatalogVersion(catalog);
        return flexibleSearchService.getModelByExample(mediaContainer);
    }

    @Test
    public void updateStoreContentForApp_givenValidData_updatesProductContainerModifiedTime() {
        ProductContainerModel productContainer = sampleDataCreator.createProductContainerWithApp("myTestApp",
            developerService.getDeveloperByInternalUserId(SAMPLE_DATA_DEVELOPER_A1_UID));
        Date originalContainerChangeDate = sampleDataCreator.ensureProductContainerModifiedTimeIsInThePast(productContainer);

        StoreContentData storeContentData = basicStoreContent();
        storeContentData.withProductContainerCode(productContainer.getCode());

        storeContentFacade.updateStoreContentForApp(storeContentData);

        modelService.refresh(productContainer);
        assertThat(productContainer.getModifiedtime()).as("Product container modification date should be updated")
            .isAfter(originalContainerChangeDate);
    }

    private List<UseCaseData> getUseCaseData() {
        return ImmutableList.of(new UseCaseData()
            .withId(useCaseModel.getPk().getLongValue())
            .withName(useCaseModel.getName()));
    }

    private List<IndustryData> getIndustryData() {
        return ImmutableList.of(new IndustryData()
            .withId(industry.getPk().getLongValue())
            .withName(industry.getName()));
    }

    private static <A, B> Map<A, B> mutableCopyOf(Map<A, B> immutableMap) {
        return new HashMap<>(immutableMap);
    }
}
