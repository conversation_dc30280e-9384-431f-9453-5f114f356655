package com.sast.cis.core.products.availability.job;

import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.DeveloperModel;
import com.sast.cis.core.service.customer.developer.DeveloperService;
import com.sast.cis.test.utils.SampleDataCreator;
import com.sast.cis.test.utils.SessionCatalogRule;
import com.sast.cis.test.utils.TestLogListener;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.core.model.user.UserGroupModel;
import de.hybris.platform.servicelayer.ServicelayerTransactionalTest;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.util.logging.HybrisLogger;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;

import javax.annotation.Resource;

import static com.sast.cis.core.dao.CatalogVersion.STAGED;
import static com.sast.cis.core.enums.StoreAvailabilityMode.PUBLIC;
import static com.sast.cis.core.enums.StoreAvailabilityMode.RESTRICTED_BUYER_GROUP;
import static com.sast.cis.test.utils.TestDataConstants.AA_AUSTRIA1_COMPANY_DEVELOPER_UID;
import static com.sast.cis.test.utils.TestDataConstants.AA_PRODUCT_CATALOG;
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.APPROVED;
import static java.util.Set.of;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
public class AvailabilityPriceVerificationProcessorITest extends ServicelayerTransactionalTest {

    @Rule
    public final SessionCatalogRule sessionCatalogRule = SessionCatalogRule.ofCatalog(AA_PRODUCT_CATALOG, STAGED);

    @Resource
    private DeveloperService developerService;

    @Resource
    private ModelService modelService;

    @Resource
    private AvailabilityPriceVerificationProcessor availabilityPriceVerificationProcessor;

    private final SampleDataCreator sampleDataCreator = new SampleDataCreator();
    private final TestLogListener listener = new TestLogListener();

    private AppModel app;
    private AppLicenseModel appLicense;
    private UserGroupModel bdcUserGroup;

    @Before
    public void setup() {
        final DeveloperModel developer = developerService.getDeveloperByInternalUserId(AA_AUSTRIA1_COMPANY_DEVELOPER_UID);
        bdcUserGroup = sampleDataCreator.getUserGroup("BDC000");

        app = sampleDataCreator.createApp(
            "AA_1234_r", "AvailabilityPriceVerificationProcessorITest", developer, AA_PRODUCT_CATALOG, STAGED, APPROVED
        );
        app.setStoreAvailabilityMode(RESTRICTED_BUYER_GROUP);

        appLicense = sampleDataCreator.createFullAppLicense(app);
        appLicense.setUserGroups(of(bdcUserGroup));

        modelService.saveAll(app, appLicense);
        modelService.refresh(app);

        HybrisLogger.addListener(listener);
    }

    @After
    public void tearDown() {
        HybrisLogger.removeListener(listener);
    }

    @Test
    public void givenAvailabilityAndNoPrice_whenVerifyForCurrentCatalog_thenLogAlert() {
        // The product is available to the user group but no price is set.
        availabilityPriceVerificationProcessor.verifyForCurrentCatalog();

        assertThat(listener.getLog()).anySatisfy(logEvent ->
            assertThat(logEvent.getRenderedMessage())
                .isEqualTo(
                    "ALERT: License is available to group but does not have a valid price for it. License code: %s, group UID: %s"
                        .formatted(appLicense.getCode(), bdcUserGroup.getUid())
                )
        );
    }

    @Test
    public void givenAppIsNotGroupRestricted_whenVerifyForCurrentCatalog_thenDoNotLogAlert() {
        app.setStoreAvailabilityMode(PUBLIC);

        availabilityPriceVerificationProcessor.verifyForCurrentCatalog();

        assertThat(listener.getLog()).noneSatisfy(logEvent ->
            assertThat(logEvent.getRenderedMessage()).startsWith("ALERT:")
        );
    }
}
