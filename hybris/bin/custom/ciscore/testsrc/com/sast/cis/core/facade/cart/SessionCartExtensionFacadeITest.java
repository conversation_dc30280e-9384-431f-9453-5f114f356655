package com.sast.cis.core.facade.cart;

import com.sast.cis.core.BaseIntegrationTest;
import com.sast.cis.core.config.keycloak.SiteUmpAdapterConfigResolutionService;
import com.sast.cis.core.dao.CatalogVersion;
import com.sast.cis.core.data.EulaAcceptanceData;
import com.sast.cis.core.data.LicensePurchaseData;
import com.sast.cis.core.data.PlaceOrderData;
import com.sast.cis.core.enums.Feature;
import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.enums.PspSellerAccountStatus;
import com.sast.cis.core.exceptions.web.BadRequestException;
import com.sast.cis.core.facade.CisCartFacade;
import com.sast.cis.core.facade.cart.eula.EulaNotAcceptedException;
import com.sast.cis.core.factory.CisCartFactory;
import com.sast.cis.core.model.*;
import com.sast.cis.core.service.company.IotCompanyService;
import com.sast.cis.test.utils.FeatureToggleRule;
import com.sast.cis.test.utils.LoginUtil;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.catalog.CatalogVersionService;
import de.hybris.platform.catalog.model.CatalogVersionModel;
import de.hybris.platform.core.model.order.CartModel;
import de.hybris.platform.order.CartService;
import de.hybris.platform.servicelayer.user.UserService;
import de.hybris.platform.servicelayer.user.daos.UserGroupDao;
import de.hybris.platform.site.BaseSiteService;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static com.sast.cis.core.constants.CiscoreConstants.CIS_PRODUCT_CATALOG;
import static com.sast.cis.core.constants.CiscoreConstants.IOT_STORE_BASE_SITE_UID;
import static com.sast.cis.core.constants.shop.ShopErrorCode.EULA_NOT_ACCEPTED;
import static com.sast.cis.core.dao.CatalogVersion.ONLINE;
import static com.sast.cis.core.util.TestCartUtility.createValidLicenseQuantityDataList;
import static com.sast.cis.test.utils.SampleDataCreator.COUNTRY_EULA_ESI_DEFAULT;
import static com.sast.cis.test.utils.TestDataConstants.DEMO_COMPANY_INTEGRATOR_UID;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@IntegrationTest
public class SessionCartExtensionFacadeITest extends BaseIntegrationTest {

    private static final String SELLER_COMPANY_UID = "integrationtestCompanyA";

    @Rule
    public FeatureToggleRule featureToggleRule = new FeatureToggleRule();

    @Resource
    private CartService cartService;

    @Resource
    private CisCartFacade cisCartFacade;

    @Resource
    private IotCompanyService iotCompanyService;

    @Resource
    private SiteUmpAdapterConfigResolutionService siteUmpAdapterConfigResolutionService;

    @Resource
    private RestTemplate cisRestTemplate;

    @Resource
    private UserService userService;

    @Resource
    private BaseSiteService baseSiteService;

    @Resource
    private CatalogVersionService catalogVersionService;

    @Resource
    private CisCartFactory cisCartFactory;

    @Resource
    private SessionCartExtensionFacade sessionCartExtensionFacade;

    private AppLicenseModel productWithEulaContainer;

    @Resource
    private UserGroupDao userGroupDao;

    @Before
    public void setup() throws Exception {

        LoginUtil loginUtil = new LoginUtil(siteUmpAdapterConfigResolutionService, cisRestTemplate);
        SecurityContextHolder.getContext().setAuthentication(loginUtil.generateKeycloakAuthenticationObject());
        userService.setCurrentUser(userService.getUserForUID(DEMO_COMPANY_INTEGRATOR_UID));
        baseSiteService.setCurrentBaseSite(baseSiteService.getBaseSiteForUID(IOT_STORE_BASE_SITE_UID), false);

        IoTCompanyModel sellerCompany = iotCompanyService.getCompanyByUid(SELLER_COMPANY_UID).orElseThrow();
        sampleDataCreator.createDpgSellerAccount(sellerCompany);

        final CatalogVersionModel catalogVersion = catalogVersionService.getCatalogVersion(CIS_PRODUCT_CATALOG, ONLINE.getVersionName());
        catalogVersionService.setSessionCatalogVersions(Collections.singletonList(catalogVersion));

        final CartModel cart = cisCartFactory.createCart();
        cart.getCompany().setAaCustomerGroup(userGroupDao.findUserGroupByUid("IDW000"));
        cartService.setSessionCart(cart);
        cisCartFacade.addProductsToCart(new LicensePurchaseData().withLicenses(createValidLicenseQuantityDataList(1, 0, "HW1210-3423", "my.sample.app")));

        final EulaContainerModel eulaContainer = sampleDataCreator.createEulaContainer();
        final AppModel app = sampleDataCreator.createApp("app_with_eula", "app.with.eula", CatalogVersion.ONLINE);
        app.setEulaContainers(Set.of(eulaContainer));
        productWithEulaContainer = sampleDataCreator.createFullAppLicense(app);
    }

    @Test
    public void extendSessionCart_invoiceNotesArePersisted() {
        sessionCartExtensionFacade.extendSessionCart(new PlaceOrderData().withInvoiceNotes(List.of("Test 1", "Test 2")));

        CartModel sessionCart = cartService.getSessionCart();
        assertThat(sessionCart.getFirstInvoiceNote()).isEqualTo("Test 1");
        assertThat(sessionCart.getSecondInvoiceNote()).isEqualTo("Test 2");
    }

    @Test
    @SneakyThrows
    public void givenItemWithEula_whenExtendSessionCart_thenEulaAcceptanceIsPersisted() {
        cisCartFacade.addProductsToCart(new LicensePurchaseData().withLicenses(createValidLicenseQuantityDataList(productWithEulaContainer)));

        final PlaceOrderData placeOrderData = new PlaceOrderData().withEulaAcceptanceData(new EulaAcceptanceData().withEulaAccepted(true));
        sessionCartExtensionFacade.extendSessionCart(placeOrderData);

        final CartModel sessionCart = cartService.getSessionCart();
        final EulaAcceptanceModel eulaAcceptance = sessionCart.getEulaAcceptance();
        assertThat(eulaAcceptance).isNotNull();
        assertThat(eulaAcceptance.getAcceptedEulasUrls()).containsExactlyInAnyOrder(COUNTRY_EULA_ESI_DEFAULT);
    }

    @Test
    @SneakyThrows
    public void givenItemWithEulaAndEulaNotAccepted_whenExtendSessionCart_thenEulaAcceptanceIsPersisted() {
        cisCartFacade.addProductsToCart(new LicensePurchaseData().withLicenses(createValidLicenseQuantityDataList(productWithEulaContainer)));

        final PlaceOrderData placeOrderData = new PlaceOrderData().withEulaAcceptanceData(new EulaAcceptanceData().withEulaAccepted(false));

        assertThatThrownBy(() -> sessionCartExtensionFacade.extendSessionCart(placeOrderData))
            .isInstanceOf(EulaNotAcceptedException.class)
            .hasFieldOrPropertyWithValue("code", EULA_NOT_ACCEPTED);
    }
}
