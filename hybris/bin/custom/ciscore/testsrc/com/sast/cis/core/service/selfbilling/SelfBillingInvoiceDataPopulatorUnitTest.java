package com.sast.cis.core.service.selfbilling;

import com.sast.cis.core.converter.PriceDataConverter;
import com.sast.cis.core.data.BasicAppData;
import com.sast.cis.core.data.SBIData;
import com.sast.cis.core.data.SelfBillingInvoiceData;
import com.sast.cis.core.enums.SelfBillingInvoiceStatus;
import com.sast.cis.core.factory.CisPriceDataFactory;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.model.SelfBillingInvoiceModel;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.product.data.PriceData;
import de.hybris.platform.commercefacades.product.data.PriceDataType;
import de.hybris.platform.core.model.c2l.CurrencyModel;
import de.hybris.platform.core.model.order.OrderEntryModel;
import de.hybris.platform.core.model.order.OrderModel;
import de.hybris.platform.servicelayer.dto.converter.Converter;
import generated.com.sast.cis.core.model.IoTCompanyBuilder;
import generated.com.sast.cis.core.model.SelfBillingInvoiceBuilder;
import generated.de.hybris.platform.core.model.c2l.CurrencyBuilder;
import generated.de.hybris.platform.core.model.order.OrderBuilder;
import generated.de.hybris.platform.core.model.order.OrderEntryBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;
import static org.mockito.ArgumentMatchers.eq;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class SelfBillingInvoiceDataPopulatorUnitTest {
    private static final String ORDER_CODE = "O1234";
    private static final Date SBI_INVOICE_DATE = new Date();
    private static final Double TOTAL_PRICE = 1.00;
    private static final Double TOTAL_TAX = 0.19;
    private static final String SBI_ID = "123456789012345678";
    private static final String SBI_URL = "http://best-self-billing-invoice.com";
    private static final String CUSTOMER_DISPLAY_NAME = "Best Integrator";
    private static final String CUSTOMER_NAME = "Best Integrator Inc.";
    @Mock
    private PriceDataConverter priceDataConverter;
    @Mock
    private Converter<SelfBillingInvoiceModel, SelfBillingInvoiceData> selfBillingInvoiceConverter;
    @Mock
    private Converter<AppLicenseModel, BasicAppData> basicAppDataConverter;
    @Mock
    private CisPriceDataFactory priceDataFactory;
    @Mock
    private IoTCompanyModel company;
    @Mock
    private BasicAppData basicAppData;
    @Mock
    private AppLicenseModel appLicense;
    @Mock
    private com.sast.cis.core.data.PriceData priceData;

    private SelfBillingInvoiceDataPopulator selfBillingInvoiceDataPopulator;
    private SelfBillingInvoiceModel selfBillingInvoice;

    @Before
    public void setUp() {
        selfBillingInvoiceDataPopulator = new SelfBillingInvoiceDataPopulator(priceDataConverter,
            basicAppDataConverter, priceDataFactory, selfBillingInvoiceConverter);

        CurrencyModel currency = CurrencyBuilder.generate().buildMockInstance();
        OrderEntryModel orderEntry = OrderEntryBuilder.generate().withProduct(appLicense).buildMockInstance();
        company = IoTCompanyBuilder.generate().withName(CUSTOMER_NAME).withFriendlyName(CUSTOMER_DISPLAY_NAME).buildMockInstance();
        OrderModel order = OrderBuilder.generate().withCode(ORDER_CODE).withCompany(company)
            .withTotalPrice(TOTAL_PRICE)
            .withNet(true)
            .withCurrency(currency)
            .withEntries(List.of(orderEntry))
            .buildMockInstance();

        selfBillingInvoice = SelfBillingInvoiceBuilder.generate()
            .withInvoiceDate(SBI_INVOICE_DATE)
            .withOrder(order)
            .withStatus(SelfBillingInvoiceStatus.COMPLETED)
            .buildMockInstance();

        when(basicAppDataConverter.convertAll(any())).thenReturn(List.of(basicAppData));
        var selfBillingInvoiceData = new SelfBillingInvoiceData().withUrl(SBI_URL).withDisplayName(SBI_ID);
        when(selfBillingInvoiceConverter.convert(selfBillingInvoice)).thenReturn(selfBillingInvoiceData);
        PriceData price = new PriceData();
        when(priceDataFactory.create(any(PriceDataType.class), any(BigDecimal.class), eq(currency))).thenReturn(price);
        when(priceDataConverter.toPriceData(price)).thenReturn(priceData);
    }

    @Test
    public void populate_sbiData() {
        var sbiData = new SBIData();
        selfBillingInvoiceDataPopulator.populate(selfBillingInvoice, sbiData);
        SBIData expectedRevenueData = getExpectedSbiData(SelfBillingInvoiceStatus.COMPLETED.getCode());
        assertThat(sbiData).usingRecursiveComparison().isEqualTo(expectedRevenueData);
    }
    @Test
    public void populate_noPaymentStatus_sbiDataWithNullPaymentStatus() {
        when(selfBillingInvoice.getStatus()).thenReturn(null);
        var sbiData = new SBIData();
        selfBillingInvoiceDataPopulator.populate(selfBillingInvoice, sbiData);
        SBIData expectedRevenueData = getExpectedSbiData(null);
        assertThat(sbiData).usingRecursiveComparison().isEqualTo(expectedRevenueData);
    }

    private SBIData getExpectedSbiData(String invoiceStatus) {
        var sbiData = new SBIData();
        sbiData.setDate(SBI_INVOICE_DATE);
        sbiData.setOrderId(ORDER_CODE);
        sbiData.setCustomer(CUSTOMER_NAME);
        sbiData.setPaymentStatus(invoiceStatus);
        sbiData.setInvoiceUrl(SBI_URL);
        sbiData.setApps(List.of(basicAppData));
        sbiData.setTotalPrice(priceData);
        return sbiData;
    }

}
