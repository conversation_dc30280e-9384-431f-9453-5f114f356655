package com.sast.cis.core.converter;

import com.sast.cis.core.enums.StoreAvailabilityMode;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.service.AppService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.product.ProductService;
import de.hybris.platform.servicelayer.exceptions.UnknownIdentifierException;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.variants.model.VariantProductModel;
import de.hybris.platform.variants.model.VariantTypeModel;
import generated.com.sast.cis.core.model.AppBuilder;
import generated.de.hybris.platform.core.model.product.ProductBuilder;
import generated.de.hybris.platform.variants.model.VariantProductBuilder;
import generated.de.hybris.platform.variants.model.VariantTypeBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.APPROVED;
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.UNAPPROVED;
import static de.hybris.platform.core.model.product.ProductModel.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CisProductBasicPopulatorUnitTest {

    private static final String PRODUCT_VARIANT_TYPE_CODE = "TestProductVariantCode";
    private static final String PRODUCT_NAME = "Test product";
    private static final String PRODUCT_MANUFACTURER_NAME = "Test product manufacturer name";
    private static final String BASE_PRODUCT_CODE = "TestBaseProductCode";
    private static final String VARIANT_TYPE_CODE = "TestVariantTypeCode";
    private static final Double AVERAGE_RATING = 3.5;
    private static final String EXCEPTION_MESSAGE = "test";
    private static final int PRODUCT_ORDER = 1;

    @Mock
    private ModelService modelService;

    @Mock
    private ProductService productService;

    @Mock
    private AppService appService;

    @InjectMocks
    private CisProductBasicPopulator<ProductModel, ProductData> cisProductBasicPopulator;

    @Test
    public void populate_withVariantProduct_correctlyPopulatedAndPurchasableIsFalse() {
        VariantTypeModel variantType = VariantTypeBuilder.generate().withCode(VARIANT_TYPE_CODE).buildMockInstance();
        ProductModel baseProduct = getDefaultBaseProduct().buildMockInstance();
        VariantProductModel product = getDefaultVariantProduct(variantType, baseProduct).buildMockInstance();

        when(product.getAverageRating()).thenReturn(AVERAGE_RATING);
        when(modelService.getAttributeValue(product, NAME)).thenReturn(PRODUCT_NAME);
        when(modelService.getAttributeValue(product, MANUFACTURERNAME)).thenReturn(PRODUCT_MANUFACTURER_NAME);
        when(productService.getProductForCode(PRODUCT_VARIANT_TYPE_CODE)).thenThrow(new UnknownIdentifierException(EXCEPTION_MESSAGE));

        AppModel app = getApp(product).buildMockInstance();
        when(appService.getAppFromProduct(eq(product))).thenReturn(Optional.of(app));

        ProductData actualResult = new ProductData();
        cisProductBasicPopulator.populate(product, actualResult);

        ProductData expectedResult = getDefaultExpectedResult().withBaseProduct(BASE_PRODUCT_CODE);

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withVariantProductAndBaseProductIsNotPresent_correctlyPopulatedAndPurchasableIsFalse() {
        VariantTypeModel variantType = VariantTypeBuilder.generate().withCode(VARIANT_TYPE_CODE).buildMockInstance();
        VariantProductModel product = getDefaultVariantProduct(variantType, null).buildMockInstance();

        when(product.getAverageRating()).thenReturn(AVERAGE_RATING);
        when(modelService.getAttributeValue(product, NAME)).thenReturn(PRODUCT_NAME);
        when(modelService.getAttributeValue(product, MANUFACTURERNAME)).thenReturn(PRODUCT_MANUFACTURER_NAME);
        when(productService.getProductForCode(PRODUCT_VARIANT_TYPE_CODE)).thenThrow(new UnknownIdentifierException(EXCEPTION_MESSAGE));

        AppModel app = getApp(product).buildMockInstance();
        when(appService.getAppFromProduct(eq(product))).thenReturn(Optional.of(app));

        ProductData actualResult = new ProductData();
        cisProductBasicPopulator.populate(product, actualResult);

        ProductData expectedResult = getDefaultExpectedResult();

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withProduct_correctlyPopulatedAndPurchasableIsFalse() {
        VariantTypeModel variantType = VariantTypeBuilder.generate().withCode(VARIANT_TYPE_CODE).buildMockInstance();
        ProductModel product = getDefaultProduct(variantType).buildMockInstance();

        when(product.getAverageRating()).thenReturn(AVERAGE_RATING);
        when(modelService.getAttributeValue(product, NAME)).thenReturn(PRODUCT_NAME);
        when(modelService.getAttributeValue(product, MANUFACTURERNAME)).thenReturn(PRODUCT_MANUFACTURER_NAME);
        when(productService.getProductForCode(PRODUCT_VARIANT_TYPE_CODE)).thenThrow(new UnknownIdentifierException(EXCEPTION_MESSAGE));

        AppModel app = getApp(product).buildMockInstance();
        when(appService.getAppFromProduct(eq(product))).thenReturn(Optional.of(app));

        ProductData actualResult = new ProductData();
        cisProductBasicPopulator.populate(product, actualResult);

        ProductData expectedResult = getDefaultExpectedResult();

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withProductAndAverageRatingIsNotSet_correctlyPopulatedAndPurchasableIsFalse() {
        VariantTypeModel variantType = VariantTypeBuilder.generate().withCode(VARIANT_TYPE_CODE).buildMockInstance();
        ProductModel product = getDefaultProduct(variantType).buildMockInstance();

        when(product.getAverageRating()).thenReturn(null);
        when(modelService.getAttributeValue(product, NAME)).thenReturn(PRODUCT_NAME);
        when(modelService.getAttributeValue(product, MANUFACTURERNAME)).thenReturn(PRODUCT_MANUFACTURER_NAME);
        when(productService.getProductForCode(PRODUCT_VARIANT_TYPE_CODE)).thenThrow(new UnknownIdentifierException(EXCEPTION_MESSAGE));

        AppModel app = getApp(product).buildMockInstance();
        when(appService.getAppFromProduct(eq(product))).thenReturn(Optional.of(app));

        ProductData actualResult = new ProductData();
        cisProductBasicPopulator.populate(product, actualResult);

        ProductData expectedResult = getDefaultExpectedResult().withAverageRating(null);

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withProductAndVariantTypeIsNotSet_correctlyPopulatedAndPurchasableIsFalse() {
        ProductModel product = getDefaultProduct(null).buildMockInstance();

        when(product.getAverageRating()).thenReturn(AVERAGE_RATING);
        when(modelService.getAttributeValue(product, NAME)).thenReturn(PRODUCT_NAME);
        when(modelService.getAttributeValue(product, MANUFACTURERNAME)).thenReturn(PRODUCT_MANUFACTURER_NAME);
        when(productService.getProductForCode(PRODUCT_VARIANT_TYPE_CODE)).thenThrow(new UnknownIdentifierException(EXCEPTION_MESSAGE));

        AppModel app = getApp(product).buildMockInstance();
        when(appService.getAppFromProduct(eq(product))).thenReturn(Optional.of(app));

        ProductData actualResult = new ProductData();
        cisProductBasicPopulator.populate(product, actualResult);

        ProductData expectedResult = getDefaultExpectedResult().withVariantType(null);

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withProduct_correctlyPopulatedAndPurchasableIsTrue() {
        ProductModel product = getDefaultProduct(null).withApprovalStatus(APPROVED).buildMockInstance();

        when(product.getAverageRating()).thenReturn(AVERAGE_RATING);
        when(modelService.getAttributeValue(product, NAME)).thenReturn(PRODUCT_NAME);
        when(modelService.getAttributeValue(product, MANUFACTURERNAME)).thenReturn(PRODUCT_MANUFACTURER_NAME);
        when(productService.getProductForCode(PRODUCT_VARIANT_TYPE_CODE)).thenReturn(product);

        AppModel app = getApp(product).buildMockInstance();
        when(appService.getAppFromProduct(eq(product))).thenReturn(Optional.of(app));

        ProductData actualResult = new ProductData();
        cisProductBasicPopulator.populate(product, actualResult);

        ProductData expectedResult = getDefaultExpectedResult().withVariantType(null).withPurchasable(true);

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    private VariantProductBuilder<?, ?> getDefaultVariantProduct(VariantTypeModel variantType, ProductModel baseProduct) {
        return VariantProductBuilder.generate()
            .withCode(PRODUCT_VARIANT_TYPE_CODE)
            .withBaseProduct(baseProduct)
            .withVariantType(variantType)
            .withApprovalStatus(UNAPPROVED)
            .withOrder(PRODUCT_ORDER);
    }

    private ProductBuilder<?, ?> getDefaultProduct(VariantTypeModel variantType) {
        return ProductBuilder.generate()
            .withCode(PRODUCT_VARIANT_TYPE_CODE)
            .withVariantType(variantType)
            .withApprovalStatus(UNAPPROVED)
            .withOrder(PRODUCT_ORDER);
    }

    private ProductBuilder<?, ?> getDefaultBaseProduct() {
        return ProductBuilder.generate()
            .withCode(BASE_PRODUCT_CODE);
    }

    private AppBuilder<?, ?> getApp(ProductModel product) {
        return AppBuilder.generate()
            .withCode(product.getCode())
            .withStoreAvailabilityMode(StoreAvailabilityMode.PUBLIC)
            .withOrder(PRODUCT_ORDER);
    }

    private ProductData getDefaultExpectedResult() {
        return new ProductData()
            .withName(PRODUCT_NAME)
            .withManufacturer(PRODUCT_MANUFACTURER_NAME)
            .withAverageRating(AVERAGE_RATING)
            .withVariantType(VARIANT_TYPE_CODE)
            .withPurchasable(false)
            .withStoreAvailabilityMode(StoreAvailabilityMode.PUBLIC)
            .withOrder(PRODUCT_ORDER);
    }

}
