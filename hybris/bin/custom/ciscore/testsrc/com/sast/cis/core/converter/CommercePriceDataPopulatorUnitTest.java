package com.sast.cis.core.converter;

import com.sast.cis.core.data.ScalePriceInfo;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.product.data.PriceData;
import de.hybris.platform.commercefacades.product.data.PriceDataType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CommercePriceDataPopulatorUnitTest {

    @InjectMocks
    private CommercePriceDataPopulator commercePriceDataPopulator;

    @Test
    public void populate_populatesAllFieldsCorrectly() {
        PriceData priceData = new PriceData()
            .withCurrencyIso("EUR")
            .withFormattedValue("140.00")
            .withMaxQuantity(4l)
            .withMinQuantity(3l)
            .withValue(BigDecimal.valueOf(140l))
            .withPriceType(PriceDataType.BUY);
        ScalePriceInfo scalePriceInfo = new ScalePriceInfo();

        commercePriceDataPopulator.populate(priceData, scalePriceInfo);

        assertThat(scalePriceInfo).usingRecursiveComparison().isEqualTo(priceData);
    }
}
