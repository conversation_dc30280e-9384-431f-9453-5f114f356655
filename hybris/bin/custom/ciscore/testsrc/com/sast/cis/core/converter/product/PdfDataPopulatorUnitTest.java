package com.sast.cis.core.converter.product;

import com.sast.cis.core.data.PdfData;
import com.sast.cis.core.model.*;
import de.hybris.bootstrap.annotations.UnitTest;
import generated.com.sast.cis.core.model.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.assertj.core.api.Assertions.assertThat;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class PdfDataPopulatorUnitTest {

    private static final String PDF_CODE = "testPdfCode";
    private static final String PDF_ALT_TEXT = "Test alt text";
    private static final Long PDF_SIZE = 256L;

    @InjectMocks
    private PdfDataPopulator populator;

    @Test
    public void populate_WithApp_properlyPopulated() {
        PdfMediaModel pdfData = PdfMediaBuilder.generate()
            .withCode(PDF_CODE)
            .withAltText(PDF_ALT_TEXT)
            .withSize(PDF_SIZE)
            .buildMockInstance();

        PdfData actualResult = new PdfData();
        populator.populate(pdfData, actualResult);

        PdfData expectedResult = new PdfData()
            .withCode(PDF_CODE)
            .withDisplayName(PDF_ALT_TEXT)
            .withUrl("/app/manuals/"+PDF_CODE)
            .withSize(PDF_SIZE);

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }
}
