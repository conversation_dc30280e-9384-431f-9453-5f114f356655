package com.sast.cis.core.country.sync.service;

import com.sast.cis.core.constants.BaseStoreEnum;
import com.sast.cis.core.enums.LicenseType;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.CountryStoreConfigurationModel;
import com.sast.cis.core.service.AppLicenseService;
import com.sast.cis.core.service.AppService;
import com.sast.cis.core.service.country.CountryStoreConfigurationService;
import com.sast.cis.test.utils.SessionCatalogRule;
import de.hybris.bootstrap.annotations.IntegrationTest;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.core.model.c2l.LanguageModel;
import de.hybris.platform.servicelayer.ServicelayerTest;
import de.hybris.platform.servicelayer.i18n.CommonI18NService;
import de.hybris.platform.servicelayer.model.ModelService;
import de.hybris.platform.store.BaseStoreModel;
import de.hybris.platform.store.services.BaseStoreService;
import de.hybris.platform.testframework.HybrisJUnit4ClassRunner;
import de.hybris.platform.testframework.RunListeners;
import de.hybris.platform.testframework.runlistener.ItemCreationListener;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.sast.cis.core.dao.CatalogVersion.STAGED;
import static com.sast.cis.core.enums.PaymentMethodType.CREDIT_CARD;
import static com.sast.cis.core.enums.PaymentMethodType.SEPA_CREDIT;
import static com.sast.cis.core.enums.PaymentProvider.DPG;
import static com.sast.cis.core.enums.PaymentProvider.STRIPE;
import static org.assertj.core.api.Assertions.assertThat;

@IntegrationTest
@RunWith(HybrisJUnit4ClassRunner.class)
@RunListeners({ ItemCreationListener.class })
public class CountrySyncServiceITest extends ServicelayerTest {

    private static final String ENCODING = "utf-8";
    private static final String TEST_BASICS_FILE = "/test/testCountrySyncData.impex";
    private static final String APP_WITH_NEW_COUNTRY_CONSENT = "country_sync_app_1";
    private static final String APP_WITH_NO_NEW_COUNTRY_CONSENT = "country_sync_app_2";
    private static final Set<String> ENABLED_COUNTRY = Set.of("DE", "AT");
    @Resource
    private ModelService modelService;

    @Resource
    private CommonI18NService commonI18NService;

    @Resource
    private AppService appService;

    @Resource
    private AppLicenseService appLicenseService;

    @Resource
    private CountrySyncService countrySyncService;
    @Resource
    private BaseStoreService baseStoreService;

    @Resource
    private CountryStoreConfigurationService countryStoreConfigurationService;

    @Rule
    public SessionCatalogRule sessionCatalogRule = SessionCatalogRule.stagedCatalog();

    @Before
    public void setup() throws Exception {
        importCsv(TEST_BASICS_FILE, ENCODING);
    }

    @Test
    public void syncCountries_createsNewCountry_whenExistsOnlyInCountryService() {
        String isocode = "RO";
        CountryModel romania = commonI18NService.getCountry(isocode);
        modelService.remove(romania);

        countrySyncService.syncCountries();

        CountryModel createdCountry = commonI18NService.getCountry(isocode);
        assertThat(createdCountry).isNotNull();
        assertBasicFields(createdCountry);
    }

    @Test
    public void syncCountries_differentInDatabase_syncsWithThatOfCountryService() {
        String isocode = "RO";
        modifyMasterDataProperties(isocode);

        countrySyncService.syncCountries();

        CountryModel modifiedCountry = commonI18NService.getCountry(isocode);
        assertThat(modifiedCountry).isNotNull();
        assertBasicFields(modifiedCountry);
    }

    @Test
    public void syncCountries_euFlagSync() {
        String isocode = "US";
        CountryModel country = commonI18NService.getCountry(isocode);
        country.setInEu(true);
        modelService.save(country);

        countrySyncService.syncCountries();

        CountryModel modifiedCountry = commonI18NService.getCountry(isocode);
        assertThat(modifiedCountry).isNotNull();
        assertThat(modifiedCountry.getInEu()).isFalse();
    }

    @Test
    public void whenSyncCountries_thenSyncBlockedCountriesCommercial() {
        final String isoCode = "CA";
        final CountryModel country = commonI18NService.getCountry(isoCode);
        country.setBlockedCountriesCommercial(Set.of());
        modelService.save(country);

        countrySyncService.syncCountries();

        final CountryModel syncedCountry = commonI18NService.getCountry(isoCode);
        assertThat(syncedCountry).isNotNull();
        assertThat(syncedCountry.getBlockedCountriesCommercial()).containsExactly("CA");
    }

    @Test
    public void givenCountryInServiceHasDifferentBlockedCountriesCommercial_whenSyncCountries_thenSync() {
        final String isoCode = "DE";
        final CountryModel country = commonI18NService.getCountry(isoCode);
        final Set<String> blockedCountries = Set.of("AT", "RO");
        country.setBlockedCountriesCommercial(blockedCountries);
        modelService.save(country);

        countrySyncService.syncCountries();

        final CountryModel syncedCountry = commonI18NService.getCountry(isoCode);
        assertThat(syncedCountry).isNotNull();
        assertThat(syncedCountry.getBlockedCountriesCommercial()).doesNotContainAnyElementsOf(blockedCountries);
    }

    private void modifyMasterDataProperties(String isocode) {
        CountryModel country = commonI18NService.getCountry(isocode);
        country.setActive(false);
        country.setEnabledForIntegrators(false);
        country.setEnabledForDevelopers(false);
        country.setSupportedPaymentProviders(Set.of(DPG));
        country.setSupportedPaymentMethods(Set.of(CREDIT_CARD));
        modelService.save(country);
    }

    private void assertBasicFields(CountryModel country) {
        assertThat(country.getActive()).isTrue();
        assertThat(country.isEnabledForIntegrators()).isTrue();
        assertThat(country.isEnabledForDevelopers()).isTrue();
        assertThat(country.getSupportedPaymentProviders()).containsExactlyInAnyOrder(DPG);
        assertThat(country.getSupportedPaymentMethods()).containsExactlyInAnyOrder(CREDIT_CARD, SEPA_CREDIT);
        assertThat(country.getBlockedCountriesCommercial()).isEmpty();
    }

    @Test
    public void syncCountries_createNewCountriesAndAppsWithConsentGetNewEnabledCountries_whenExistsOnlyInCountryService() {
        String isocode = "RO";

        CountryModel romania = commonI18NService.getCountry(isocode);
        modelService.remove(romania);

        countrySyncService.syncCountries();

        assertThat(getEnabledCountryCodes(getApp(APP_WITH_NEW_COUNTRY_CONSENT))).containsAll(ENABLED_COUNTRY)
            .contains(isocode);
        assertThat(getEnabledCountryCodes(getApp(APP_WITH_NO_NEW_COUNTRY_CONSENT))).containsAll(ENABLED_COUNTRY)
            .doesNotContain(isocode);
    }

    @Test
    public void syncCountries_onlyDeactivatesAndNoChangeInAppLicenseEnabledCountries_whenUnknownInCountryService() {
        String deactivatedCountryCode = "css_isocode_1";

        countrySyncService.syncCountries();

        assertThat(getEnabledCountryCodes(getApp(APP_WITH_NEW_COUNTRY_CONSENT))).containsAll(ENABLED_COUNTRY)
            .doesNotContain(deactivatedCountryCode);
        assertThat(getEnabledCountryCodes(getApp(APP_WITH_NO_NEW_COUNTRY_CONSENT))).containsAll(ENABLED_COUNTRY)
            .doesNotContain(deactivatedCountryCode);
    }

    @Test
    public void givenCountryHasChangedSupportedLanguages_whenSyncCountries_thenCountryStoreConfigurationsAreInSync() {
        final String isoCode = "AT";
        final CountryModel country = commonI18NService.getCountry(isoCode);
        final BaseStoreModel baseStore = baseStoreService.getBaseStoreForUid(BaseStoreEnum.AA.getBaseStoreUid());
        countryStoreConfigurationService.getCountryStoreConfiguration(country, baseStore)
            .ifPresent(configurationModel -> modelService.removeAll(configurationModel));

        countrySyncService.syncCountries();

        Optional<CountryStoreConfigurationModel> actualConfiguration = countryStoreConfigurationService.getCountryStoreConfiguration(
            country, baseStore);
        assertThat(actualConfiguration).isPresent();
        assertThat(actualConfiguration.get().getLanguages()).extracting(LanguageModel::getIsocode).containsExactlyInAnyOrder("de", "en");
        assertThat(actualConfiguration.get().getDefaultLanguage()).extracting(LanguageModel::getIsocode).isEqualTo("de");

    }

    @Test
    public void givenCountryHasChangedSupportedLanguages_whenSyncCountries_thenExistingEntryUpdated() {
        final String isoCode = "AT";
        final CountryModel country = commonI18NService.getCountry(isoCode);
        final BaseStoreModel baseStore = baseStoreService.getBaseStoreForUid(BaseStoreEnum.AA.getBaseStoreUid());
        countryStoreConfigurationService.getCountryStoreConfiguration(country, baseStore).ifPresent(configurationModel -> {
                configurationModel.setLanguages(Set.of(commonI18NService.getLanguage("en")));
                configurationModel.setDefaultLanguage(null);
                modelService.save(configurationModel);
            }
        );

        countrySyncService.syncCountries();

        Optional<CountryStoreConfigurationModel> actualConfiguration = countryStoreConfigurationService.getCountryStoreConfiguration(
            country, baseStore);
        assertThat(actualConfiguration).isPresent();
        assertThat(actualConfiguration.get().getLanguages()).extracting(LanguageModel::getIsocode).containsExactlyInAnyOrder("de", "en");
        assertThat(actualConfiguration.get().getDefaultLanguage()).extracting(LanguageModel::getIsocode).isEqualTo("de");

    }

    private Optional<AppModel> getApp(String appCode) {
        return appService.getAppForCodeAndCatalogVersion(appCode, STAGED);
    }

    private Set<String> getEnabledCountryCodes(Optional<AppModel> countrySyncApp) {
        return appLicenseService.getLicense(countrySyncApp.get(), LicenseType.FULL)
            .orElseThrow()
            .getEnabledCountries()
            .stream()
            .map(CountryModel::getIsocode).collect(Collectors.toSet());
    }
}
