package com.sast.cis.core.converter.appediting;

import com.sast.cis.core.model.ApkMediaModel;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.AppVersionModel;
import com.sast.cis.core.service.AppVersionService;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.commercefacades.product.data.ChangelogData;
import de.hybris.platform.commercefacades.product.data.ProductData;
import de.hybris.platform.core.model.product.ProductModel;
import generated.com.sast.cis.core.model.*;

import generated.de.hybris.platform.core.model.product.ProductBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class AppVersionChangelogPopulatorUnitTest {

    private static final String VERSION_ONE_NAME = "Test app version one name";
    private static final String VERSION_TWO_NAME = "Test app version two name";
    private static final String APK_ONE_CHANGELOG = "Test apk one changes";
    private static final String APK_TWO_CHANGELOG = "Test apk two changes";
    private static final Date APK_ONE_CREATION_DATE = Date.valueOf(LocalDate.of(2020, 1, 1));
    private static final Date APK_TWO_CREATION_DATE = Date.valueOf(LocalDate.of(2020, 2, 1));

    @Mock
    private AppVersionService appVersionService;

    @InjectMocks
    private AppVersionChangelogPopulator cisProductPopulator;

    @Test
    public void populate_withApp_correctlyPopulated() {
        ApkMediaModel apk = createDefaultApk(VERSION_ONE_NAME);
        AppVersionModel appVersion = createDefaultAppVersion(apk, APK_ONE_CHANGELOG, APK_ONE_CREATION_DATE);
        AppModel app = createDefaultApp(appVersion);

        when(appVersionService.getVersionsInDescendingOrder(app)).thenReturn(List.of(appVersion));

        ProductData actualResult = new ProductData();
        cisProductPopulator.populate(app, actualResult);

        ProductData expectedResult = new ProductData()
            .withChangelogs(List.of(
                new ChangelogData().withDescription(APK_ONE_CHANGELOG).withVersion(VERSION_ONE_NAME).withCreationDate(APK_ONE_CREATION_DATE)
            ));

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withAppWithMultipleVersions_correctlyPopulated() {
        ApkMediaModel apkOne = createDefaultApk(VERSION_ONE_NAME);
        ApkMediaModel apkTwo = createDefaultApk(VERSION_TWO_NAME);
        AppVersionModel appVersionOne = createDefaultAppVersion(apkOne, APK_ONE_CHANGELOG, APK_ONE_CREATION_DATE);
        AppVersionModel appVersionTwo = createDefaultAppVersion(apkTwo, APK_TWO_CHANGELOG, APK_TWO_CREATION_DATE);
        AppModel app = createDefaultApp(appVersionOne, appVersionTwo);

        when(appVersionService.getVersionsInDescendingOrder(app)).thenReturn(List.of(appVersionOne, appVersionTwo));

        ProductData actualResult = new ProductData();
        cisProductPopulator.populate(app, actualResult);

        ProductData expectedResult = new ProductData()
            .withChangelogs(List.of(
                new ChangelogData()
                    .withDescription(APK_ONE_CHANGELOG).withVersion(VERSION_ONE_NAME).withCreationDate(APK_ONE_CREATION_DATE),
                new ChangelogData()
                    .withDescription(APK_TWO_CHANGELOG).withVersion(VERSION_TWO_NAME).withCreationDate(APK_TWO_CREATION_DATE)
            ));

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withAppWithoutVersions_correctlyPopulated() {
        AppModel app = AppBuilder.generate().buildMockInstance();

        when(appVersionService.getVersionsInDescendingOrder(app)).thenReturn(List.of());

        ProductData actualResult = new ProductData();
        cisProductPopulator.populate(app, actualResult);

        ProductData expectedResult = new ProductData().withChangelogs(List.of());

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withAppLicense_correctlyPopulated() {
        ApkMediaModel apk = createDefaultApk(VERSION_ONE_NAME);
        AppVersionModel appVersion = createDefaultAppVersion(apk, APK_ONE_CHANGELOG, APK_ONE_CREATION_DATE);
        AppModel app = createDefaultApp(appVersion);
        AppLicenseModel appLicense = createDefaultAppLicense(app);

        when(appVersionService.getVersionsInDescendingOrder(app)).thenReturn(List.of(appVersion));

        ProductData actualResult = new ProductData();
        cisProductPopulator.populate(appLicense, actualResult);

        ProductData expectedResult = new ProductData()
            .withChangelogs(List.of(
                new ChangelogData().withDescription(APK_ONE_CHANGELOG).withVersion(VERSION_ONE_NAME).withCreationDate(APK_ONE_CREATION_DATE)
            ));

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(expectedResult);
    }

    @Test
    public void populate_withUnknownProduct_returnsEmptyResult() {
        ProductModel product = ProductBuilder.generate().buildMockInstance();

        ProductData actualResult = new ProductData();
        cisProductPopulator.populate(product, actualResult);

        assertThat(actualResult).usingRecursiveComparison().isEqualTo(new ProductData());
    }

    private ApkMediaModel createDefaultApk(String name) {
        return ApkMediaBuilder.generate().withVersionName(name).buildMockInstance();
    }

    private AppVersionModel createDefaultAppVersion(ApkMediaModel apk, String changelog, Date date) {
        return AppVersionBuilder.generate()
            .withApk(apk)
            .withChangelog(changelog)
            .withCreationtime(date)
            .buildMockInstance();
    }

    private AppModel createDefaultApp(AppVersionModel... appVersions) {
        return AppBuilder.generate().withVersions(Set.of(appVersions)).buildMockInstance();
    }

    private AppLicenseModel createDefaultAppLicense(AppModel app) {
        return AppLicenseBuilder.generate().withBaseProduct(app).buildMockInstance();
    }

}
