[{"module": "AOP alliance", "groupId": "aopalliance", "artifactId": "aopalliance", "url": "http://aopalliance.sourceforge.net", "versions": [{"version": "1.0", "licenses": [{"name": "Public Domain", "url": ""}]}]}, {"module": "localstack-utils", "groupId": "cloud.localstack", "artifactId": "localstack-utils", "url": "http://localstack.cloud", "versions": [{"version": "0.2.1", "licenses": [{"name": "Apache License 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Amazon SQS Java Messaging Library", "groupId": "com.amazonaws", "artifactId": "amazon-sqs-java-messaging-lib", "url": "https://github.com/awslabs/amazon-sqs-java-messaging-lib/", "versions": [{"version": "1.0.8", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://aws.amazon.com/apache2.0"}]}]}, {"module": "AWS SDK for Java - Core", "groupId": "com.amazonaws", "artifactId": "aws-java-sdk-core", "url": "https://aws.amazon.com/sdkforjava", "versions": [{"version": "1.12.780", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://aws.amazon.com/apache2.0"}]}]}, {"module": "AWS Java SDK for Amazon S3", "groupId": "com.amazonaws", "artifactId": "aws-java-sdk-s3", "url": "https://aws.amazon.com/sdkforjava", "versions": [{"version": "1.12.780", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://aws.amazon.com/apache2.0"}]}]}, {"module": "AWS Java SDK for Amazon SNS", "groupId": "com.amazonaws", "artifactId": "aws-java-sdk-sns", "url": "https://aws.amazon.com/sdkforjava", "versions": [{"version": "1.12.780", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://aws.amazon.com/apache2.0"}]}]}, {"module": "AWS Java SDK for Amazon SQS", "groupId": "com.amazonaws", "artifactId": "aws-java-sdk-sqs", "url": "https://aws.amazon.com/sdkforjava", "versions": [{"version": "1.12.780", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://aws.amazon.com/apache2.0"}]}]}, {"module": "AWS Java SDK for AWS STS", "groupId": "com.amazonaws", "artifactId": "aws-java-sdk-sts", "url": "https://aws.amazon.com/sdkforjava", "versions": [{"version": "1.12.780", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://aws.amazon.com/apache2.0"}]}]}, {"module": "AWS Lambda Java Core Library", "groupId": "com.amazonaws", "artifactId": "aws-lambda-java-core", "url": "https://aws.amazon.com/lambda/", "versions": [{"version": "1.2.0", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://aws.amazon.com/apache2.0"}]}]}, {"module": "communicator-v2-client", "groupId": "com.bosch.sast", "artifactId": "communicator-v2-client", "url": "", "versions": [{"version": "2.0.13", "licenses": [{"name": "NL", "url": "https://gitlab.sastdev.net/SAST/"}]}]}, {"module": "AWS Lambda Java Events Library", "groupId": "com.amazonaws", "artifactId": "aws-lambda-java-events", "url": "https://aws.amazon.com/lambda/", "versions": [{"version": "2.1.0", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://aws.amazon.com/apache2.0"}]}]}, {"module": "Android Tools apksig library", "groupId": "com.android.tools.build", "artifactId": "a<PERSON><PERSON><PERSON>", "url": "https://developer.android.com/studio", "versions": [{"version": "3.5.2", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Jackson-annotations", "groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-annotations", "url": "http://github.com/FasterXML/jackson", "versions": [{"version": "2.6.0", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}, {"version": "2.6.1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}, {"version": "2.9.0", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "<PERSON>-<PERSON>", "groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-core", "url": "https://github.com/FasterXML/jackson-core", "versions": [{"version": "2.6.1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}, {"version": "2.6.7", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}, {"version": "2.9.5", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "jackson-databind", "groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-databind", "url": "http://github.com/FasterXML/jackson", "versions": [{"version": "2.6.1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}, {"version": "2.6.7.1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}, {"version": "2.9.8", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Jackson-dataformat-CBOR", "groupId": "com.fasterxml.jackson.dataformat", "artifactId": "jackson-dataformat-cbor", "url": "http://wiki.fasterxml.com/JacksonForCbor", "versions": [{"version": "2.6.7", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Jackson datatype: jdk8", "groupId": "com.fasterxml.jackson.datatype", "artifactId": "jackson-datatype-jdk8", "url": "https://github.com/FasterXML/jackson-modules-java8/jackson-datatype-jdk8", "versions": [{"version": "2.18.3", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Jackson datatype: JSR310", "groupId": "com.fasterxml.jackson.datatype", "artifactId": "jackson-datatype-jsr310", "url": "https://github.com/FasterXML/jackson-modules-java8/jackson-datatype-jsr310", "versions": [{"version": "2.18.3", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "SpotBugs Annotations", "groupId": "com.github.spotbugs", "artifactId": "spotbugs-annotations", "url": "https://spotbugs.github.io/", "versions": [{"version": "3.1.12", "licenses": [{"name": "GNU LESSER GENERAL PUBLIC LICENSE, Version 2.1", "url": "https://www.gnu.org/licenses/old-licenses/lgpl-2.1.en.html"}]}]}, {"module": "WireMock", "groupId": "com.github.tomakehurst", "artifactId": "wiremock-jre8-standalone", "url": "http://wiremock.org", "versions": [{"version": "2.35.0", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/license/LICENSE-2.0.txt"}]}]}, {"module": "FindBugs-jsr305", "groupId": "com.google.code.findbugs", "artifactId": "jsr305", "url": "http://findbugs.sourceforge.net/", "versions": [{"version": "1.3.9", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Gson", "groupId": "com.google.code.gson", "artifactId": "gson", "url": "https://github.com/google/gson/gson", "versions": [{"version": "2.9.0", "licenses": [{"name": "Apache-2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "error-prone annotations", "groupId": "com.google.errorprone", "artifactId": "error_prone_annotations", "url": "http://nexus.sonatype.org/oss-repository-hosting.html/error_prone_parent/error_prone_annotations", "versions": [{"version": "2.1.3", "licenses": [{"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Guava: Google Core Libraries for Java", "groupId": "com.google.guava", "artifactId": "guava", "url": "https://github.com/google/guava/guava", "versions": [{"version": "24.1.1-jre", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "J2ObjC Annotations", "groupId": "com.google.j2objc", "artifactId": "j2objc-annotations", "url": "https://github.com/google/j2objc/", "versions": [{"version": "1.1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "JSch", "groupId": "com.jcraft", "artifactId": "jsch", "url": "http://www.jcraft.com/jsch/", "versions": [{"version": "0.1.54", "licenses": [{"name": "Revised BSD", "url": "http://www.jcraft.com/jsch/LICENSE.txt"}]}]}, {"module": "rome", "groupId": "com.rometools", "artifactId": "rome", "url": "http://rometools.com/rome", "versions": [{"version": "1.6.0", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "rome-fetcher", "groupId": "com.rometools", "artifactId": "rome-fetcher", "url": "http://rometools.com/rome-fetcher", "versions": [{"version": "1.6.0", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "rome-utils", "groupId": "com.rometools", "artifactId": "rome-utils", "url": "http://rometools.com/rome-utils", "versions": [{"version": "1.6.0", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Solace JMS API", "groupId": "com.solacesystems", "artifactId": "sol-jms", "url": "https://docs.solace.com/Solace-JMS-API/jms-get-started-open.htm", "versions": [{"version": "10.6.3", "licenses": [{"name": "SOLACE CORPORATION LICENSE AGREEMENT", "url": "https://solace.com/license-software"}]}]}, {"module": "stripe-java", "groupId": "com.stripe", "artifactId": "stripe-java", "url": "https://github.com/stripe/stripe-java", "versions": [{"version": "10.4.0", "licenses": [{"name": "The MIT License", "url": "https://opensource.org/licenses/MIT"}]}, {"version": "11.0.0", "licenses": [{"name": "The MIT License", "url": "https://opensource.org/licenses/MIT"}]}, {"version": "11.7.0", "licenses": [{"name": "The MIT License", "url": "https://opensource.org/licenses/MIT"}]}, {"version": "14.0.1", "licenses": [{"name": "The MIT License", "url": "https://opensource.org/licenses/MIT"}]}, {"version": "17.1.0", "licenses": [{"name": "The MIT License", "url": "https://opensource.org/licenses/MIT"}]}, {"version": "18.4.0", "licenses": [{"name": "The MIT License", "url": "https://opensource.org/licenses/MIT"}]}]}, {"module": "CORS Filter", "groupId": "com.thetransactioncompany", "artifactId": "cors-filter", "url": "http://software.dzhuvinov.com/cors-filter.html", "versions": [{"version": "1.8", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Java Property Utility", "groupId": "com.thetransactioncompany", "artifactId": "java-property-utils", "url": "http://software.dzhuvinov.com/", "versions": [{"version": "1.9", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "junit-dataprovider-core", "groupId": "com.tngtech.junit.dataprovider", "artifactId": "junit-dataprovider-core", "url": "https://github.com/TNG/junit-dataprovider", "versions": [{"version": "2.6", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "junit4-dataprovider", "groupId": "com.tngtech.junit.dataprovider", "artifactId": "junit4-dataprovider", "url": "https://github.com/TNG/junit-dataprovider", "versions": [{"version": "2.6", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "junit-dataprovider", "groupId": "com.tngtech.java", "artifactId": "junit-dataprovider", "url": "https://github.com/TNG/junit-dataprovider", "versions": [{"version": "1.13.1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "commons-beanutils", "groupId": "commons-beanutils", "artifactId": "commons-beanutils", "url": "", "versions": [{"version": "1.7.0", "licenses": [{"name": "Apache License Version 2.0", "url": ""}]}]}, {"module": "Apache Commons Codec", "groupId": "commons-codec", "artifactId": "commons-codec", "url": "http://commons.apache.org/proper/commons-codec/", "versions": [{"version": "1.9", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}, {"version": "1.10", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Codec", "groupId": "commons-codec", "artifactId": "commons-codec", "url": "", "versions": [{"version": "1.2", "licenses": [{"name": "The Apache Software License, Version 1.1", "url": ""}]}]}, {"module": "commons-collections", "groupId": "commons-collections", "artifactId": "commons-collections", "url": "", "versions": [{"version": "3.1", "licenses": [{"name": "Apache License Version 2.0", "url": "http://www.apache.org/licenses/"}]}]}, {"module": "HttpClient", "groupId": "commons-httpclient", "artifactId": "commons-httpclient", "url": "http://jakarta.apache.org/httpcomponents/httpclient-3.x/", "versions": [{"version": "3.1", "licenses": [{"name": "Apache License", "url": "http://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "IO", "groupId": "commons-io", "artifactId": "commons-io", "url": "http://jakarta.apache.org/commons/io/", "versions": [{"version": "1.3.1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "/LICENSE.txt"}]}]}, {"module": "Commons IO", "groupId": "commons-io", "artifactId": "commons-io", "url": "http://commons.apache.org/io/", "versions": [{"version": "2.4", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "<PERSON>", "groupId": "commons-lang", "artifactId": "commons-lang", "url": "http://jakarta.apache.org/commons/lang/", "versions": [{"version": "2.3", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "/LICENSE.txt"}]}]}, {"module": "Commons Logging", "groupId": "commons-logging", "artifactId": "commons-logging", "url": "http://commons.apache.org/proper/commons-logging/", "versions": [{"version": "1.1.3", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Apache Commons Net", "groupId": "commons-net", "artifactId": "commons-net", "url": "http://commons.apache.org/proper/commons-net/", "versions": [{"version": "3.4", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Display tag library", "groupId": "displaytag", "artifactId": "displaytag", "url": "http://displaytag.sourceforge.net/displaytag", "versions": [{"version": "1.2", "licenses": [{"name": "Artistic License", "url": "LICENSE.txt"}]}]}, {"module": "Prometheus JMX Exporter - Java Agent", "groupId": "io.prometheus.jmx", "artifactId": "jmx_prometheus_javaagent", "url": "http://github.com/prometheus/jmx_exporter", "versions": [{"version": "0.18.0", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "javax.inject", "groupId": "javax.inject", "artifactId": "javax.inject", "url": "http://code.google.com/p/atinject/", "versions": [{"version": "1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "servlet-api", "groupId": "javax.servlet", "artifactId": "servlet-api", "url": "", "versions": [{"version": "2.5", "licenses": [{"name": "CDDL", "url": ""}, {"name": "GPL 2.0", "url": ""}]}]}, {"module": "jsp-api", "groupId": "javax.servlet.jsp", "artifactId": "jsp-api", "url": "", "versions": [{"version": "2.1", "licenses": [{"name": "CDDL", "url": ""}, {"name": "GPL 2.0", "url": ""}]}]}, {"module": "JavaServer Pages(TM) Standard Tag Library", "groupId": "javax.servlet.jsp.jstl", "artifactId": "jstl-api", "url": "http://jcp.org/en/jsr/detail?id=52", "versions": [{"version": "1.2", "licenses": [{"name": "CDDL + GPLv2 with classpath exception", "url": "https://glassfish.dev.java.net/nonav/public/CDDL+GPL.html"}]}]}, {"module": "jersey-client", "groupId": "com.sun.jersey", "artifactId": "jersey-client", "url": "https://jersey.java.net/jersey-client/", "versions": [{"version": "1.13", "licenses": [{"name": "CDDL 1.1", "url": "http://glassfish.java.net/public/CDDL+GPL_1_1.html"}, {"name": "GPL2 w/ CPE", "url": "http://glassfish.java.net/public/CDDL+GPL_1_1.html"}]}]}, {"module": "jersey-core", "groupId": "com.sun.jersey", "artifactId": "jersey-core", "url": "https://jersey.java.net/jersey-core/", "versions": [{"version": "1.13", "licenses": [{"name": "CDDL 1.1", "url": "http://glassfish.java.net/public/CDDL+GPL_1_1.html"}, {"name": "GPL2 w/ CPE", "url": "http://glassfish.java.net/public/CDDL+GPL_1_1.html"}]}]}, {"module": "jersey-json", "groupId": "com.sun.jersey", "artifactId": "jersey-json", "url": "https://jersey.java.net/jersey-json/", "versions": [{"version": "1.13", "licenses": [{"name": "CDDL 1.1", "url": "http://glassfish.java.net/public/CDDL+GPL_1_1.html"}, {"name": "GPL2 w/ CPE", "url": "http://glassfish.java.net/public/CDDL+GPL_1_1.html"}]}]}, {"module": "jersey-server", "groupId": "com.sun.jersey", "artifactId": "jersey-server", "url": "https://jersey.java.net/jersey-server/", "versions": [{"version": "1.13", "licenses": [{"name": "CDDL 1.1", "url": "http://glassfish.java.net/public/CDDL+GPL_1_1.html"}, {"name": "GPL2 w/ CPE", "url": "http://glassfish.java.net/public/CDDL+GPL_1_1.html"}]}]}, {"module": "jersey-servlet", "groupId": "com.sun.jersey", "artifactId": "jersey-servlet", "url": "https://jersey.java.net/jersey-servlet/", "versions": [{"version": "1.13", "licenses": [{"name": "CDDL 1.1", "url": "http://glassfish.java.net/public/CDDL+GPL_1_1.html"}, {"name": "GPL2 w/ CPE", "url": "http://glassfish.java.net/public/CDDL+GPL_1_1.html"}]}]}, {"module": "jersey-spring", "groupId": "com.sun.jersey.contribs", "artifactId": "jersey-spring", "url": "http://maven.apache.org", "versions": [{"version": "1.13", "licenses": [{"name": "CDDL 1.1", "url": "http://glassfish.java.net/public/CDDL+GPL_1_1.html"}, {"name": "GPL2 w/ CPE", "url": "http://glassfish.java.net/public/CDDL+GPL_1_1.html"}]}]}, {"module": "Jersey Test Framework - Core", "groupId": "com.sun.jersey.jersey-test-framework", "artifactId": "jersey-test-framework-core", "url": "https://jersey.java.net/jersey-test-framework/jersey-test-framework-core/", "versions": [{"version": "1.13", "licenses": [{"name": "CDDL 1.1", "url": "http://glassfish.java.net/public/CDDL+GPL_1_1.html"}, {"name": "GPL2 w/ CPE", "url": "http://glassfish.java.net/public/CDDL+GPL_1_1.html"}]}]}, {"module": "JSR105 API", "groupId": "javax.xml.crypto", "artifactId": "jsr105-api", "url": "", "versions": [{"version": "1.0.1", "licenses": [{"name": "Apache License Version 2.0", "url": "http://www.apache.org/licenses/"}]}]}, {"module": "jsr311-api", "groupId": "javax.ws.rs", "artifactId": "jsr311-api", "url": "https://jsr311.dev.java.net", "versions": [{"version": "1.1.1", "licenses": [{"name": "CDDL License", "url": "http://www.opensource.org/licenses/cddl1.php"}]}]}, {"module": "Joda-Time", "groupId": "joda-time", "artifactId": "joda-time", "url": "http://www.joda.org/joda-time/", "versions": [{"version": "2.8.1", "licenses": [{"name": "Apache 2", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "JUnit", "groupId": "junit", "artifactId": "junit", "url": "http://junit.org", "versions": [{"version": "4.12", "licenses": [{"name": "Eclipse Public License 1.0", "url": "http://www.eclipse.org/legal/epl-v10.html"}]}]}, {"module": "Log4j", "groupId": "log4j", "artifactId": "log4j", "url": "http://logging.apache.org/log4j/docs/", "versions": [{"version": "1.2.13", "licenses": [{"name": "Apache", "url": ""}]}]}, {"module": "Apache Log4j", "groupId": "log4j", "artifactId": "log4j", "url": "http://logging.apache.org/log4j/1.2/", "versions": [{"version": "1.2.17", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "MySQL Connector/J", "groupId": "com.mysql", "artifactId": "mysql-connector-j", "url": "http://dev.mysql.com/doc/connector-j/en/", "versions": [{"version": "8.2.0", "licenses": [{"name": "The GNU General Public License, v2 with Universal FOSS Exception, v1.0", "url": ""}]}]}, {"module": "apk-parser", "groupId": "com.azena", "artifactId": "apk-parser", "url": "https://github.com/hsiafan/apk-parser", "versions": [{"version": "2.6.14", "licenses": [{"name": "The BSD 2-Clause License", "url": "https://github.com/hsiafan/apk-parser/blob/master/LICENSE.txt"}]}]}, {"module": "Java Native Access", "groupId": "net.java.dev.jna", "artifactId": "jna", "url": "https://github.com/twall/jna", "versions": [{"version": "4.1.0", "licenses": [{"name": "LGPL, version 2.1", "url": "http://www.gnu.org/licenses/licenses.html"}, {"name": "ASL, version 2", "url": "http://www.apache.org/licenses/"}]}]}, {"module": "StAX Utilities Project", "groupId": "net.java.dev.stax-utils", "artifactId": "stax-utils", "url": "http://java.net/projects/stax-utils/", "versions": [{"version": "20070216", "licenses": [{"name": "BSD", "url": "http://www.opensource.org/licenses/bsd-license.html"}]}]}, {"module": "Apache Commons Lang", "groupId": "org.apache.commons", "artifactId": "commons-lang3", "url": "http://commons.apache.org/proper/commons-lang/", "versions": [{"version": "3.4", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}, {"version": "3.5", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0.txt"}]}, {"version": "3.7", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Apache HttpClient", "groupId": "org.apache.httpcomponents", "artifactId": "httpclient", "url": "http://hc.apache.org/httpcomponents-client", "versions": [{"version": "4.5.13", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Apache HttpCore", "groupId": "org.apache.httpcomponents", "artifactId": "httpcore", "url": "http://hc.apache.org/httpcomponents-core-ga", "versions": [{"version": "4.4.4", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "org.apache.xmlgraphics:batik-constants", "groupId": "org.apache.xmlgraphics", "artifactId": "batik-constants", "url": "http://xmlgraphics.apache.org/batik/batik-constants/", "versions": [{"version": "1.9.1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}, {"version": "1.11", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "org.apache.xmlgraphics:batik-css", "groupId": "org.apache.xmlgraphics", "artifactId": "batik-css", "url": "http://xmlgraphics.apache.org/batik/batik-css/", "versions": [{"version": "1.9.1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}, {"version": "1.11", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "org.apache.xmlgraphics:batik-i18n", "groupId": "org.apache.xmlgraphics", "artifactId": "batik-i18n", "url": "http://xmlgraphics.apache.org/batik/batik-i18n/", "versions": [{"version": "1.9.1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}, {"version": "1.11", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "org.apache.xmlgraphics:batik-util", "groupId": "org.apache.xmlgraphics", "artifactId": "batik-util", "url": "http://xmlgraphics.apache.org/batik/batik-util/", "versions": [{"version": "1.9.1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}, {"version": "1.11", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Apache XML Graphics Commons", "groupId": "org.apache.xmlgraphics", "artifactId": "xmlgraphics-commons", "url": "http://xmlgraphics.apache.org/commons/", "versions": [{"version": "2.2", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}, {"version": "2.3", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Awaitility", "groupId": "org.awaitility", "artifactId": "awaitility", "url": "http://awaitility.org", "versions": [{"version": "4.1.0", "licenses": [{"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Bouncy Castle PKIX, CMS, EAC, TSP, PKCS, OCSP, CMP, and CRMF APIs", "groupId": "org.bouncycastle", "artifactId": "bcpkix-jdk18on", "url": "http://www.bouncycastle.org/java.html", "versions": [{"version": "1.80", "licenses": [{"name": "Bouncy Castle Licence", "url": "http://www.bouncycastle.org/licence.html"}]}]}, {"module": "Bouncy Castle Provider", "groupId": "org.bouncycastle", "artifactId": "bcprov-jdk18on", "url": "http://www.bouncycastle.org/java.html", "versions": [{"version": "1.80", "licenses": [{"name": "Bouncy Castle Licence", "url": "http://www.bouncycastle.org/licence.html"}]}]}, {"module": "Checker Qual", "groupId": "org.checkerframework", "artifactId": "checker-compat-qual", "url": "http://checkerframework.org", "versions": [{"version": "2.0.0", "licenses": [{"name": "GNU General Public License, version 2 (GPL2), with the classpath exception", "url": "http://www.gnu.org/software/classpath/license.html"}, {"name": "The MIT License", "url": "http://opensource.org/licenses/MIT"}]}]}, {"module": "Apache Groovy", "groupId": "org.codehaus.groovy", "artifactId": "groovy-all", "url": "http://groovy-lang.org", "versions": [{"version": "2.4.6", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Animal Sniffer Annotations", "groupId": "org.codehaus.mojo", "artifactId": "animal-sniffer-annotations", "url": "http://mojo.codehaus.org/animal-sniffer/animal-sniffer-annotations", "versions": [{"version": "1.14", "licenses": [{"name": "MIT license", "url": "http://www.opensource.org/licenses/mit-license.php"}]}]}, {"module": "JavaServer Pages (TM) TagLib Implementation", "groupId": "org.glassfish.web", "artifactId": "javax.servlet.jsp.jstl", "url": "http://jstl.java.net", "versions": [{"version": "1.2.5", "licenses": [{"name": "CDDL + GPLv2 with classpath exception", "url": "http://glassfish.dev.java.net/nonav/public/CDDL+GPL.html"}]}]}, {"module": "Hamcrest All", "groupId": "org.hamcrest", "artifactId": "hamcrest-all", "url": "https://github.com/hamcrest/JavaHamcrest/hamcrest-all", "versions": [{"version": "1.3", "licenses": [{"name": "New BSD License", "url": "http://www.opensource.org/licenses/bsd-license.php"}]}]}, {"module": "Hamcrest Core", "groupId": "org.hamcrest", "artifactId": "hamcrest-core", "url": "https://github.com/hamcrest/JavaHamcrest/hamcrest-core", "versions": [{"version": "1.3", "licenses": [{"name": "New BSD License", "url": "http://www.opensource.org/licenses/bsd-license.php"}]}]}, {"module": "Hamcrest library", "groupId": "org.hamcrest", "artifactId": "hamcrest-library", "url": "https://github.com/hamcrest/JavaHamcrest/hamcrest-library", "versions": [{"version": "1.3", "licenses": [{"name": "New BSD License", "url": "http://www.opensource.org/licenses/bsd-license.php"}]}]}, {"module": "HyperSQL Database", "groupId": "org.hsqldb", "artifactId": "hsqldb", "url": "http://hsqldb.org", "versions": [{"version": "2.4.1", "licenses": [{"name": "HSQLDB License, a BSD open source license", "url": "http://hsqldb.org/web/hsqlLicense.html"}]}]}, {"module": "Smack", "groupId": "org.igniterealtime.smack", "artifactId": "smack-core", "url": "http://www.igniterealtime.org/projects/smack/", "versions": [{"version": "4.1.9", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Smack", "groupId": "org.igniterealtime.smack", "artifactId": "smack-extensions", "url": "http://www.igniterealtime.org/projects/smack/", "versions": [{"version": "4.1.9", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Smack", "groupId": "org.igniterealtime.smack", "artifactId": "smack-im", "url": "http://www.igniterealtime.org/projects/smack/", "versions": [{"version": "4.1.9", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Smack", "groupId": "org.igniterealtime.smack", "artifactId": "smack-java7", "url": "http://www.igniterealtime.org/projects/smack/", "versions": [{"version": "4.1.9", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Smack", "groupId": "org.igniterealtime.smack", "artifactId": "smack-resolver-javax", "url": "http://www.igniterealtime.org/projects/smack/", "versions": [{"version": "4.1.9", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Smack", "groupId": "org.igniterealtime.smack", "artifactId": "smack-sasl-javax", "url": "http://www.igniterealtime.org/projects/smack/", "versions": [{"version": "4.1.9", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Smack", "groupId": "org.igniterealtime.smack", "artifactId": "smack-tcp", "url": "http://www.igniterealtime.org/projects/smack/", "versions": [{"version": "4.1.9", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "JBoss Logging 3", "groupId": "org.jboss.logging", "artifactId": "jboss-logging", "url": "http://www.jboss.org", "versions": [{"version": "3.3.2.Final", "licenses": [{"name": "Apache License, version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "JDOM", "groupId": "org.jdom", "artifactId": "jdom2", "url": "http://www.jdom.org", "versions": [{"version": "2.0.6", "licenses": [{"name": "Similar to Apache License but with the acknowledgment clause removed", "url": "https://raw.github.com/hunterhacker/jdom/master/LICENSE.txt"}]}]}, {"module": "winp", "groupId": "org.jvnet.winp", "artifactId": "winp", "url": "http://kohsuke.org/winp/", "versions": [{"version": "1.23", "licenses": [{"name": "The MIT license", "url": "http://www.opensource.org/licenses/mit-license.php"}]}]}, {"module": "JXMPP", "groupId": "org.jxmpp", "artifactId": "jxmpp-core", "url": "http://www.igniterealtime.org/projects/jxmpp/", "versions": [{"version": "0.4.2", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "JXMPP", "groupId": "org.jxmpp", "artifactId": "jxmpp-util-cache", "url": "http://www.igniterealtime.org/projects/jxmpp/", "versions": [{"version": "0.4.2", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Keycloak Adapter Core", "groupId": "org.keycloak", "artifactId": "keycloak-adapter-core", "url": "http://keycloak.org/keycloak-adapter-core", "versions": [{"version": "24.0.5", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Keycloak Adapter SPI", "groupId": "org.keycloak", "artifactId": "keycloak-adapter-spi", "url": "http://keycloak.org/keycloak-adapter-spi", "versions": [{"version": "24.0.5", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Keycloak Common", "groupId": "org.keycloak", "artifactId": "keycloak-common", "url": "http://keycloak.org/keycloak-common", "versions": [{"version": "24.0.5", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Keycloak Core", "groupId": "org.keycloak", "artifactId": "keycloak-core", "url": "http://keycloak.org/keycloak-core", "versions": [{"version": "24.0.5", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Keycloak Spring Security Integration", "groupId": "org.keycloak", "artifactId": "keycloak-spring-security-adapter", "url": "http://keycloak.org/keycloak-spring-security-adapter", "versions": [{"version": "24.0.5", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Keycloak Authz: Client API", "groupId": "org.keycloak", "artifactId": "keycloak-authz-client", "url": "http://keycloak.org/keycloak-authz-parent/keycloak-authz-client", "versions": [{"version": "24.0.5", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Keycloak Crypto Default", "groupId": "org.keycloak", "artifactId": "keycloak-crypto-default", "url": "http://keycloak.org/keycloak-crypto-parent/keycloak-crypto-default", "versions": [{"version": "24.0.5", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Keycloak Authz: Policy Enforcer", "groupId": "org.keycloak", "artifactId": "keycloak-policy-enforcer", "url": "http://keycloak.org/keycloak-authz-parent/keycloak-policy-enforcer", "versions": [{"version": "24.0.5", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "<PERSON><PERSON><PERSON>", "groupId": "org.mockito", "artifactId": "mockito-core", "url": "http://www.mockito.org", "versions": [{"version": "1.10.19", "licenses": [{"name": "The MIT License", "url": "http://github.com/mockito/mockito/blob/master/LICENSE"}]}]}, {"module": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "groupId": "org.objenesis", "artifactId": "obje<PERSON><PERSON>", "url": "http://objenesis.org", "versions": [{"version": "2.1", "licenses": [{"name": "Apache 2", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}, {"version": "2.6", "licenses": [{"name": "Apache 2", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "OWASP AntiSamy", "groupId": "org.owasp.antisamy", "artifactId": "antisamy", "url": "https://github.com/nahsra/antisamy", "versions": [{"version": "1.5.7", "licenses": [{"name": "BSD 3", "url": "https://opensource.org/licenses/BSD-3-Clause"}]}, {"version": "1.5.8", "licenses": [{"name": "BSD 3", "url": "https://opensource.org/licenses/BSD-3-Clause"}]}]}, {"module": "JCL Implemented Over SLF4J", "groupId": "org.slf4j", "artifactId": "jcl104-over-slf4j", "url": "http://www.slf4j.org", "versions": [{"version": "1.4.2", "licenses": [{"name": "MIT", "url": ""}]}]}, {"module": "SLF4J API Module", "groupId": "org.slf4j", "artifactId": "slf4j-api", "url": "http://www.slf4j.org", "versions": [{"version": "1.4.2", "licenses": [{"name": "MIT", "url": ""}]}, {"version": "1.7.12", "licenses": [{"name": "MIT License", "url": "http://www.opensource.org/licenses/mit-license.php"}]}, {"version": "1.7.16", "licenses": [{"name": "MIT License", "url": "http://www.opensource.org/licenses/mit-license.php"}]}, {"version": "1.7.22", "licenses": [{"name": "MIT License", "url": "http://www.opensource.org/licenses/mit-license.php"}]}]}, {"module": "SLF4J LOG4J-12 Binding", "groupId": "org.slf4j", "artifactId": "slf4j-log4j12", "url": "http://www.slf4j.org", "versions": [{"version": "1.4.2", "licenses": [{"name": "MIT", "url": ""}]}]}, {"module": "Spring AOP", "groupId": "org.springframework", "artifactId": "spring-aop", "url": "https://github.com/spring-projects/spring-framework", "versions": [{"version": "4.2.9.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Beans", "groupId": "org.springframework", "artifactId": "spring-beans", "url": "https://github.com/spring-projects/spring-framework", "versions": [{"version": "4.2.9.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Cloud AWS Messaging", "groupId": "org.springframework.cloud", "artifactId": "spring-cloud-aws-messaging", "url": "https://spring.io/spring-cloud/spring-cloud-aws/spring-cloud-aws-messaging", "versions": [{"version": "2.2.6.RELEASE", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Spring Context", "groupId": "org.springframework", "artifactId": "spring-context", "url": "https://github.com/spring-projects/spring-framework", "versions": [{"version": "4.3.22.RELEASE", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Spring Context Support", "groupId": "org.springframework", "artifactId": "spring-context-support", "url": "https://github.com/spring-projects/spring-framework", "versions": [{"version": "4.3.22.RELEASE", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Spring Core", "groupId": "org.springframework", "artifactId": "spring-core", "url": "https://github.com/spring-projects/spring-framework", "versions": [{"version": "4.2.9.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Expression Language (SpEL)", "groupId": "org.springframework", "artifactId": "spring-expression", "url": "https://github.com/spring-projects/spring-framework", "versions": [{"version": "4.3.22.RELEASE", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Spring JDBC", "groupId": "org.springframework", "artifactId": "spring-jdbc", "url": "https://github.com/spring-projects/spring-framework", "versions": [{"version": "4.3.22.RELEASE", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Spring JMS", "groupId": "org.springframework", "artifactId": "spring-jms", "url": "https://github.com/spring-projects/spring-framework", "versions": [{"version": "4.3.22.RELEASE", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Spring Messaging", "groupId": "org.springframework", "artifactId": "spring-messaging", "url": "https://github.com/spring-projects/spring-framework", "versions": [{"version": "4.3.22.RELEASE", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Spring Object/XML Marshalling", "groupId": "org.springframework", "artifactId": "spring-oxm", "url": "https://github.com/spring-projects/spring-framework", "versions": [{"version": "4.3.22.RELEASE", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Spring TestContext Framework", "groupId": "org.springframework", "artifactId": "spring-test", "url": "https://github.com/spring-projects/spring-framework", "versions": [{"version": "4.3.22.RELEASE", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Spring Transaction", "groupId": "org.springframework", "artifactId": "spring-tx", "url": "https://github.com/spring-projects/spring-framework", "versions": [{"version": "4.3.22.RELEASE", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Spring Web", "groupId": "org.springframework", "artifactId": "spring-web", "url": "https://github.com/spring-projects/spring-framework", "versions": [{"version": "4.2.9.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Web MVC", "groupId": "org.springframework", "artifactId": "spring-webmvc", "url": "https://github.com/spring-projects/spring-framework", "versions": [{"version": "4.3.22.RELEASE", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Spring Integration Core", "groupId": "org.springframework.integration", "artifactId": "spring-integration-core", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration ApplicationEvent Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-event", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration RSS Feed Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-feed", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration File Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-file", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration FTP Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-ftp", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration Groovy Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-groovy", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration HTTP Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-http", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration IP Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-ip", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration JDBC Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-jdbc", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration JMS Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-jms", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration JMX Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-jmx", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration Mail Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-mail", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration RMI Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-rmi", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration Scripting Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-scripting", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration Security Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-security", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration SFTP Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-sftp", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration Stream Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-stream", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration Test Support - **No SI Dependencies Allowed**", "groupId": "org.springframework.integration", "artifactId": "spring-integration-test", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration Twitter Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-twitter", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration Web Services Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-ws", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration XML Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-xml", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Integration XMPP Support", "groupId": "org.springframework.integration", "artifactId": "spring-integration-xmpp", "url": "https://projects.spring.io/spring-integration", "versions": [{"version": "4.3.19.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Retry", "groupId": "org.springframework.retry", "artifactId": "spring-retry", "url": "http://www.springsource.org", "versions": [{"version": "1.1.3.RELEASE", "licenses": [{"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "spring-security-core", "groupId": "org.springframework.security", "artifactId": "spring-security-core", "url": "http://spring.io/spring-security", "versions": [{"version": "4.2.7.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "spring-security-crypto", "groupId": "org.springframework.security", "artifactId": "spring-security-crypto", "url": "http://spring.io/spring-security", "versions": [{"version": "3.2.8.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Social Config", "groupId": "org.springframework.social", "artifactId": "spring-social-config", "url": "https://github.com/spring-projects/spring-social", "versions": [{"version": "1.1.0.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Foundational module containing the ServiceProvider Connect Framework and Service API invocation support.", "groupId": "org.springframework.social", "artifactId": "spring-social-core", "url": "https://github.com/spring-projects/spring-social", "versions": [{"version": "1.1.0.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Twitter API", "groupId": "org.springframework.social", "artifactId": "spring-social-twitter", "url": "https://github.com/SpringSource/spring-social-twitter", "versions": [{"version": "1.1.2.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Spring Web Integration", "groupId": "org.springframework.social", "artifactId": "spring-social-web", "url": "https://github.com/spring-projects/spring-social", "versions": [{"version": "1.1.0.RELEASE", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "spring-ws-core", "groupId": "org.springframework.ws", "artifactId": "spring-ws-core", "url": "http://project.spring.io/spring-ws/spring-ws-core", "versions": [{"version": "2.4.2.RELEASE", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "spring-xml", "groupId": "org.springframework.ws", "artifactId": "spring-xml", "url": "http://project.spring.io/spring-ws/spring-xml", "versions": [{"version": "2.4.2.RELEASE", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "software.amazon.ion:ion-java", "groupId": "software.amazon.ion", "artifactId": "ion-java", "url": "https://github.com/amznlabs/ion-java/", "versions": [{"version": "1.0.2", "licenses": [{"name": "The Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "json-unit-assertj", "groupId": "net.javacrumbs.json-unit", "artifactId": "json-unit-assertj", "url": "https://github.com/lukas-krecan/JsonUnit/json-unit-assertj", "versions": [{"version": "2.40.1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "json-unit-json-path", "groupId": "net.javacrumbs.json-unit", "artifactId": "json-unit-json-path", "url": "https://github.com/lukas-krecan/JsonUnit/json-unit-json-path", "versions": [{"version": "2.40.1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "json-unit-core", "groupId": "net.javacrumbs.json-unit", "artifactId": "json-unit-core", "url": "https://github.com/lukas-krecan/JsonUnit/json-unit-core", "versions": [{"version": "2.40.1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "XML Commons External Components XML APIs", "groupId": "xml-apis", "artifactId": "xml-apis", "url": "http://xml.apache.org/commons/components/external/", "versions": [{"version": "1.4.01", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}, {"name": "The SAX License", "url": "http://www.saxproject.org/copying.html"}, {"name": "The W3C License", "url": "http://www.w3.org/TR/2004/REC-DOM-Level-3-Core-20040407/java-binding.zip"}]}]}, {"module": "XML Commons External Components XML APIs Extensions", "groupId": "xml-apis", "artifactId": "xml-apis-ext", "url": "http://xml.apache.org/commons/components/external/", "versions": [{"version": "1.3.04", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "MXP1: Xml Pull Parser 3rd Edition (XPP3)", "groupId": "xpp3", "artifactId": "xpp3", "url": "http://www.extreme.indiana.edu/xgws/xsoap/xpp/mxp1/", "versions": [{"version": "1.1.4c", "licenses": [{"name": "Indiana University Extreme! Lab Software License, vesion 1.1.1", "url": "http://www.extreme.indiana.edu/viewcvs/~checkout~/XPP3/java/LICENSE.txt"}, {"name": "Public Domain", "url": "http://creativecommons.org/licenses/publicdomain"}, {"name": "Apache Software License, version 1.1", "url": "http://www.apache.org/licenses/LICENSE-1.1"}]}]}, {"module": "jsoup Java HTML Parser", "groupId": "org.jsoup", "artifactId": "jsoup", "url": "https://jsoup.org/", "versions": [{"version": "1.14.3", "licenses": [{"name": "The MIT License", "url": "https://jsoup.org/license"}]}]}, {"module": "OWASP Java HTML Sanitizer", "groupId": "com.googlecode.owasp-java-html-sanitizer", "artifactId": "owasp-java-html-sanitizer", "url": "https://github.com/OWASP/java-html-sanitizer/owasp-java-html-sanitizer", "versions": [{"version": "20211018.2", "licenses": [{"name": "Apache License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Project Lombok", "groupId": "org.projectlombok", "artifactId": "lombok", "url": "https://projectlombok.org", "versions": [{"version": "1.18.22", "licenses": [{"name": "The MIT License", "url": "https://projectlombok.org/LICENSE"}]}]}, {"module": "Mattermost Data Models", "groupId": "net.bis5.mattermost4j", "artifactId": "mattermost-models", "url": "https://github.com/maruTA-bis5/mattermost4j/mattermost-models", "versions": [{"version": "0.22.1", "licenses": [{"name": "Apache License Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Mattermost APIv4 Client for Java", "groupId": "net.bis5.mattermost4j", "artifactId": "mattermost4j-core", "url": "https://github.com/maruTA-bis5/mattermost4j/mattermost4j-core", "versions": [{"version": "0.22.1", "licenses": [{"name": "Apache License Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "groupId": "org.skyscreamer", "artifactId": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/skyscreamer/JSONassert", "versions": [{"version": "1.5.1", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "json-path", "groupId": "com.jayway.jsonpath", "artifactId": "json-path", "url": "https://github.com/jayway/JsonPath", "versions": [{"version": "2.9.0", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "JSON in Java", "groupId": "org.json", "artifactId": "json", "url": "https://github.com/douglascrockford/JSON-java", "versions": [{"version": "20231013", "licenses": [{"name": "Public Domain", "url": "https://github.com/stleary/JSON-java/blob/master/LICENSE"}]}]}, {"module": "jsonevent-layout", "groupId": "net.logstash.log4j", "artifactId": "jsonevent-layout", "url": "http://logstash.net", "versions": [{"version": "1.7", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "Java Message Service", "groupId": "javax.jms", "artifactId": "jms", "url": "http://java.sun.com/products/jms", "versions": [{"version": "1.1", "licenses": [{"name": "CDDL", "url": " "}]}]}, {"module": "Nimbus JOSE+JWT", "groupId": "com.nimbusds", "artifactId": "nimbus-jose-jwt", "url": "https://bitbucket.org/connect2id/nimbus-jose-jwt", "versions": [{"version": "9.41.2", "licenses": [{"name": "The Apache Software License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}, {"module": "spring-security-oauth2-core", "groupId": "org.springframework.security", "artifactId": "spring-security-oauth2-core", "url": "https://spring.io/projects/spring-security", "versions": [{"version": "5.8.16", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "spring-security-oauth2-jose", "groupId": "org.springframework.security", "artifactId": "spring-security-oauth2-jose", "url": "https://spring.io/projects/spring-security", "versions": [{"version": "5.8.16", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "spring-security-oauth2-resource-server", "groupId": "org.springframework.security", "artifactId": "spring-security-oauth2-resource-server", "url": "https://spring.io/projects/spring-security", "versions": [{"version": "5.8.16", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "spring-security-oauth2-client", "groupId": "org.springframework.security", "artifactId": "spring-security-oauth2-client", "url": "https://spring.io/projects/spring-security", "versions": [{"version": "5.8.16", "licenses": [{"name": "Apache License, Version 2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0"}]}]}, {"module": "Apache Log4j Core Tests", "groupId": "org.apache.logging.log4j", "artifactId": "log4j-core-test", "url": "https://logging.apache.org/log4j/2.x/log4j/log4j-core-test/", "versions": [{"version": "2.23.1", "licenses": [{"name": "Apache-2.0", "url": "https://www.apache.org/licenses/LICENSE-2.0.txt"}]}]}]