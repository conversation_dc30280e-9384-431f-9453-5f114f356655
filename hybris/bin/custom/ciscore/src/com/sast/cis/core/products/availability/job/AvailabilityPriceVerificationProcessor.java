package com.sast.cis.core.products.availability.job;

import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.products.availability.service.AvailabilityToPricesSyncService;
import com.sast.cis.core.service.AppService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class AvailabilityPriceVerificationProcessor {

    private final AppService appService;

    private final AvailabilityToPricesSyncService availabilityToPricesSyncService;

    public void verifyForCurrentCatalog() {
        LOG.info("Verifying availability and prices consistency for current catalog");
        final List<AppModel> apps = appService.getRestrictedBuyerGroupAppsOfStagedCatalog();
        apps.forEach(availabilityToPricesSyncService::verifyAppAvailability);
        LOG.info("Verification of availability and prices consistency for current catalog completed");
    }
}
