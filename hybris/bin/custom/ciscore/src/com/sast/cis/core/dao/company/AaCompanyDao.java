package com.sast.cis.core.dao.company;

import com.sast.cis.core.enums.BillingSystemStatus;
import com.sast.cis.core.enums.PspSellerAccountStatus;
import com.sast.cis.core.model.AppModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.model.PspSellerAccountModel;
import de.hybris.platform.catalog.model.CatalogModel;
import de.hybris.platform.catalog.model.CatalogVersionModel;
import de.hybris.platform.catalog.model.CompanyModel;
import de.hybris.platform.cmsfacades.util.builder.CountryModelBuilder;
import de.hybris.platform.core.model.c2l.CountryModel;
import de.hybris.platform.servicelayer.exceptions.SystemException;
import de.hybris.platform.servicelayer.search.FlexibleSearchQuery;
import de.hybris.platform.servicelayer.search.FlexibleSearchService;
import de.hybris.platform.store.BaseStoreModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
@RequiredArgsConstructor
@Slf4j
public class AaCompanyDao {

    private static final String FIND_SELLER_COMPANY_FOR_COUNTRY = "SELECT DISTINCT {company:pk}"
        + " FROM {" + IoTCompanyModel._TYPECODE + " AS company"
        // An AA Seller company should have apps in the AA product catalog
        + " JOIN " + AppModel._TYPECODE + " AS app ON {app:" + AppModel.COMPANY + "} = {company:pk}"
        + " JOIN " + CatalogVersionModel._TYPECODE + " AS cv ON {app:" + AppModel.CATALOGVERSION + "} = {cv:pk}"
        + " JOIN " + CatalogModel._TYPECODE + " AS catalog ON {cv:" + CatalogVersionModel.CATALOG + "} = {catalog:pk}"
        // An AA Seller company should have seller accounts
        + " JOIN " + PspSellerAccountModel._TYPECODE + " AS psa ON {psa:" + PspSellerAccountModel.COMPANY + "} = {company:pk}"
        + " JOIN " + PspSellerAccountStatus._TYPECODE + " AS psa_status ON {psa:" + PspSellerAccountModel.STATUS + "} = {psa_status:pk}"
        + " JOIN " + BillingSystemStatus._TYPECODE + " AS b_system_status ON {psa:" + PspSellerAccountModel.BILLINGSYSTEMSTATUS
        + "} = {b_system_status:pk}"
        + " JOIN " + CountryModel._TYPECODE + " AS country ON {company:" + IoTCompanyModel.COUNTRY + "} = {country:pk}"
        + " JOIN " + BaseStoreModel._TYPECODE + " AS store ON {company:" + IoTCompanyModel.STORE + "} = {store:pk}"
        + " }"
        + " WHERE {company:" + IoTCompanyModel.COUNTRY + "} = ?country"
        + " AND {company:" + IoTCompanyModel.DEACTIVATIONDATE + "} IS NULL"
        // The company should have active seller accounts
        + " AND {psa_status:code} = 'ACTIVE'"
        + " AND {b_system_status:code} = 'IN_SYNC'"
        // We're interested in AA Seller companies only
        + " AND {store:" + BaseStoreModel.UID + "} = 'aastore'"
        + " AND {catalog:" + CatalogModel.ID + "} = 'aav2ProductCatalog'";

    private final FlexibleSearchService flexibleSearchService;

    /**
     * Find the AA Seller company for a given country.
     * <p/>
     * This is built on the assumption that for AA, there is always at most one seller company per country.
     * <p/>
     * There is no straightforward way to find the AA Seller company for a given country.
     * This method will return a company that has the following properties:
     * - It has apps in the AA product catalog
     * - It has seller accounts
     * - It is associated with the given country
     * - It is associated with the AA store
     * - It has not been deactivated
     *
     * @param country The country for which to find the AA Seller company
     * @return The AA Seller company for the given country, if found.
     */
    public Optional<IoTCompanyModel> findAaSellerForCountry(final CountryModel country) {
        final FlexibleSearchQuery query = new FlexibleSearchQuery(FIND_SELLER_COMPANY_FOR_COUNTRY);
        query.addQueryParameter("country", country);

        try {
            IoTCompanyModel company = new IoTCompanyModel();
            company.setUid("company_uid_1234");
            company.setCountry(CountryModelBuilder.aModel().withIsoCode("AT").build());
            return Optional.of(company);
//            return Optional.of(flexibleSearchService.searchUnique(query));
        } catch (final SystemException e) {
            LOG.error("Error while searching for AA Seller company for country {}", country, e);
            return Optional.empty();
        }
    }
}
