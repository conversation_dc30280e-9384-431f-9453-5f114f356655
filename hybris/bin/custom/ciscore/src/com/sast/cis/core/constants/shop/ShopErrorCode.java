package com.sast.cis.core.constants.shop;

public enum ShopErrorCode {
    GENERIC_SERVER_ERROR("generic.serverError"),
    GENERIC_NOT_FOUND("generic.notFound"),
    GENERIC_BAD_REQUEST("generic.badRequest"),
    GENERIC_FORBIDDEN("generic.forbidden"),
    REVIEW_EXISTS("review.exists"),
    REVIEW_APP_NOT_OWNED("review.appNotOwned"),
    CHECKOUT_CART_MODIFIED("checkout.cartModified"),
    CHECKOUT_PLACE_ORDER_FAILED("checkout.placeOrderFailed"),
    CHECKOUT_PAYMENT_FAILED("checkout.paymentFailed"),
    CHECKOUT_INVALID_CART_STATE("checkout.invalidCartState"),
    CART_UNPAYABLE("cart.unpayable"),
    CART_EMPTY("cart.empty"),
    CART_INVALID_QUANTITY("cart.invalidQuantity"),
    CART_NO_SELF_PURCHASES("cart.noSelfPurchases"),
    CART_ADD_FAILED("cart.addFailed"),
    CART_UNSUPPORTED_LICENSE("cart.unsupportedLicense"),
    CART_CHECKOUT_PREPARATION_FAILURE("cart.preparationFailure"),
    LICENSEACTIVATION_ERROR("licenseactivation.generic"),
    LICENSEACTIVATION_NOT_PURCHASABLE("licenseactivation.notPurchasable"),
    LICENSEACTIVATION_TYPE_NOT_SUPPORTED("licenseactivation.notSupported"),
    COMPANYPROFILE_NOT_FOUND("companyprofile.notFound"),
    PRODUCT_NOT_FOUND("product.notFound"),
    EULA_NOT_ACCEPTED("eula.notAccepted"),
    PAYMENT_INFO_CREATION_FAILURE("paymentInfo.creationFailure"),
    PAYMENT_INFO_CREATION_CANCELED("paymentInfo.creationCanceled");

    private final String value;

    ShopErrorCode(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
