package com.sast.cis.core.products.availability.service;

import com.sast.cis.core.customergroup.CustomerGroupService;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.AppModel;
import de.hybris.platform.core.model.user.UserGroupModel;
import de.hybris.platform.europe1.enums.UserPriceGroup;
import de.hybris.platform.europe1.model.PriceRowModel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.sast.cis.core.dao.CatalogVersion.STAGED;
import static com.sast.cis.core.enums.StoreAvailabilityMode.RESTRICTED_BUYER_GROUP;
import static java.util.List.of;

@Slf4j
@Service
@RequiredArgsConstructor
public class AvailabilityToPricesSyncService {

    private final CustomerGroupService customerGroupService;

    /**
     * Checks whether the User Group assignments for the specified app are consistent with the Customer Group prices.
     * Logs alerts if discrepancies are found.
     * Note: This method does not check for cases where a group has a price but is not assigned to the license.
     * Such prices are intentionally retained to support existing subscriptions.
     *
     * @param app app to verify
     */
    public void verifyAppAvailability(@NonNull final AppModel app) {
        LOG.debug("Verify User Groups assignments for app '{}' ", app.getCode());
        final boolean restrictedBuyerGroupApp = RESTRICTED_BUYER_GROUP.equals(app.getStoreAvailabilityMode());
        if (!restrictedBuyerGroupApp) {
            throw new IllegalArgumentException(
                "App with code '%s' is not a user group restricted product".formatted(app.getCode())
            );
        }
        final boolean stagedCatalogApp = STAGED.getVersionName().equals(app.getCatalogVersion().getVersion());
        if (!stagedCatalogApp) {
            throw new IllegalArgumentException(
                "App with code '%s' should be from the STAGED catalog version".formatted(app.getCode())
            );
        }
        app.getVariants().stream()
            .filter(AppLicenseModel.class::isInstance)
            .map(AppLicenseModel.class::cast)
            .forEach(this::verifyVariantAvailability);
    }

    private void verifyVariantAvailability(@NonNull final AppLicenseModel license) {
        LOG.debug("Verify User Groups assignments for license '{}' ", license.getCode());
        final Set<UserGroupModel> currentUserGroups = license.getUserGroups();
        final Set<UserGroupModel> groupsWithActivePrices = license.getCurrentlyValidPrices()
            .stream()
            .map(this::getPriceRowUserGroups)
            .flatMap(Collection::stream)
            .collect(Collectors.toSet());

        currentUserGroups.stream()
            .filter(group -> !groupsWithActivePrices.contains(group))
            .forEach(group -> LOG.warn(
                "ALERT: License is available to group but does not have a valid price for it. License code: {}, group UID: {}",
                license.getCode(),
                group.getUid()
            ));
    }

    private List<UserGroupModel> getPriceRowUserGroups(final PriceRowModel priceRow) {
        return Optional.ofNullable(priceRow.getUg())
            .filter(UserPriceGroup.class::isInstance)
            .map(UserPriceGroup.class::cast)
            .map(customerGroupService::findByUserPriceGroup)
            .orElse(of());
    }
}
