package com.sast.cis.core.facade.cart;

import com.sast.cis.core.data.PlaceOrderData;
import com.sast.cis.core.distributor.AaDistributorService;
import de.hybris.platform.core.model.order.CartModel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@RequiredArgsConstructor
@Slf4j
@Component
public class AaDistributorExtensionHandler implements SessionCartExtensionHandler {

    @Resource
    private final AaDistributorService aaDistributorService;

    @Override
    public void extendSessionCart(@NonNull CartModel sessionCart, @NonNull PlaceOrderData placeOrderData) {

        // use default distributor, if selected distributor ID not found in the backend
        sessionCart.setAaDistributorCompany(aaDistributorService
            .findAaDistributorByID(placeOrderData.getDistributorID())
            .orElse(sessionCart.getCompany().getDefaultAaDistributor()));
    }
}
