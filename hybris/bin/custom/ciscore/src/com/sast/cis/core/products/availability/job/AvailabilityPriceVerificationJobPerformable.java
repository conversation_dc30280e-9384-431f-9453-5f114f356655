package com.sast.cis.core.products.availability.job;

import com.sast.cis.core.job.AbstractConfigurableCronJobPerformable;
import de.hybris.platform.commerceservices.impersonation.ImpersonationContext;
import de.hybris.platform.cronjob.enums.CronJobResult;
import de.hybris.platform.cronjob.enums.CronJobStatus;
import de.hybris.platform.cronjob.model.CronJobModel;
import de.hybris.platform.servicelayer.cronjob.PerformResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Checks whether the User Group assignments for RESTRICTED_BUYER_GROUP apps are consistent with the Customer Group prices and logs alerts if discrepancies are found.
 * Note: This method does not check for cases where a group has a price but is not assigned to the license.
 * Such prices are intentionally retained to support existing subscriptions tied to that group.
 *
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AvailabilityPriceVerificationJobPerformable extends AbstractConfigurableCronJobPerformable<CronJobModel> {

    private final AvailabilityPriceVerificationProcessor availabilityToPricesSyncProcessor;

    @Override
    protected PerformResult performInternal(final CronJobModel cronJob, final ImpersonationContext context) {
        LOG.info("Start job for verifying User Groups assignments for current catalog");

        availabilityToPricesSyncProcessor.verifyForCurrentCatalog();

        return new PerformResult(CronJobResult.SUCCESS, CronJobStatus.FINISHED);
    }
}
