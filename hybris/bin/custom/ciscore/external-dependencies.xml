<!-- [y] hybris Platform Copyright (c) 2017 SAP SE or an SAP affiliate company.
    All rights reserved. This software is the confidential and proprietary information
    of SAP ("Confidential Information"). You shall not disclose such Confidential
    Information and shall use it only in accordance with the terms of the license
    agreement you entered into with SAP. -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>de.hybris.platform</groupId>
    <artifactId>ciscore</artifactId>
    <version>6.6.0.0-RC24</version>

    <packaging>jar</packaging>

    <properties>
        <aws-java-sdk.version>1.12.780</aws-java-sdk.version>
        <junit-dataprovider.version>2.6</junit-dataprovider.version>
        <spotbugs-annotations.version>3.1.12</spotbugs-annotations.version>
        <gson.version>2.9.0</gson.version>
        <jackson.version>2.18.3</jackson.version>
        <keycloak.version>24.0.5</keycloak.version>
        <spring.security.version>5.8.16</spring.security.version>
        <apk-parser.version>2.6.14</apk-parser.version>
        <prometheus.version>0.18.0</prometheus.version>
        <awaitility.version>4.1.0</awaitility.version>
        <wiremock.version>2.35.0</wiremock.version>
        <apksig.version>3.5.2</apksig.version>
        <localstack.version>0.2.1</localstack.version>
        <json-junit.version>2.40.1</json-junit.version>
        <jsoup.version>1.14.3</jsoup.version>
        <owasp.version>20211018.2</owasp.version>
        <lombok.version>1.18.22</lombok.version>
        <mattermost4j-core.version>0.22.1</mattermost4j-core.version>
        <jsonassert.version>1.5.1</jsonassert.version>
        <json-path.version>2.9.0</json-path.version>
        <json.version>20231013</json.version>
        <jsonevent-layout.version>1.7</jsonevent-layout.version>
        <jms.version>1.1</jms.version>
        <httpcomponents.version>4.5.13</httpcomponents.version>
        <nimbusds.version>9.41.2</nimbusds.version>
        <log4j2.test.version>2.23.1</log4j2.test.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-bom</artifactId>
                <version>${aws-java-sdk.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>${httpcomponents.version}</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-s3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-sqs</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-sns</artifactId>
            <version>${aws-java-sdk.version}</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-sts</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.azena</groupId>
            <artifactId>apk-parser</artifactId>
            <version>${apk-parser.version}</version>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-spring-security-adapter</artifactId>
            <version>${keycloak.version}</version>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-adapter-spi</artifactId>
            <version>${keycloak.version}</version>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-core</artifactId>
            <version>${keycloak.version}</version>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-adapter-core</artifactId>
            <version>${keycloak.version}</version>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-common</artifactId>
            <version>${keycloak.version}</version>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-authz-client</artifactId>
            <version>${keycloak.version}</version>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-crypto-default</artifactId>
            <version>${keycloak.version}</version>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-policy-enforcer</artifactId>
            <version>${keycloak.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-core</artifactId>
            <version>${spring.security.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-resource-server</artifactId>
            <version>${spring.security.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-jose</artifactId>
            <version>${spring.security.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-client</artifactId>
            <version>${spring.security.version}</version>
        </dependency>
        <dependency>
            <groupId>com.nimbusds</groupId>
            <artifactId>nimbus-jose-jwt</artifactId>
            <version>${nimbusds.version}</version>
        </dependency>
        <dependency>
            <groupId>io.prometheus.jmx</groupId>
            <artifactId>jmx_prometheus_javaagent</artifactId>
            <version>${prometheus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jdk8</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.android.tools.build</groupId>
            <artifactId>apksig</artifactId>
            <version>${apksig.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>${jsoup.version}</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.owasp-java-html-sanitizer</groupId>
            <artifactId>owasp-java-html-sanitizer</artifactId>
            <version>${owasp.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <dependency>
            <groupId>net.bis5.mattermost4j</groupId>
            <artifactId>mattermost4j-core</artifactId>
            <version>${mattermost4j-core.version}</version>
        </dependency>
        <dependency>
            <groupId>net.bis5.mattermost4j</groupId>
            <artifactId>mattermost-models</artifactId>
            <version>${mattermost4j-core.version}</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.log4j</groupId>
            <artifactId>jsonevent-layout</artifactId>
            <version>${jsonevent-layout.version}</version>
        </dependency>
        <dependency>
            <groupId>javax.jms</groupId>
            <artifactId>jms</artifactId>
            <version>${jms.version}</version>
        </dependency>

        <!--  test dependencies -->
        <dependency>
            <groupId>com.tngtech.junit.dataprovider</groupId>
            <artifactId>junit4-dataprovider</artifactId>
            <version>${junit-dataprovider.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tngtech.junit.dataprovider</groupId>
            <artifactId>junit-dataprovider-core</artifactId>
            <version>${junit-dataprovider.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.spotbugs</groupId>
            <artifactId>spotbugs-annotations</artifactId>
            <version>${spotbugs-annotations.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.tomakehurst</groupId>
            <artifactId>wiremock-jre8-standalone</artifactId>
            <version>${wiremock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <version>${awaitility.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>cloud.localstack</groupId>
            <artifactId>localstack-utils</artifactId>
            <version>${localstack.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.json-unit</groupId>
            <artifactId>json-unit-assertj</artifactId>
            <version>${json-junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.json-unit</groupId>
            <artifactId>json-unit-json-path</artifactId>
            <version>${json-junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.json-unit</groupId>
            <artifactId>json-unit-core</artifactId>
            <version>${json-junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.skyscreamer</groupId>
            <artifactId>jsonassert</artifactId>
            <version>${jsonassert.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
            <version>${json-path.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>${json.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core-test</artifactId>
            <scope>test</scope>
            <version>${log4j2.test.version}</version>
        </dependency>
    </dependencies>
</project>
