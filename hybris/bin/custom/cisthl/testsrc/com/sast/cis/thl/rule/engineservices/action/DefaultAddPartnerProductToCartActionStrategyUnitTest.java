package com.sast.cis.thl.rule.engineservices.action;

import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.order.*;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.core.model.product.UnitModel;
import de.hybris.platform.order.CalculationService;
import de.hybris.platform.order.CartService;
import de.hybris.platform.order.exceptions.CalculationException;
import de.hybris.platform.product.ProductService;
import de.hybris.platform.promotionengineservices.model.RuleBasedOrderAddProductActionModel;
import de.hybris.platform.promotionengineservices.promotionengine.PromotionActionService;
import de.hybris.platform.promotionengineservices.util.ActionUtils;
import de.hybris.platform.promotionengineservices.util.PromotionResultUtils;
import de.hybris.platform.promotions.model.PromotionResultModel;
import de.hybris.platform.ruleengineservices.rao.*;
import de.hybris.platform.servicelayer.exceptions.AmbiguousIdentifierException;
import de.hybris.platform.servicelayer.exceptions.UnknownIdentifierException;
import de.hybris.platform.servicelayer.model.ModelService;
import generated.de.hybris.platform.core.model.order.CartEntryBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.NoSuchElementException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class DefaultAddPartnerProductToCartActionStrategyUnitTest {
    private static final String THL_PRODUCT_CODE = "thlProductCode";
    private static final String MASTER_PRODUCT_CODE = "masterProductCode";
    private static final String USERPRICEGROUP = "IDW000";

    @Mock
    private CalculationService calculationService;

    @Mock
    private CartService cartService;

    @Mock
    private ProductService productService;

    @Mock
    private ModelService modelService;

    @Mock
    private PromotionActionService promotionActionService;

    @Mock
    private PartnerProductRAO partnerProductRAO;

    @Mock
    private CartRAO cartRAO;

    @Mock
    private ProductModel product1;

    @Mock
    private ProductModel product2;

    @Mock
    private OrderEntryRAO entryRAO2;

    @Mock
    private PromotionResultModel promotionResult;

    @Mock
    private PromotionResultUtils promotionResultUtils;

    @Mock
    private CartModel cart;

    @Mock
    private OrderModel order;

    private AbstractOrderEntryModel orderEntry1;

    private CartEntryModel orderEntry2;

    @Mock
    private RuleBasedOrderAddProductActionModel ruleBasedOrderAddProductAction;

    @Mock
    private ActionUtils actionUtils;

    @InjectMocks
    DefaultAddPartnerProductToCartActionStrategy strategy;

    @Before
    public void setup() {
        strategy.setPromotionActionService(promotionActionService);
        strategy.setPromotionResultUtils(promotionResultUtils);
        strategy.setModelService(modelService);
        strategy.setPromotionAction(RuleBasedOrderAddProductActionModel.class);
        strategy.setActionUtils(actionUtils);
        strategy.setCalculationService(calculationService);
        when(partnerProductRAO.getAppliedToObject()).thenReturn(cartRAO);
        when(entryRAO2.getProductCode()).thenReturn(THL_PRODUCT_CODE);
        when(entryRAO2.getUg()).thenReturn(USERPRICEGROUP);
        when(productService.getProductForCode(eq(THL_PRODUCT_CODE))).thenReturn(product2);
        when(product1.getCode()).thenReturn(MASTER_PRODUCT_CODE);
        when(product2.getCode()).thenReturn(THL_PRODUCT_CODE);
        orderEntry1 = createOrderEntryModel(product1, 1, USERPRICEGROUP, cart, 1);
        orderEntry2 = createOrderEntryModel(product2, 1, USERPRICEGROUP, cart, 2);
        when(cart.getEntries()).thenReturn(List.of(orderEntry1, orderEntry2));
        when(partnerProductRAO.getAddedOrderEntry()).thenReturn(entryRAO2);
        when(partnerProductRAO.getParentProductCode()).thenReturn(MASTER_PRODUCT_CODE);
        when(promotionActionService.createPromotionResult(any(PartnerProductRAO.class))).thenReturn(promotionResult);
        when(modelService.create(any(Class.class))).thenReturn(ruleBasedOrderAddProductAction);
        when(promotionResultUtils.getOrder(any(PromotionResultModel.class))).thenReturn(cart);
    }

    @Test
    public void testApply_returns_successful_promotion_result() {
        List<PromotionResultModel> result = strategy.apply(partnerProductRAO);
        assertThat(result).isNotEmpty();
        verify(modelService, times(1)).save(orderEntry1);
        verify(orderEntry1, times(1)).setAddOnUg(any(String.class));
        verify(orderEntry1, times(1)).setAddOnThl(eq(THL_PRODUCT_CODE));
    }

    @Test
    public void testApply_returns_empty_list_when_action_is_not_partnerproductrao() {
        DiscountRAO discountRAO = new DiscountRAO();
        List<PromotionResultModel> result = strategy.apply(discountRAO);
        assertThat(result).isEmpty();
    }

    @Test
    public void testApply_returns_empty_list_when_applied_object_is_not_cartrao() {
        OrderRAO orderRAO = new OrderRAO();
        when(partnerProductRAO.getAppliedToObject()).thenReturn(orderRAO);
        List<PromotionResultModel> result = strategy.apply(partnerProductRAO);
        assertThat(result).isEmpty();
    }

    @Test
    public void testApply_returns_empty_list_when_added_order_entry_is_not_exists() {
        when(partnerProductRAO.getAddedOrderEntry()).thenReturn(null);
        List<PromotionResultModel> result = strategy.apply(partnerProductRAO);
        assertThat(result).isEmpty();
    }

    @Test
    public void testApply_returns_empty_list_when_added_order_entry_has_no_productcode() {
        when(entryRAO2.getProductCode()).thenReturn(null);
        List<PromotionResultModel> result = strategy.apply(partnerProductRAO);
        assertThat(result).isEmpty();
    }

    @Test
    public void testApply_returns_empty_list_when_product_not_found_for_productcode() {
        doThrow(new UnknownIdentifierException("")).when(productService).getProductForCode(eq(THL_PRODUCT_CODE));
        List<PromotionResultModel> result = strategy.apply(partnerProductRAO);
        assertThat(result).isEmpty();
    }

    @Test
    public void testApply_returns_empty_list_when_duplicate_product_found_for_productcode() {
        doThrow(new AmbiguousIdentifierException("")).when(productService).getProductForCode(eq(THL_PRODUCT_CODE));
        List<PromotionResultModel> result = strategy.apply(partnerProductRAO);
        assertThat(result).isEmpty();
    }

    @Test
    public void testApply_returns_empty_list_when_no_promotion_result() {
        when(promotionActionService.createPromotionResult(any(PartnerProductRAO.class))).thenReturn(null);
        List<PromotionResultModel> result = strategy.apply(partnerProductRAO);
        assertThat(result).isEmpty();
    }

    @Test
    public void testApply_returns_empty_list_when_promotion_result_has_no_order() {
        when(promotionResultUtils.getOrder(any(PromotionResultModel.class))).thenReturn(null);
        List<PromotionResultModel> result = strategy.apply(partnerProductRAO);
        assertThat(result).isEmpty();
    }

    @Test
    public void testApply_throws_exception_when_no_parent_product_order_entry_exists() {
        when(cart.getEntries()).thenReturn(List.of(orderEntry2));
        assertThatThrownBy(() -> strategy.apply(partnerProductRAO)).isInstanceOf(NoSuchElementException.class)
            .hasMessage("No cartEntry existent for appLicense with code=%s", MASTER_PRODUCT_CODE);
    }

    @Test
    public void testApply_returns_empty_list_when_no_thl_order_entry_found_for_non_cart_instance() {
        when(promotionResultUtils.getOrder(any(PromotionResultModel.class))).thenReturn(order);
        List<PromotionResultModel> result = strategy.apply(partnerProductRAO);
        assertThat(result).isEmpty();
    }

    @Test
    public void testApply_returns_promotionresult_when_thl_order_entry_found_for_cart() {
        when(cart.getEntries()).thenReturn(List.of(orderEntry1, orderEntry2));
        List<PromotionResultModel> result = strategy.apply(partnerProductRAO);
        assertThat(result).isNotEmpty();
        verify(cartService, times(0)).addNewEntry(any(CartModel.class), any(ProductModel.class), eq(1L), any(UnitModel.class), eq(-1),
            eq(Boolean.FALSE));
    }

    @Test
    public void testApply_throws_exception_when_calculation_service_fails() throws CalculationException {
        doThrow(new CalculationException("")).when(calculationService).recalculate(any(AbstractOrderModel.class));
        assertThatThrownBy(() -> strategy.apply(partnerProductRAO)).isInstanceOf(RuntimeException.class);
    }

    private CartEntryModel createOrderEntryModel(ProductModel product, long quantity, String userPriceGroup,
        CartModel cart, int entryNumber) {
        return CartEntryBuilder.generate()
            .withOrder(cart)
            .withQuantity(quantity)
            .withAddOnUg(userPriceGroup)
            .withProduct(product)
            .withEntryNumber(entryNumber)
            .buildMockInstance();
    }

}
