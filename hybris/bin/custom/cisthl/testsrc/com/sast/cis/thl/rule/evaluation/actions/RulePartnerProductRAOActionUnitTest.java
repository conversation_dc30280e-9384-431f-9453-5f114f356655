package com.sast.cis.thl.rule.evaluation.actions;

import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.model.product.ProductModel;
import de.hybris.platform.product.ProductService;
import de.hybris.platform.ruleengineservices.rao.*;
import de.hybris.platform.ruleengineservices.rule.evaluation.RuleActionContext;
import de.hybris.platform.ruleengineservices.rule.evaluation.actions.RAOConsumptionSupport;
import de.hybris.platform.ruleengineservices.util.RaoUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashSet;
import java.util.LinkedHashSet;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class RulePartnerProductRAOActionUnitTest {
    private static final String THL_PRODUCT_CODE = "thlProductCode";
    private static final String MASTER_PRODUCT_CODE = "masterProductCode";
    private static final String USERPRICEGROUP = "IDW000";

    @Mock
    private ProductService productService;

    @Mock
    private RuleActionContext context;

    @Mock
    private RAOConsumptionSupport consumptionSupport;

    @Mock
    private RaoUtils raoUtils;

    @Mock
    private PartnerProductRAO partnerProductRAO;

    @Mock
    private RuleEngineResultRAO resultRAO;

    @Mock
    private AbstractRuleActionRAO ruleActionRAO;

    @Mock
    private CartRAO cartRAO;

    @Mock
    private ProductModel product;

    @Mock
    private OrderEntryRAO entryRAO1;

    @Mock
    private OrderEntryRAO entryRAO2;

    private LinkedHashSet<AbstractRuleActionRAO> ruleActionRAOs = new LinkedHashSet<>();

    @InjectMocks
    RulePartnerProductRAOAction action;

    @Before
    public void setup() {
        action = new RulePartnerProductRAOAction();
        action.setProductService(productService);
        action.setConsumptionSupport(consumptionSupport);
        action.setRaoUtils(raoUtils);
        ruleActionRAOs.add(ruleActionRAO);
        when(context.getCartRao()).thenReturn(cartRAO);
        when(context.getRuleEngineResultRao()).thenReturn(resultRAO);
        when(resultRAO.getActions()).thenReturn(ruleActionRAOs);
        when(context.getParameter("product", String.class)).thenReturn(THL_PRODUCT_CODE);
        when(context.getParameter("quantity", Integer.class)).thenReturn(1);
        when(context.getParameter("ug", String.class)).thenReturn("ug");
        when(context.getParameter("parent", String.class)).thenReturn(MASTER_PRODUCT_CODE);
        when(productService.getProductForCode(eq(THL_PRODUCT_CODE))).thenReturn(product);
        when(product.getCode()).thenReturn(THL_PRODUCT_CODE);
        when(entryRAO1.getProductCode()).thenReturn(MASTER_PRODUCT_CODE);
        when(entryRAO1.getQuantity()).thenReturn(1);
        when(entryRAO1.getUg()).thenReturn(USERPRICEGROUP);
        when(entryRAO2.getProductCode()).thenReturn(THL_PRODUCT_CODE);
        when(entryRAO2.getUg()).thenReturn(USERPRICEGROUP);
        when(cartRAO.getEntries()).thenReturn(new HashSet<OrderEntryRAO>() {{
            add(entryRAO1);
            add(entryRAO2);
        }});
        when(cartRAO.getCurrencyIsoCode()).thenReturn("EUR");
        when(consumptionSupport.getConsumableQuantity(any(OrderEntryRAO.class))).thenReturn(1);
    }

    @Test
    public void testPerformActionInternal_returns_true() {
        boolean result = action.performActionInternal(context);
        assertThat(result).isTrue();
    }

    @Test
    public void testPerformActionInternal_no_context_throwsException() {
        assertThatThrownBy(() -> action.performActionInternal(null))
            .isInstanceOf(IllegalStateException.class)
            .hasMessage("Given rule action context cannot be null");
    }

    @Test
    public void testPerformActionInternal_no_productCode_throwsException() {
        when(context.getParameter("product", String.class)).thenReturn(null);
        assertThatThrownBy(() -> action.performActionInternal(context))
            .isInstanceOf(IllegalStateException.class)
            .hasMessage("Given THL product code cannot be empty");
    }

    @Test
    public void testPerformActionInternal_no_quantity_throwsException() {
        when(context.getParameter("quantity", Integer.class)).thenReturn(0);
        assertThatThrownBy(() -> action.performActionInternal(context))
            .isInstanceOf(IllegalStateException.class)
            .hasMessage("Given THL product quantity cannot be less than 1");
    }

    @Test
    public void testPerformActionInternal_no_userPriceGroup_throwsException() {
        when(context.getParameter("ug", String.class)).thenReturn(null);
        assertThatThrownBy(() -> action.performActionInternal(context))
            .isInstanceOf(IllegalStateException.class)
            .hasMessage("Given UserPriceGroup cannot be empty");
    }

    @Test
    public void testPerformActionInternal_no_parentProductCode_throwsException() {
        when(context.getParameter("parent", String.class)).thenReturn(null);
        assertThatThrownBy(() -> action.performActionInternal(context))
            .isInstanceOf(IllegalStateException.class)
            .hasMessage("Given parent productcode for the cannot be empty");
    }

    @Test
    public void testPerformActionInternal_with_no_cart_returns_false() {
        when(context.getCartRao()).thenReturn(null);
        boolean result = action.performActionInternal(context);
        assertThat(result).isFalse();
    }

    @Test
    public void testPerformActionInternal_with_no_thl_product_found_returns_false() {
        when(productService.getProductForCode(eq(THL_PRODUCT_CODE))).thenReturn(null);
        boolean result = action.performActionInternal(context);
        assertThat(result).isFalse();
    }

    @Test
    public void testPerformActionInternal_with_no_parent_product_found_returns_false() {
        when(entryRAO1.getProductCode()).thenReturn(null);
        boolean result = action.performActionInternal(context);
        assertThat(result).isFalse();
    }

    @Test
    public void testPerformActionInternal_with_negative_parent_product_quantity_returns_false() {
        when(entryRAO1.getQuantity()).thenReturn(-1);
        boolean result = action.performActionInternal(context);
        assertThat(result).isFalse();
    }

    @Test
    public void testPerformActionInternal_with_result_no_quantity_returns_false() {
        when(consumptionSupport.getConsumableQuantity(any(OrderEntryRAO.class))).thenReturn(0);
        boolean result = action.performActionInternal(context);
        assertThat(result).isFalse();
    }

    @Test
    public void testPerformActionInternal_with_existing_entry_returns_true() {
        OrderEntryRAO entryRAO3 = new OrderEntryRAO();
        entryRAO3.setProductCode(THL_PRODUCT_CODE);
        entryRAO3.setUg(USERPRICEGROUP);
        entryRAO3.setQuantity(1);
        when(cartRAO.getEntries()).thenReturn(new HashSet<OrderEntryRAO>() {{
            add(entryRAO1);
            add(entryRAO2);
            add(entryRAO3);
        }});
        boolean result = action.performActionInternal(context);
        assertThat(result).isTrue();
    }

}
