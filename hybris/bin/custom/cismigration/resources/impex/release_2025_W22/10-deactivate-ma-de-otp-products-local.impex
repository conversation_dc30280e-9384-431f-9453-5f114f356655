#% impex.enableCodeExecution(true);

$deCc = 049

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]

#% if: !"live".equals("$config-spring.profiles.active") && !"demo".equals("$config-spring.profiles.active") && !"dev".equals("$config-spring.profiles.active");
UPDATE AppLicense; code[unique = true] ; availabilityStatus(code)[default = UNPUBLISHED]; $catalogVersion[unique = true];
                 ; AA2_$deCc1687P15142 ;
                 ; AA2_$deCc1687P15152 ;
                 ; AA2_$deCc1987P12051 ;
                 ; AA2_$deCc1987P12389 ;
                 ; AA2_$deCc1987P12412 ;
#% endif:
