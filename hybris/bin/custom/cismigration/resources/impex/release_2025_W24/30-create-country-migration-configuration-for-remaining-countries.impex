$dateFormat = dd.MM.yyyy
$dateTimeFormat = dd.MM.yyyy HH:mm

INSERT_UPDATE CountryMigrationConfiguration; country(isocode)[unique = true]; contractStartDate[dateformat = $dateTimeFormat, default = 01.01.2026 00:00]; sepaDDRecommended[default = false]; sepaMandateCreationDueDate[dateformat = $dateTimeFormat, default = 01.01.2026 00:00]; firstMigrationNoticeDate[dateformat = $dateTimeFormat, default = 01.01.2026 00:00]; purchaseAllowedFromDate[dateformat = $dateFormat, default = 01.08.2025]; lmpEndDateForFutureDatedContracts[dateformat = $dateTimeFormat, default = 31.12.2025 23:59:59]; allowPurchasesWithoutMigratedContracts[default = false];
                                           ; PT
                                           ; HR
                                           ; SI
                                           ; FI
                                           ; EE
                                           ; LT
                                           ; LV
                                           ; NO
                                           ; SE
                                           ; DK
                                           ; IS
                                           ; BE
                                           ; NL
                                           ; ES
                                           ; GR
                                           ; CY
                                           ; MT
                                           ; FR
