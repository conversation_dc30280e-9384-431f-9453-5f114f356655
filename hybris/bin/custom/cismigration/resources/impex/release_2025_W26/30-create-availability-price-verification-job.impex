#% impex.enableCodeExecution(true);
UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]

INSERT_UPDATE ServicelayerJob; code[unique = true]              ; springId
                             ; availabilityPriceVerificationJob ; availabilityPriceVerificationJobPerformable

INSERT_UPDATE CronJob; code[unique = true]                  ; job(code)                        ; sessionUser(uid); sessionLanguage(isocode); nodeGroup; configuration(code)  ; filesCount; filesDaysOld; filesOperator(code); logToFile
                     ; availabilityPriceVerificationCronJob ; availabilityPriceVerificationJob ; syncuser        ; en                      ; ncf      ; cronConfigForAaStore ; 60        ; 60          ; AND                ; TRUE


INSERT_UPDATE Trigger; cronJob(code)[unique = true]         ; cronExpression
                     ; availabilityPriceVerificationCronJob ; 0 0 10 ? * *


#% if: !"live".equals("$config-spring.profiles.active") && !"demo".equals("$config-spring.profiles.active") && !"dev".equals("$config-spring.profiles.active");
UPDATE CronJob; code[unique = true]                  ; active;
              ; availabilityPriceVerificationCronJob ; false ;
#% endif:
