#% impex.setLocale( Locale.ENGLISH );

# numerical code for LV, used as prefix for product code
$lvCc = 371
$aaCc = 040
$aaPackageName = com.sast.aa.lv.

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf

# please make sure, to use right DELEVELOPER COMPANY for Lithuania (Finland seller) in the right environment.
# live
# $companyId =
# demo
# $companyId = a8d470e0-ee94-41fd-b233-3eb88f9ea7e4
# dev
#$companyId =
# local
$companyId = d13faef4-769e-43e3-94e7-fae2c2009f10

$packDiagnosticDescription = Profesionālās diagnostikas, remonta un tehniskās apkopes uzsākšana. Tas ļauj veikt kompetentu diagnostiku un piedāvā plašu citu funkciju klāstu visiem transportlīdzekļiem, uz kuriem tas attiecas.
$packDiagnosticDescription_en = The entry into professional diagnosis, repair and maintenance. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.
$packDiagnosticDescription_fi = Ohjelmistopaketti ammattimaiseen diagnoostiikkaan, korjaukseen ja huoltoihin. Sen avulla voit suorittaa ohjausyksiköiden diagnostiikan ja kattavan valikoiman muita toimintoja.
$packAdvancedDescription = Nākamais profesionālā darbnīcas aprīkojuma līmenis. Papildus diagnostikas komplektam ir pievienotas instrukcijas un rokasgrāmatas. Connected Repair nodrošina servisa un tehnisko apkopju vēstures saglbāšanu un remonta informāciju.
$packAdvancedDescription_en = The next level of professional workshop equipment. In addition to the Diagnostic package, instructions and manuals are included. Connected Repair provides service and maintenance history and repair information.
$packAdvancedDescription_fi = Laajennettu ohjelmistopaketti korjaamoille. Diagnostiikkapaketin lisäksi mukana on korjausohjeet ja käsikirjat. Connected Repair tarjoaa mahdollisuuden tallentaa laitedokumentit ja huoltotiedot eri työpisteille.
$packMasterDescription = Pilnībā visaptveroša pakete profesionālai transportlīdzekļu diagnostikai. Tā nodrošina visu nepieciešamo informāciju diagnostikai, remontam, apkopei, rezerves daļām, dokumentācijai un datu pārvaldībai. Tehniskais atbalsts palīdz rast risinājumus.
$packMasterDescription_en = The fully comprehensive package for professional vehicle diagnostics. It provides all the necessary information for diagnosis, repair, maintenance, spare parts, documentation and data management. Technical Support assists you in finding solutions.
$packMasterDescription_fi = Laajin ohjelmistopaketti ajoneuvon diagnostiikkaan. Se tarjoaa kaiken tarvittavan ohjainlaitediagnostiikkaa, korjausta, huoltoa, varaosia, dokumentointia ja tiedonhallintaa varten. Tekninen tuki auttaa ratkaisujen löytämisessä.

$packComponentCatDescription = Rezerves daļu kataloga paketē ir iekļautas lietojumprogrammas, funkcijas un automobiļu aprīkojums, kā arī dīzeļsistēmu rezerves daļas un elektrisko agregātu rezerves daļas, ieskaitot arhīvu un elektrisko rezerves daļu ESI[tronic]-F.
$packComponentCatDescription_en = The spare parts catalog package includes the applications, functions and automotive equipment as well as the diesel spare parts and electrical spare parts incl. archive and electrical spare part ESI[tronic]-F.
$packComponentCatDescription_fi = Varaosaluettelo-paketti sisältää ajoneuvotiedot, toiminnot ja varaosatiedot sekä diesel- ja sähkövaraosat, mukaan lukien arkisto ja sähkövaraosat ESI[tronic]-F.
$packComponentRepairDieselDescription = Dīzeļsistēmu pakete sniedz informāciju par rezerves daļām un dīzeļdzinēju un elektrisko agregātu remontu. Tas ļauj identificēt transportlīdzekli, Bosch automobiļu aprīkojumu un ietver remonta instrukcijas un servisa informāciju.
$packComponentRepairDieselDescription_en = The Repair Diesel package provides information on spare parts and repair of diesel and electrical components. It allows identification of the vehicle, Bosch automotive equipment and includes repair instructions & service information.
$packComponentRepairDieselDescription_fi = Diesel korjausohjeet -paketti tarjoaa tietoa varaosista ja diesel- ja sähkökomponenttien korjausohjeet. Sen avulla voidaan tunnistaa ajoneuvo, Boschin autovarusteet ja se sisältää korjausohjeita, sekä huoltotietoja.
$packComponentRepairElectricDescription = Arvien augošais transportlīdzekļu modeļu klāsts apgrūtina autoservisiem aktuālas informācijas par transportlīdzekļa elektriskajām sistēmām iegūšanu. Elektrisko agregātu remonta pakete sniedz iformāciju par automobiļu elektrisko sistēmu rezerves daļu datiem pārskatāmā formātā.
$packComponentRepairElectricDescription_en = The increasing number of vehicle models makes it difficult for workshops to have up-to-date information on vehicle electrical systems at their fingertips. The Repair Electrics Package provides support with spare parts data on car electrics in a clear format.
$packComponentRepairElectricDescription_fi = Ajoneuvomallien määrän lisääntyessä korjaamoiden on vaikea löytää ajantasaiset tiedot ajoneuvojen sähköjärjestelmistä käyttöönsä. Sähkökomponenttien korjausohjeet-paketti tarjoaa tukea autojen sähköjärjestelmiä koskevilla varaosatiedoilla selkeässä muodossa.

$packTruckDescription = Kravas automobiļu pakete palīdz darbnīcām veikt uzticamu diagnostiku, pilnīgu apkopi un efektīvu remontu visiem parastajiem vieglajiem un smagajiem kravas automobiļiem, piekabēm, mikroautobusiem un autobusiem.
$packTruckDescription_en = The Truck package supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.
$packTruckDescription_fi = Truck-paketti tukee korjaamoja kaikkien kevyiden- ja raskaiden hyötyajoneuvojen, perävaunujen, pakettiautojen ja linja-autojen diagnostiikassa, täydellisessä huollossa ja tehokkaassa korjauksessa.

$ohw1Description = Lauksaimniecības tehnikas diagnostikas pakete sniedz informāciju par lauksaimniecības transportlīdzekļu diagnostiku, apkopi un remontu. Komplektā, cita starpā, ir iekļautas hidraulisko sistēmu regulēšanas un parametrizēšanas funkcijas.
$ohw1Description_en = The Agricultural Machinery Diagnostic Package provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw1Description_fi = Maatalouskoneiden diagnostiikkapaketissa on tietoa maatalousajoneuvojen diagnosoinnista, huollosta ja korjauksesta. Mukana on muun muassa hydraulijärjestelmien säätö- ja parametrointitoimintoja.
$ohw2Description = Diagnostikas pakete celtniecības mašīnām un dzinējiem sniedz informāciju par lauksaimniecības transportlīdzekļu diagnostiku, apkopi un remontu. Komplektā, cita starpā, ir iekļautas hidraulisko sistēmu regulēšanas un parametrizēšanas funkcijas.
$ohw2Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw2Description_fi = Maanrakennuskoneiden ja -moottoreiden diagnoosipaketti sisältää tietoa maatalousajoneuvojen diagnosoinnista, huollosta ja korjauksesta. Mukana on muun muassa hydraulijärjestelmien säätö- ja parametrointitoimintoja.

$thlPkwDescription = Vai jums ir nepieciešams tehniskais atbalsts automašīnas apkopei vai remontam, vai vienkārši uzticams otrs viedoklis? Tad sazinieties ar mūsu atbalsta komandu un saņemiet ātrus un pamatotus risinājumus.
$thlPkwDescription_en = Do you need technical support to maintain or repair a car, or simply a reliable second opinion? Then contact our support team and get fast and sound solutions.
$thlPkwDescription_fi = Tarvitsetko teknistä tukea auton huoltoon tai korjaukseen vai vain luotettavan toisen mielipiteen? Ota yhteyttä tukitiimiimme, niin saat nopeat ratkaisut ongelmiisi.

$compacFsa500Description = CompacSoft[plus] for FSA 500 ir aprīkots ar iepriekš iestatītiem komponentu testiem, un to var pievienot esošajām sistēmām, kā arī izmantot, lai pakāpeniski paplašinātu darbnīcas testēšanas sistēmu.
$compacFsa500Description_en = The CompacSoft[plus] for FSA 500 is equipped with preset component tests and can be connected to existing systems, as well as used to gradually expand your workshop test system.
$compacFsa500Description_fi = CompacSoft[plus] FSA 500:lle on varustettu ESI[tronic] esiasetetuilla komponenttitesteillä, ja se voidaan liittää olemassa oleviin järjestelmiin tai sitä voidaan käyttää korjaamon testausjärjestelmän asteittaiseen laajentamiseen.
$compacFsa7xxDescription = Ar CompacSoft[plus] for FSA 7xx visu mērīšanas uzdevumu veikšanas ērtumu transportlīdzeklī vēl vairāk palielina izvēlnes vadīti testa soļi, pēc izvēles transportlīdzeklim specifiski iestatītās vērtības, kā arī faktisko vērtību rādīšana.
$compacFsa7xxDescription_en = With CompacSoft[plus] for FSA 7xx, the comfort for all measuring tasks on the vehicle is further increased by the menu-guided test steps, the optional vehicle-specific set values, as well as the display of the actual values.
$compacFsa7xxDescription_fi = CompacSoft[plus] FSA 7xx:n avulla kaikkien ajoneuvon mittaustehtävien mukavuutta lisäävät entisestään valikko-ohjatut testivaiheet, valinnaiset ajoneuvokohtaiset asetusarvot sekä todellisten arvojen näyttö.

$criDescription = CRI DCI 700 programmatūra sniedz jaunākos datus, nodrošina vienmērīgu procesu norisi un ietver pjezoinžektoru testēšanu common rail sistēmām.
$criDescription_en = CRI for DCI 700 software provides up-to-date data, ensures smooth processes and includes testing of piezo injectors for common rail systems.
$criDescription_fi = CRI DCI 700 -ohjelmisto tarjoaa ajantasaiset tiedot common rail -järjestelmien pietsosuuttimien testaukseen ja varmistaa sujuvat prosessit.
$crinDescription = CRIN DCI 700 programmatūra sniedz jaunākos datus, nodrošina vienmērīgu procesu norisi un ietver common rail sistēmu elektromagnētisko vārstu inžektoru testēšanu.
$crinDescription_en = CRIN for DCI 700 software provides up-to-date data, ensures smooth processes and includes the testing of solenoid valve injectors for common rail systems.
$crinDescription_fi = CRIN DCI 700 -ohjelmisto tarjoaa ajantasaiset tiedot common Rail -järjestelmien magneettiventtiilisuuttimien testaukseen ja varmistaa sujuvat prosessit.

# TODO: UPDATE TRANSLATION
$adasDescription = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.
$adasDescription_en = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.
$adasDescription_fi = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.

$coReDescription = Bosch Connected Repair ir programmatūra, kas savieno darba un attēlus saskaņā ar Vispārīgo datu aizsardzības regulu - CoRe ir pielāgota klientu vajadzībām un var sasaistīt servisa aprīkojuma, transportlīdzekļa un remonta datus. Neatkarīgi no tā, vai runa ir par defektiem vai datu saglabāšanu.
$coReDescription_en = Bosch Connected Repair is software that connects workshop equipment, vehicle and repair data. Whether in the event of malfunctions or the storage of data and images in accordance with the Basic Data Protection Regulation - CoRe has been adapted to the needs of customers.
$coReDescription_fi = Bosch CoRe on ohjelmisto, joka yhdistää korjaamon laitteet, ajoneuvon ja korjaustiedot. Voit luoda työasemakohtaisia työkortteja ja kerää tietoja useista Bosch-laitteista yhdistääksesi ne yhdeksi asiakasraportiksi.

$kts250SDDescription = Ieeja profesionālajā diagnostikā, remontā un uzturēšanā īpaši KTS 250. Tas nodrošina kompetentu elektrisko diagnozi un piedāvā plašu citu funkciju klāstu visiem pārklātajiem transportlīdzekļiem.
$kts250SDDescription_en = The entry into professional diagnosis, repair and maintenance specifically for KTS 250. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.
$kts250SDDescription_fi = Pääsy diagnostiikkaan, sekä korjaus- ja huolto toimintoihin erityisesti KTS 250:lle. Se mahdollistaa tehokkaat diagnostiikkatyöt ja tarjoaa laajan valikoiman muita toimintoja ajoneuvoille.

$infoartWDescription = ESI[tronic] 2.0 Infoart W satur informāciju par dīzeļsistēmu komponentu testa vērtībām rindas sūkņu kombinācijām, kā arī VE sūkņiem, pilnu testa procesa testa procedūru sakot ar izmērīto vērtību noteikšanas līdz atskaites izdrukai un testa soļu attēlojumu optimālā secībā.
$infoartWDescription_en = The ESI[tronic] 2.0 Infoart W contains information about diesel test values for in-line pump combinations as well as for VE pumps, the complete test process test procedure from the determination of the measured values to the printout of the report and the display of the test steps in the optimum sequence.
$infoartWDescription_fi = ESI[tronic] 2.0 W sisältää tiedot testiarvoista diesel rivipumppuyhdistelmille, sekä VE-pumpuille. Se mahdollistaa koko testiprosessin mittausarvojen määrittämisestä raportin tulostamiseen ja testivaiheiden näyttämiseen optimaalisessa järjestyksessä.

$infoartTestdataDescription = ESI[tronic] 2.0-Infotype Testdata (CD) satur testa vērtības Bosch Common Rail augstspiediena sūkņiem, Common Rail iesmidzinātājiem un VP 29 / 30 / 44 sadales sūkņiem.
$infoartTestdataDescription_en = The ESI[tronic] 2.0-Infotype Testdata (CD) contains test values for Bosch Common Rail high pressure pumps, Common Rail injectors and VP 29 / 30 / 44 distributor injection pumps.
$infoartTestdataDescription_fi = ESI[tronic] 2.0-Infotype Testdata (CD) sisältää testiarvot Boschin Common Rail -korkeapainepumpuille, Common Rail -ruiskutussuuttimille ja VP 29 / 30 / 44 -jakajaruiskutuspumpuille.
$truckUpgradeDescription = Kravas automobiļu papildinājums ir paredzēts ESI[tronic] Car lietotājiem un palīdz darbnīcām veikt uzticamu diagnostiku, pilnīgu apkopi un efektīvu remontu visiem parastajiem vieglajiem un smagajiem kravas automobiļiem, piekabēm, mikroautobusiem un autobusiem.
$truckUpgradeDescription_en = The Truck Upgrade package is dedicated to ESI[tronic] Car users and supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.
$truckUpgradeDescription_fi = Truck laajennus -paketti on tarkoitettu henkilöautopuolen ESI[tronic] käyttäjille, ja se tukee korjaamoja kaikkien kevyiden- ja raskaiden hyötyajoneuvojen, perävaunujen, pakettiautojen ja linja-autojen diagnostiikassa, täydellisessä huollossa ja tehokkaassa korjauksessa.

$crr950DieselInjectorsRepairSoftDescription = CRR 950 dīzeļdegvielas sprauslu remonta programmatūra
$crr950DieselInjectorsRepairSoftDescription_en = The CRR 950 guides and support the professional repairs of magnetic solenoid common-rail injectors from Bosch
$crr950DieselInjectorsRepairSoftDescription_fi = CRR 950 ohjaa ja tukee Boschin magneettisten solenoidisten yhteispaineruiskuttimien ammattimaista korjausta. (Lisenssi 1 tietokoneasennukseen)

$compacFsa500Full3YBrimName = CompacSoft[plus] FSA 500 (3 years)
$compacFsa500SubsBrimName = CompacSoft[plus] FSA 500
$compacFsa7xxFull3YBrimName = CompacSoft[plus] FSA 7xx (3 years)
$compacFsa7xxSubsBrimName = CompacSoft[plus] FSA 7xx
$criSubsS1BrimName = Component DCI-CRI
$criSubsM3BrimName = Component DCI-CRI_Multi-users_3
$criFull3YBrimName = Component DCI-CRI (3 year)
$crinSubsS1BrimName = Component DCI-CRIN
$crinSubsM3BrimName = Component DCI-CRIN_Multi-users_3
$crinFull3YBrimName = Component DCI-CRIN (3 year)
$packAdvancedSubsS1BrimName = ESI 2.0 Advanced Unlimited
$packAdvancedSubsM3BrimName = ESI 2.0 Advanced Unlimited Multi
$packAdvancedFull3YS1BrimName = ESI 2.0 Advanced 3 years
$packAdvancedFull3YM3BrimName = ESI 2.0 Advanced 3 years Multi
$packComponentCatSubsS1BrimName = ESI 2.0 ComponentCatalog D+E Unlimited
$packComponentCatSubsM3BrimName = ESI 2.0 ComponentCatalog D+E Unlim Multi
$packComponentCatFull3YS1BrimName = ESI 2.0 ComponentCatalog D+E 3 years
$packComponentCatFull3YM3BrimName = ESI 2.0 ComponentCatalog D+E 3y Multi
$packComponentRepairDieselSubsS1BrimName = ESI 2.0 ComponentRepair D+E Unlimited
$packComponentRepairDieselSubsM3BrimName = ESI 2.0 ComponentRepair D+E Unlim Multi
$packComponentRepairDieselFull3YS1BrimName = ESI 2.0 ComponentRepair D+E 3 years
$packComponentRepairDieselFull3YM3BrimName = ESI 2.0 ComponentRepair D+E 3y Multi
$packComponentRepairElectricSubsS1BrimName = ESI 2.0 ComponentRepair E Unlimited
$packComponentRepairElectricSubsM3BrimName = ESI 2.0 ComponentRepair E Unlim Multi
$packComponentRepairElectricFull3YS1BrimName = ESI 2.0 ComponentRepair E 3 years
$packComponentRepairElectricFull3YM3BrimName = ESI 2.0 ComponentRepair E 3 years Multi
$packDiagnosticSubsS1BrimName = ESI 2.0 Diagnostic Unlimited
$packDiagnosticSubsM3BrimName = ESI 2.0 Diagnostic Unlimited Multi
$packDiagnosticFull3YS1BrimName = ESI 2.0 Diagnostic 3 years
$packDiagnosticFull3YM3BrimName = ESI 2.0 Diagnostic 3 years Multi
$packMasterSubsS1BrimName = ESI 2.0 Master Unlimited
$packMasterSubsM3BrimName = ESI 2.0 Master Unlimited Multi
$packMasterFull3YS1BrimName = ESI 2.0 Master 3 years
$packMasterFull3YM3BrimName = ESI 2.0 Master 3 years Multi

$ohw1SubsS1BrimName = ESI[tronic] 2.0 Truck OHW I
$ohw1SubsM3BrimName = ESI[tronic] 2.0 Truck OHW I Multi
$ohw1Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW I (3 years)
$ohw1Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW I (3y) Multi

$ohw2SubsS1BrimName = ESI[tronic] 2.0 Truck OHW II
$ohw2SubsM3BrimName = ESI[tronic] 2.0 Truck OHW II Multi
$ohw2Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW II (3 years)
$ohw2Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW II (3y) Multi

$packTruckSubsS1BrimName = ESI[tronic] 2.0 Truck
$packTruckSubsM3BrimName = ESI[tronic] 2.0 Truck Multi
$packTruckFull3YS1BrimName = ESI[tronic] 2.0 Truck (3 years)
$packTruckFull3YM3BrimName = ESI[tronic] 2.0 Truck (3 years) Multi
$packTruckFullBrimName = ESI[tronic] 2.0 Truck (Einmalkauf)
$infoartWSubsBrimName = ESI[tronic] W Diesel Test Data (WP)
$infoartWFull3YBrimName = ESI[tronic] W (3 Jahre)
$thlPkwSubsBrimName = THL use technical hotline
$thlPkwFull3YBrimName = Technische Hotline LKW (3 Jahre)
$thlTrkSubsBrimName = Technische Hotline ESI[tronic] for Truck
$thlPkwTrkSubsBrimName = Technische Hotline fuer LKWs und PKWs
$infoartTestdataSubsBrimName = Testdata VP-M/CP
$truckUpgradeSubsS1BrimName = ESI[tronic] 2.0 Truck Upgrade
$truckUpgradeSubsM3BrimName = ESI[tronic] 2.0 Truck Upgrade Multi
$truckUpgradeFull3YS1BrimName = ESI[tronic] 2.0 Truck Upgrade (3 years)
$truckUpgradeFull3YM3BrimName = ESI[tronic] 2.0 Truck Upgrade (3y) Multi
$coreSubsS1BrimName = CoRe for ESI 2.0 packages
$kts250SDFullBrimName = KTS 250 SD (OTP one time purchase)
$kts250SDSubsBrimName = KTS 250 SD ECU Diagnosis

$crr950DieselInjectorsRepairSoftwareBrimName1L = CRR 950 Diesel Injectors (1 License)
$crr950DieselInjectorsRepairSoftwareBrimName3L = CRR 950 Diesel Injectors (3 Licenses)

$ohw3Description = In dem Diagnose Paket Baumaschinen und Motoren stehen Informationen zur Diagnose, Wartung und Reparatur von landwirtschaftlich genutzten Fahrzeugen zur Verfügung. Enthalten sind unter anderem Einstell- und Parametrierfunktionen an Hydrauliksystemen.
$ohw3Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.

$ohw3S1BrimName = ESI[tronic] Truck OHW III Unlimited
$ohw3M3BrimName = ESI[tronic] Truck OHW III Unlimited Multi
$ohw3Full3YM3BrimName = ESI[tronic] Truck OHW III (3y) Multi

$adasSubsS1BrimName = ADAS ONE - DATA
$adasFull3YS1BrimName = ADAS ONE - DATA (3 years)
$fsa7xxMulti = CompacSoft[plus] FSA 7xx Multi
$fsa7xx3YearsMulti = CompacSoft[plus] FSA 7xx (3 years) Multi
$fsa5xxMulti = CompacSoft[plus] FSA 500 Multi
$fsa5xx3YearsMulti = CompacSoft[plus] FSA 500 (3 years)Multi
$countryRestricted = true
# @formatter:off
$enabledIn = LV
# @formatter:on


INSERT_UPDATE App; code[unique = true]       ; packageName                    ; name[lang = lv]                          ; summary[lang = lv]                          ; description[lang = lv]                      ; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$lvCc1687_CSFSA500    ; $aaPackageName1687_CSFSA500    ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description                    ; $compacFsa500Description                    ;
                 ; AA2_$lvCc1687_CSFSA7XX    ; $aaPackageName1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription                    ; $compacFsa7xxDescription                    ;
                 ; AA2_$lvCc1687_DCICRI      ; $aaPackageName1687_DCICRI      ; Component DCI-CRI                        ; $criDescription                             ; $criDescription                             ;
                 ; AA2_$lvCc1687_DCICRIN     ; $aaPackageName1687_DCICRIN     ; Component DCI-CRIN                       ; $crinDescription                            ; $crinDescription                            ;
                 ; AA2_$lvCc1987_ESIADV      ; $aaPackageName1987_ESIADV      ; ESI[tronic] 2.0 Advanced                 ; $packAdvancedDescription                    ; $packAdvancedDescription                    ;
                 ; AA2_$lvCc1987_ESIREPCAT   ; $aaPackageName1987_ESIREPCAT   ; ESI[tronic] 2.0 ComponentCat D+E         ; $packComponentCatDescription                ; $packComponentCatDescription                ;
                 ; AA2_$lvCc1987_ESIREPD     ; $aaPackageName1987_ESIREPD     ; ESI[tronic] 2.0 ComponentRepair D+E      ; $packComponentRepairDieselDescription       ; $packComponentRepairDieselDescription       ;
                 ; AA2_$lvCc1987_ESIREPE     ; $aaPackageName1987_ESIREPE     ; ESI[tronic] 2.0 ComponentRepair E        ; $packComponentRepairElectricDescription     ; $packComponentRepairElectricDescription     ;
                 ; AA2_$lvCc1987_ESIDIAG     ; $aaPackageName1987_ESIDIAG     ; ESI[tronic] 2.0 Diagnostic               ; $packDiagnosticDescription                  ; $packDiagnosticDescription                  ;
                 ; AA2_$lvCc1987_ESIMASTER   ; $aaPackageName1987_ESIMASTER   ; ESI[tronic] 2.0 Master                   ; $packMasterDescription                      ; $packMasterDescription                      ;
                 ; AA2_$lvCc1987_TRKOHW1     ; $aaPackageName1987_TRKOHW1     ; ESI[tronic] 2.0 Truck OHW I              ; $ohw1Description                            ; $ohw1Description                            ;
                 ; AA2_$lvCc1987_TRKOHW2     ; $aaPackageName1987_TRKOHW2     ; ESI[tronic] 2.0 Truck OHW II             ; $ohw2Description                            ; $ohw2Description                            ;
                 ; AA2_$lvCc1987_TRKTRUCK    ; $aaPackageName1987_TRKTRUCK    ; ESI[tronic] 2.0 Truck                    ; $packTruckDescription                       ; $packTruckDescription                       ;
                 ; AA2_$lvCc1987_TSTINFOAW   ; $aaPackageName1987_TSTINFOAW   ; ESI[tronic] W                            ; $infoartWDescription                        ; $infoartWDescription                        ;
                 ; AA2_$lvCc1687_TSTINFODAT  ; $aaPackageName1687_TSTINFODAT  ; Testdata VP-M/CP                         ; $infoartTestdataDescription                 ; $infoartTestdataDescription                 ;
                 ; AA2_$lvCc1987_THLPKW      ; $aaPackageName1987_THLPKW      ; Technical Hotline Car                    ; $thlPkwDescription                          ; $thlPkwDescription                          ;
                 ; AA2_$lvCc1987_TRKUPG      ; $aaPackageName1987_TRUCKUPG    ; ESI[tronic] 2.0 Truck Upgrade            ; $truckUpgradeDescription                    ; $truckUpgradeDescription                    ;
                 ; AA2_$lvCc1687_CORE_ESIPKG ; $aaPackageName1687_CORE_ESIPKG ; CoRe Server + Online                     ; $coReDescription                            ; $coReDescription                            ;
                 ; AA2_$lvCc1987_KTS250SD    ; $aaPackageName1987_KTS250      ; KTS 250 SD ECU diagnoze                  ; $kts250SDDescription                        ; $kts250SDDescription                        ;
                 ; AA2_$lvCc1687_CRR950      ; $aaPackageName1687_CRR950      ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription ; $crr950DieselInjectorsRepairSoftDescription ;
                 ; AA2_$lvCc1987_TRKOHW3     ; $aaPackageName1987_TRKOHW3     ; ESI[tronic] Truck OHW III                ; $ohw3Description                            ; $ohw3Description                            ;
                 ; AA2_$lvCc1987_ADAS_ONE    ; $aaPackageName1987_ADAS_ONE    ; ADAS One Solution                        ; $adasDescription                            ; $adasDescription                            ;


INSERT_UPDATE App; code[unique = true]       ; name[lang = en]                          ; summary[lang = en]                             ; description[lang = en]; $catalogVersion[unique = true];
                 ; AA2_$lvCc1687_CSFSA500    ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description_en                    ; $compacFsa500Description_en
                 ; AA2_$lvCc1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription_en                    ; $compacFsa7xxDescription_en
                 ; AA2_$lvCc1687_DCICRI      ; Component DCI-CRI                        ; $criDescription_en                             ; $criDescription_en
                 ; AA2_$lvCc1687_DCICRIN     ; Component DCI-CRIN                       ; $crinDescription_en                            ; $crinDescription_en
                 ; AA2_$lvCc1987_ESIADV      ; ESI[tronic] 2.0 Advanced                 ; $packAdvancedDescription_en                    ; $packAdvancedDescription_en
                 ; AA2_$lvCc1987_ESIREPCAT   ; ESI[tronic] 2.0 ComponentCat D+E         ; $packComponentCatDescription_en                ; $packComponentCatDescription_en
                 ; AA2_$lvCc1987_ESIREPD     ; ESI[tronic] 2.0 ComponentRepair D+E      ; $packComponentRepairDieselDescription_en       ; $packComponentRepairDieselDescription_en
                 ; AA2_$lvCc1987_ESIREPE     ; ESI[tronic] 2.0 ComponentRepair E        ; $packComponentRepairElectricDescription_en     ; $packComponentRepairElectricDescription_en
                 ; AA2_$lvCc1987_ESIDIAG     ; ESI[tronic] 2.0 Diagnostic               ; $packDiagnosticDescription_en                  ; $packDiagnosticDescription_en
                 ; AA2_$lvCc1987_ESIMASTER   ; ESI[tronic] 2.0 Master                   ; $packMasterDescription_en                      ; $packMasterDescription_en
                 ; AA2_$lvCc1987_TRKOHW1     ; ESI[tronic] 2.0 Truck OHW I              ; $ohw1Description_en                            ; $ohw1Description_en
                 ; AA2_$lvCc1987_TRKOHW2     ; ESI[tronic] 2.0 Truck OHW II             ; $ohw2Description_en                            ; $ohw2Description_en
                 ; AA2_$lvCc1987_TRKTRUCK    ; ESI[tronic] 2.0 Truck                    ; $packTruckDescription_en                       ; $packTruckDescription_en
                 ; AA2_$lvCc1987_TSTINFOAW   ; ESI[tronic] W                            ; $infoartWDescription_en                        ; $infoartWDescription_en
                 ; AA2_$lvCc1687_TSTINFODAT  ; Testdata VP-M/CP                         ; $infoartTestdataDescription_en                 ; $infoartTestdataDescription_en
                 ; AA2_$lvCc1987_THLPKW      ; Technical Hotline Cars                   ; $thlPkwDescription_en                          ; $thlPkwDescription_en
                 ; AA2_$lvCc1987_TRKUPG      ; ESI[tronic] 2.0 Truck Upgrade            ; $truckUpgradeDescription_en                    ; $truckUpgradeDescription_en
                 ; AA2_$lvCc1687_CORE_ESIPKG ; CoRe Server + Online                     ; $coReDescription_en                            ; $coReDescription_en
                 ; AA2_$lvCc1987_KTS250SD    ; KTS 250 SD ECU Diagnosis                 ; $kts250SDDescription_en                        ; $kts250SDDescription_en
                 ; AA2_$lvCc1687_CRR950      ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription_en ; $crr950DieselInjectorsRepairSoftDescription_en
                 ; AA2_$lvCc1987_TRKOHW3     ; ESI[tronic] Truck OHW III                ; $ohw3Description_en                            ; $ohw3Description_en   ;
                 ; AA2_$lvCc1987_ADAS_ONE    ; ADAS One Solution                        ; $adasDescription_en                            ; $adasDescription_en   ;

INSERT_UPDATE App; code[unique = true]       ; name[lang = fi]                          ; summary[lang = fi]                             ; description[lang = fi]; $catalogVersion[unique = true];
                 ; AA2_$lvCc1687_CSFSA500    ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description_fi                    ; $compacFsa500Description_fi
                 ; AA2_$lvCc1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription_fi                    ; $compacFsa7xxDescription_fi
                 ; AA2_$lvCc1687_DCICRI      ; Testiarvot DCI-CRI                       ; $criDescription_fi                             ; $criDescription_fi
                 ; AA2_$lvCc1687_DCICRIN     ; Testiarvot DCI-CRIN                      ; $crinDescription_fi                            ; $crinDescription_fi
                 ; AA2_$lvCc1987_ESIADV      ; ESI[tronic] 2.0 Advanced                 ; $packAdvancedDescription_fi                    ; $packAdvancedDescription_fi
                 ; AA2_$lvCc1987_ESIREPCAT   ; ESI[tronic] 2.0 Luettelo D+E             ; $packComponentCatDescription_fi                ; $packComponentCatDescription_fi
                 ; AA2_$lvCc1987_ESIREPD     ; ESI[tronic] 2.0 Komponenttikorjaus D+E   ; $packComponentRepairDieselDescription_fi       ; $packComponentRepairDieselDescription_fi
                 ; AA2_$lvCc1987_ESIREPE     ; ESI[tronic] 2.0 Komponenttikorjaus E     ; $packComponentRepairElectricDescription_fi     ; $packComponentRepairElectricDescription_fi
                 ; AA2_$lvCc1987_ESIDIAG     ; ESI[tronic] 2.0 Diagnostiikka            ; $packDiagnosticDescription_fi                  ; $packDiagnosticDescription_fi
                 ; AA2_$lvCc1987_ESIMASTER   ; ESI[tronic] 2.0 Master                   ; $packMasterDescription_fi                      ; $packMasterDescription_fi
                 ; AA2_$lvCc1987_TRKOHW1     ; ESI[tronic] 2.0 OHW I                    ; $ohw1Description_fi                            ; $ohw1Description_fi
                 ; AA2_$lvCc1987_TRKOHW2     ; ESI[tronic] 2.0 OHW II                   ; $ohw2Description_fi                            ; $ohw2Description_fi
                 ; AA2_$lvCc1987_TRKTRUCK    ; ESI[tronic] 2.0 Truck                    ; $packTruckDescription_fi                       ; $packTruckDescription_fi
                 ; AA2_$lvCc1987_TSTINFOAW   ; ESI[tronic] W                            ; $infoartWDescription_fi                        ; $infoartWDescription_fi
                 ; AA2_$lvCc1687_TSTINFODAT  ; Testdata VP-M/CP                         ; $infoartTestdataDescription_fi                 ; $infoartTestdataDescription_fi
                 ; AA2_$lvCc1987_THLPKW      ; Autotekninen neuvonta, henkilöautot      ; $thlPkwDescription_fi                          ; $thlPkwDescription_fi ;
                 ; AA2_$lvCc1987_TRKUPG      ; ESI[tronic] 2.0 Truck laajennus          ; $truckUpgradeDescription_fi                    ; $truckUpgradeDescription_fi
                 ; AA2_$lvCc1687_CORE_ESIPKG ; Core - Connected Repair                  ; $coReDescription_fi                            ; $coReDescription_fi
                 ; AA2_$lvCc1987_KTS250SD    ; KTS 250 SD ECU -diagnostiikka            ; $kts250SDDescription_fi                        ; $kts250SDDescription_fi
                 ; AA2_$lvCc1687_CRR950      ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription_fi ; $crr950DieselInjectorsRepairSoftDescription_fi
                 ; AA2_$lvCc1987_TRKOHW3     ; ESI[tronic] Truck OHW III                ; $ohw3Description                               ; $ohw3Description      ;
                 ; AA2_$lvCc1987_ADAS_ONE    ; ADAS One Solution                        ; $adasDescription_fi                            ; $adasDescription_fi   ;



INSERT_UPDATE ProductContainer; code[unique = true]        ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$lvCc1687_CSFSA500    ; AA2_$lvCc1687_CSFSA500    ;
                              ; pcaa_$lvCc1687_CSFSA7XX    ; AA2_$lvCc1687_CSFSA7XX    ;
                              ; pcaa_$lvCc1687_DCICRI      ; AA2_$lvCc1687_DCICRI      ;
                              ; pcaa_$lvCc1687_DCICRIN     ; AA2_$lvCc1687_DCICRIN     ;
                              ; pcaa_$lvCc1987_ESIADV      ; AA2_$lvCc1987_ESIADV      ;
                              ; pcaa_$lvCc1987_ESIREPCAT   ; AA2_$lvCc1987_ESIREPCAT   ;
                              ; pcaa_$lvCc1987_ESIREPD     ; AA2_$lvCc1987_ESIREPD     ;
                              ; pcaa_$lvCc1987_ESIREPE     ; AA2_$lvCc1987_ESIREPE     ;
                              ; pcaa_$lvCc1987_ESIDIAG     ; AA2_$lvCc1987_ESIDIAG     ;
                              ; pcaa_$lvCc1987_ESIMASTER   ; AA2_$lvCc1987_ESIMASTER   ;
                              ; pcaa_$lvCc1987_TRKOHW1     ; AA2_$lvCc1987_TRKOHW1     ;
                              ; pcaa_$lvCc1987_TRKOHW2     ; AA2_$lvCc1987_TRKOHW2     ;
                              ; pcaa_$lvCc1987_TRKTRUCK    ; AA2_$lvCc1987_TRKTRUCK    ;
                              ; pcaa_$lvCc1987_TSTINFOAW   ; AA2_$lvCc1987_TSTINFOAW   ;
                              ; pcaa_$lvCc1687_TSTINFODAT  ; AA2_$lvCc1687_TSTINFODAT  ;
                              ; pcaa_$lvCc1987_TRKUPG      ; AA2_$lvCc1987_TRKUPG      ;
                              ; pcaa_$lvCc1987_THLPKW      ; AA2_$lvCc1987_THLPKW      ;
                              ; pcaa_$lvCc1687_CORE_ESIPKG ; AA2_$lvCc1687_CORE_ESIPKG ;
                              ; pcaa_$lvCc1987_KTS250SD    ; AA2_$lvCc1987_KTS250SD    ;
                              ; pcaa_$lvCc1687_CRR950      ; AA2_$lvCc1687_CRR950      ;
                              ; pcaa_$lvCc1987_TRKOHW3     ; AA2_$lvCc1987_TRKOHW3     ;
                              ; pcaa_$lvCc1987_ADAS_ONE    ; AA2_$lvCc1987_ADAS_ONE    ;

INSERT_UPDATE AppLicense; code[unique = true] ; $baseProduct              ; sellerProductId; brimName[lang = en]                            ; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); specifiedPrice; enabledCountries(isocode)[default = $enabledIn]; billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; availabilityStatus(code)[default = PUBLISHED]
                        ; AA2_$lvCc1687P15063 ; AA2_$lvCc1687_CSFSA500    ; 1687P15063     ; $compacFsa500Full3YBrimName                    ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 232.00        ; $enabledIn
                        ; AA2_$lvCc1687P15060 ; AA2_$lvCc1687_CSFSA500    ; 1687P15060     ; $compacFsa500SubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 110.00        ; $enabledIn
                        ; AA2_$lvCc1687P15048 ; AA2_$lvCc1687_CSFSA7XX    ; 1687P15048     ; $compacFsa7xxFull3YBrimName                    ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 897.60        ; $enabledIn
                        ; AA2_$lvCc1687P15045 ; AA2_$lvCc1687_CSFSA7XX    ; 1687P15045     ; $compacFsa7xxSubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 340.00        ; $enabledIn
                        ; AA2_$lvCc1687P15093 ; AA2_$lvCc1687_DCICRI      ; 1687P15093     ; $criFull3YBrimName                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 871.00        ; $enabledIn
                        ; AA2_$lvCc1687P15090 ; AA2_$lvCc1687_DCICRI      ; 1687P15090     ; $criSubsS1BrimName                             ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 330.00        ; $enabledIn
                        ; AA2_$lvCc1687P15102 ; AA2_$lvCc1687_DCICRI      ; 1687P15102     ; $criSubsM3BrimName                             ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 400.00        ; $enabledIn
                        ; AA2_$lvCc1687P15103 ; AA2_$lvCc1687_DCICRIN     ; 1687P15103     ; $crinFull3YBrimName                            ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 871.00        ; $enabledIn
                        ; AA2_$lvCc1687P15100 ; AA2_$lvCc1687_DCICRIN     ; 1687P15100     ; $crinSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 330.00        ; $enabledIn
                        ; AA2_$lvCc1687P15107 ; AA2_$lvCc1687_DCICRIN     ; 1687P15107     ; $crinSubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 440.00        ; $enabledIn
                        ; AA2_$lvCc1987P12843 ; AA2_$lvCc1987_ESIADV      ; 1987P12843     ; $packAdvancedFull3YS1BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 2363.00       ; $enabledIn
                        ; AA2_$lvCc1987P12840 ; AA2_$lvCc1987_ESIADV      ; 1987P12840     ; $packAdvancedSubsS1BrimName                    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 895.00        ; $enabledIn
                        ; AA2_$lvCc1987P12846 ; AA2_$lvCc1987_ESIADV      ; 1987P12846     ; $packAdvancedFull3YM3BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 2706.00       ; $enabledIn
                        ; AA2_$lvCc1987P12847 ; AA2_$lvCc1987_ESIADV      ; 1987P12847     ; $packAdvancedSubsM3BrimName                    ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1025.00       ; $enabledIn
                        ; AA2_$lvCc1987P12988 ; AA2_$lvCc1987_ESIREPCAT   ; 1987P12988     ; $packComponentCatFull3YS1BrimName              ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 475.00        ; $enabledIn
                        ; AA2_$lvCc1987P12998 ; AA2_$lvCc1987_ESIREPCAT   ; 1987P12998     ; $packComponentCatSubsS1BrimName                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 180.00        ; $enabledIn
                        ; AA2_$lvCc1987P12783 ; AA2_$lvCc1987_ESIREPCAT   ; 1987P12783     ; $packComponentCatFull3YM3BrimName              ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 660.00        ; $enabledIn
                        ; AA2_$lvCc1987P12784 ; AA2_$lvCc1987_ESIREPCAT   ; 1987P12784     ; $packComponentCatSubsM3BrimName                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 250.00        ; $enabledIn
                        ; AA2_$lvCc1987P12973 ; AA2_$lvCc1987_ESIREPD     ; 1987P12973     ; $packComponentRepairDieselFull3YS1BrimName     ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1082.00       ; $enabledIn
                        ; AA2_$lvCc1987P12970 ; AA2_$lvCc1987_ESIREPD     ; 1987P12970     ; $packComponentRepairDieselSubsS1BrimName       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 410.00        ; $enabledIn
                        ; AA2_$lvCc1987P12296 ; AA2_$lvCc1987_ESIREPD     ; 1987P12296     ; $packComponentRepairDieselFull3YM3BrimName     ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1267.00       ; $enabledIn
                        ; AA2_$lvCc1987P12297 ; AA2_$lvCc1987_ESIREPD     ; 1987P12297     ; $packComponentRepairDieselSubsM3BrimName       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 480.00        ; $enabledIn
                        ; AA2_$lvCc1987P12993 ; AA2_$lvCc1987_ESIREPE     ; 1987P12993     ; $packComponentRepairElectricFull3YS1BrimName   ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 502.00        ; $enabledIn
                        ; AA2_$lvCc1987P12990 ; AA2_$lvCc1987_ESIREPE     ; 1987P12990     ; $packComponentRepairElectricSubsS1BrimName     ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 190.00        ; $enabledIn
                        ; AA2_$lvCc1987P12294 ; AA2_$lvCc1987_ESIREPE     ; 1987P12294     ; $packComponentRepairElectricFull3YM3BrimName   ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 686.00        ; $enabledIn
                        ; AA2_$lvCc1987P12295 ; AA2_$lvCc1987_ESIREPE     ; 1987P12295     ; $packComponentRepairElectricSubsM3BrimName     ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 260.00        ; $enabledIn
                        ; AA2_$lvCc1987P12823 ; AA2_$lvCc1987_ESIDIAG     ; 1987P12823     ; $packDiagnosticFull3YS1BrimName                ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1465.00       ; $enabledIn
                        ; AA2_$lvCc1987P12820 ; AA2_$lvCc1987_ESIDIAG     ; 1987P12820     ; $packDiagnosticSubsS1BrimName                  ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 555.00        ; $enabledIn
                        ; AA2_$lvCc1987P12822 ; AA2_$lvCc1987_ESIDIAG     ; 1987P12822     ; $packDiagnosticFull3YM3BrimName                ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1808.40       ; $enabledIn
                        ; AA2_$lvCc1987P12824 ; AA2_$lvCc1987_ESIDIAG     ; 1987P12824     ; $packDiagnosticSubsM3BrimName                  ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 685.00        ; $enabledIn
                        ; AA2_$lvCc1987P12913 ; AA2_$lvCc1987_ESIMASTER   ; 1987P12913     ; $packMasterFull3YS1BrimName                    ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3538.00       ; $enabledIn
                        ; AA2_$lvCc1987P12910 ; AA2_$lvCc1987_ESIMASTER   ; 1987P12910     ; $packMasterSubsS1BrimName                      ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1340.00       ; $enabledIn
                        ; AA2_$lvCc1987P12916 ; AA2_$lvCc1987_ESIMASTER   ; 1987P12916     ; $packMasterFull3YM3BrimName                    ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 3880.80       ; $enabledIn
                        ; AA2_$lvCc1987P12917 ; AA2_$lvCc1987_ESIMASTER   ; 1987P12917     ; $packMasterSubsM3BrimName                      ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1470.00       ; $enabledIn
                        ; AA2_$lvCc1987P12263 ; AA2_$lvCc1987_TRKOHW1     ; 1987P12263     ; $ohw1Full3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1769.00       ; $enabledIn
                        ; AA2_$lvCc1987P12260 ; AA2_$lvCc1987_TRKOHW1     ; 1987P12260     ; $ohw1SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 670.00        ; $enabledIn
                        ; AA2_$lvCc1987P12262 ; AA2_$lvCc1987_TRKOHW1     ; 1987P12262     ; $ohw1SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 800.00        ; $enabledIn
                        ; AA2_$lvCc1987P12265 ; AA2_$lvCc1987_TRKOHW1     ; 1987P12265     ; $ohw1Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 2112.00       ; $enabledIn
                        ; AA2_$lvCc1987P12280 ; AA2_$lvCc1987_TRKOHW2     ; 1987P12280     ; $ohw2Full3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3155.00       ; $enabledIn
                        ; AA2_$lvCc1987P12278 ; AA2_$lvCc1987_TRKOHW2     ; 1987P12278     ; $ohw2SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1195.00       ; $enabledIn
                        ; AA2_$lvCc1987P12275 ; AA2_$lvCc1987_TRKOHW2     ; 1987P12275     ; $ohw2SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1325.00       ; $enabledIn
                        ; AA2_$lvCc1987P12276 ; AA2_$lvCc1987_TRKOHW2     ; 1987P12276     ; $ohw2Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 3498.00       ; $enabledIn
                        ; AA2_$lvCc1987P12402 ; AA2_$lvCc1987_TRKTRUCK    ; 1987P12402     ; $packTruckFull3YS1BrimName                     ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3564.00       ; $enabledIn
                        ; AA2_$lvCc1987P12400 ; AA2_$lvCc1987_TRKTRUCK    ; 1987P12400     ; $packTruckSubsS1BrimName                       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1350.00       ; $enabledIn
                        ; AA2_$lvCc1987P12936 ; AA2_$lvCc1987_TRKTRUCK    ; 1987P12936     ; $packTruckSubsM3BrimName                       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1480.00       ; $enabledIn
                        ; AA2_$lvCc1987P12937 ; AA2_$lvCc1987_TRKTRUCK    ; 1987P12937     ; $packTruckFull3YM3BrimName                     ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 3907.00       ; $enabledIn
                        ; AA2_$lvCc1987P12412 ; AA2_$lvCc1987_TRKTRUCK    ; 1987P12412     ; $packTruckFullBrimName                         ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 3000.00       ; $enabledIn
                        ; AA2_$lvCc1987P12503 ; AA2_$lvCc1987_TSTINFOAW   ; 1987P12503     ; $infoartWFull3YBrimName                        ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 924.00        ; $enabledIn
                        ; AA2_$lvCc1987P12500 ; AA2_$lvCc1987_TSTINFOAW   ; 1987P12500     ; $infoartWSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; 350.00        ; $enabledIn
                        ; AA2_$lvCc1987729274 ; AA2_$lvCc1987_THLPKW      ; 1987729274     ; $thlPkwSubsBrimName                            ;                                          ; runtime_subs_unlimited ; <ignore>        ; 1.00          ; $enabledIn
                        ; AA2_$lvCc1687P15015 ; AA2_$lvCc1687_TSTINFODAT  ; 1687P15015     ; $infoartTestdataSubsBrimName                   ;                                          ; runtime_subs_unlimited ; <ignore>        ; 330.00        ; $enabledIn
                        ; AA2_$lvCc1987P12404 ; AA2_$lvCc1987_TRKUPG      ; 1987P12404     ; $truckUpgradeSubsS1BrimName                    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1200.00       ; $enabledIn
                        ; AA2_$lvCc1987P12140 ; AA2_$lvCc1987_TRKUPG      ; 1987P12140     ; $truckUpgradeFull3YS1BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3168.00       ; $enabledIn
                        ; AA2_$lvCc1987P12359 ; AA2_$lvCc1987_TRKUPG      ; 1987P12359     ; $truckUpgradeSubsM3BrimName                    ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1330.00       ; $enabledIn
                        ; AA2_$lvCc1987P12364 ; AA2_$lvCc1987_TRKUPG      ; 1987P12364     ; $truckUpgradeFull3YM3BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 3511.00       ; $enabledIn
                        ; AA2_$lvCc1687P15130 ; AA2_$lvCc1687_CORE_ESIPKG ; 1687P15130     ; $coreSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1.20          ; $enabledIn
                        ; AA2_$lvCc1987P12389 ; AA2_$lvCc1987_KTS250SD    ; 1987P12389     ; $kts250SDFullBrimName                          ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 600.00        ; $enabledIn
                        ; AA2_$lvCc1987P12385 ; AA2_$lvCc1987_KTS250SD    ; 1987P12385     ; $kts250SDSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; 490.00        ; $enabledIn
                        ; AA2_$lvCc1687P15137 ; AA2_$lvCc1687_CRR950      ; 1687P15137     ; $crr950DieselInjectorsRepairSoftwareBrimName1L ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 100           ; $enabledIn
                        ; AA2_$lvCc1687P15139 ; AA2_$lvCc1687_CRR950      ; 1687P15139     ; $crr950DieselInjectorsRepairSoftwareBrimName3L ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 200           ; $enabledIn
                        ; AA2_$lvCc1987P12254 ; AA2_$lvCc1987_TRKOHW3     ; 1987P12254     ; $ohw3S1BrimName                                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 380           ; $enabledIn
                        ; AA2_$lvCc1987P12257 ; AA2_$lvCc1987_TRKOHW3     ; 1987P12257     ; $ohw3M3BrimName                                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 484           ; $enabledIn
                        ; AA2_$lvCc1987P12256 ; AA2_$lvCc1987_TRKOHW3     ; 1987P12256     ; $ohw3Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1597          ; $enabledIn
                        ; AA2_$lvCc1987P12515 ; AA2_$lvCc1987_ADAS_ONE    ; 1987P12515     ; $adasSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 0.01          ; $enabledIn
                        ; AA2_$lvCc1987P12517 ; AA2_$lvCc1987_ADAS_ONE    ; 1987P12517     ; $adasFull3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 0.01          ; $enabledIn
                        ; AA2_$lvCc1687P15170 ; AA2_$lvCc1687_CSFSA500    ; 1687P15170     ; $fsa5xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$lvCc1687P15173 ; AA2_$lvCc1687_CSFSA500    ; 1687P15173     ; $fsa5xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;
                        ; AA2_$lvCc1687P15160 ; AA2_$lvCc1687_CSFSA7XX    ; 1687P15160     ; $fsa7xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$lvCc1687P15163 ; AA2_$lvCc1687_CSFSA7XX    ; 1687P15163     ; $fsa7xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;

# The licenses below were discontinued by MA and should be available only for the contract migration.
UPDATE AppLicense; code[unique = true] ; $catalogVersion[unique = true]; availabilityStatus(code)[default = UNPUBLISHED]
                 ; AA2_$lvCc1987P12389 ;
                 ; AA2_$lvCc1987P12412 ;

UPDATE AppLicense; code[unique = true] ; userGroups(uid); $catalogVersion[unique = true]
                 ; AA2_$lvCc1687P15063 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1687P15060 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1687P15048 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1687P15045 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1687P15093 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1687P15090 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1687P15102 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1687P15103 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1687P15100 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1687P15107 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12843 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12840 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12846 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12847 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12988 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12998 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12783 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12784 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12973 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12970 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12296 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12297 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12993 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12990 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12294 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12295 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12823 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12820 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12822 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12824 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12913 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12910 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12916 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12917 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12263 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12260 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12262 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12265 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12280 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12278 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12275 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12276 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12402 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12400 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12936 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12937 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12412 ;                ;
                 ; AA2_$lvCc1987P12503 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12500 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987729274 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1687P15015 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12404 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12140 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12359 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12364 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1687P15130 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12389 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12385 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1687P15137 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1687P15139 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12254 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12257 ; IDW000,WD0001  ;
                 ; AA2_$lvCc1987P12256 ; IDW000,WD0001  ;
# The group assignment for the ADAS ONE is most probably not final. we're missing the prices for now
                 ; AA2_$lvCc1987P12515 ; IDW000         ;
                 ; AA2_$lvCc1987P12517 ; IDW000         ;
                 ; AA2_$lvCc1687P15163 ; IDW000         ;
                 ; AA2_$lvCc1687P15170 ; IDW000         ;
                 ; AA2_$lvCc1687P15160 ; IDW000         ;
                 ; AA2_$lvCc1687P15173 ; IDW000         ;


INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$lvCc1687P15060                          ; 110.00
                      ; AA2_$lvCc1687P15048                          ; 897.60
                      ; AA2_$lvCc1687P15045                          ; 340.00
                      ; AA2_$lvCc1687P15093                          ; 871.00
                      ; AA2_$lvCc1687P15090                          ; 330.00
                      ; AA2_$lvCc1687P15102                          ; 400.00
                      ; AA2_$lvCc1687P15103                          ; 871.00
                      ; AA2_$lvCc1687P15100                          ; 330.00
                      ; AA2_$lvCc1687P15107                          ; 400.00
                      ; AA2_$lvCc1987P12843                          ; 2363.00
                      ; AA2_$lvCc1987P12840                          ; 895.00
                      ; AA2_$lvCc1987P12846                          ; 2706.00
                      ; AA2_$lvCc1987P12847                          ; 1025.00
                      ; AA2_$lvCc1987P12988                          ; 475.00
                      ; AA2_$lvCc1987P12998                          ; 180.00
                      ; AA2_$lvCc1987P12783                          ; 660.00
                      ; AA2_$lvCc1987P12784                          ; 250.00
                      ; AA2_$lvCc1987P12973                          ; 1082.00
                      ; AA2_$lvCc1987P12970                          ; 410.00
                      ; AA2_$lvCc1987P12296                          ; 1267.00
                      ; AA2_$lvCc1987P12297                          ; 480.00
                      ; AA2_$lvCc1987P12993                          ; 502.00
                      ; AA2_$lvCc1987P12990                          ; 190.00
                      ; AA2_$lvCc1987P12294                          ; 686.00
                      ; AA2_$lvCc1987P12295                          ; 260.00
                      ; AA2_$lvCc1987P12823                          ; 1465.00
                      ; AA2_$lvCc1987P12820                          ; 555.00
                      ; AA2_$lvCc1987P12822                          ; 1808.40
                      ; AA2_$lvCc1987P12824                          ; 685.00
                      ; AA2_$lvCc1987P12913                          ; 3538.00
                      ; AA2_$lvCc1987P12910                          ; 1340.00
                      ; AA2_$lvCc1987P12916                          ; 3880.80
                      ; AA2_$lvCc1987P12917                          ; 1470.00
                      ; AA2_$lvCc1987P12263                          ; 1769.00
                      ; AA2_$lvCc1987P12260                          ; 670.00
                      ; AA2_$lvCc1987P12262                          ; 800.00
                      ; AA2_$lvCc1987P12265                          ; 2112.00
                      ; AA2_$lvCc1987P12280                          ; 3155.00
                      ; AA2_$lvCc1987P12278                          ; 1195.00
                      ; AA2_$lvCc1987P12275                          ; 1325.00
                      ; AA2_$lvCc1987P12276                          ; 3498.00
                      ; AA2_$lvCc1987P12402                          ; 3564.00
                      ; AA2_$lvCc1987P12400                          ; 1350.00
                      ; AA2_$lvCc1987P12936                          ; 1480.00
                      ; AA2_$lvCc1987P12937                          ; 3907.00
                      ; AA2_$lvCc1987P12503                          ; 924.00
                      ; AA2_$lvCc1987P12500                          ; 350.00
                      ; AA2_$lvCc1987729274                          ; 1.00
                      ; AA2_$lvCc1687P15015                          ; 330.00
                      ; AA2_$lvCc1987P12404                          ; 1200.00
                      ; AA2_$lvCc1987P12140                          ; 3168.00
                      ; AA2_$lvCc1987P12359                          ; 1330.00
                      ; AA2_$lvCc1987P12364                          ; 3511.00
                      ; AA2_$lvCc1687P15130                          ; 1.20
                      ; AA2_$lvCc1987P12389                          ; 600.00
                      ; AA2_$lvCc1987P12385                          ; 490.00
                      ; AA2_$lvCc1687P15137                          ; 100  ;
                      ; AA2_$lvCc1687P15139                          ; 200  ;
                      ; AA2_$lvCc1987P12254                          ; 475
                      ; AA2_$lvCc1987P12257                          ; 605
                      ; AA2_$lvCc1987P12256                          ; 1597
                      ; AA2_$lvCc1987P12515                          ; 0.01 ;
                      ; AA2_$lvCc1987P12517                          ; 0.01 ;
                      ; AA2_$lvCc1687P15163                          ; 0.01 ;
                      ; AA2_$lvCc1687P15170                          ; 0.01 ;
                      ; AA2_$lvCc1687P15160                          ; 0.01 ;
                      ; AA2_$lvCc1687P15173                          ; 0.01 ;


INSERT_UPDATE App; code[unique = true]     ; boms(code); $catalogVersion[unique = true]
                 ; AA2_$lvCc1987_ESIADV    ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe
                 ; AA2_$lvCc1987_ESIREPCAT ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE
                 ; AA2_$lvCc1987_ESIREPD   ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE,MAT_$aaCcK3
                 ; AA2_$lvCc1987_ESIREPE   ; MAT_$aaCcA,MAT_$aaCcE,MAT_$aaCcK2
                 ; AA2_$lvCc1987_ESIDIAG   ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB
                 ; AA2_$lvCc1987_ESIMASTER ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe,MAT_$aaCcM,MAT_$aaCcP
                 ; AA2_$lvCc1987_TRKOHW1   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$lvCc1987_TRKOHW2   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$lvCc1987_TRKTRUCK  ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$lvCc1987_TRKUPG    ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$lvCc1987_TRKOHW3   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK

INSERT_UPDATE App; code[unique = true]       ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$lvCc1687_CSFSA500    ; CM_$aaCcCSFSA5
                 ; AA2_$lvCc1687_CSFSA7XX    ; CM_$aaCcCSS,CM_$aaCcCSK
                 ; AA2_$lvCc1687_DCICRI      ; CM_$aaCcDCICRI
                 ; AA2_$lvCc1687_DCICRIN     ; CM_$aaCcDCICRIN
                 ; AA2_$lvCc1987_ESIADV      ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe
                 ; AA2_$lvCc1987_ESIREPCAT   ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE
                 ; AA2_$lvCc1987_ESIREPD     ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE,CM_$aaCcK3
                 ; AA2_$lvCc1987_ESIREPE     ; CM_$aaCcA,CM_$aaCcE,CM_$aaCcK2
                 ; AA2_$lvCc1987_ESIDIAG     ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB
                 ; AA2_$lvCc1987_ESIMASTER   ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe,CM_$aaCcM,CM_$aaCcP
                 ; AA2_$lvCc1987_TRKOHW1     ; CM_$aaCcETOHW1
                 ; AA2_$lvCc1987_TRKOHW2     ; CM_$aaCcETOHW2
                 ; AA2_$lvCc1987_TRKTRUCK    ; CM_$aaCcETruck
                 ; AA2_$lvCc1987_TSTINFOAW   ; CM_$aaCcEW
                 ; AA2_$lvCc1687_TSTINFODAT  ; CM_$aaCcTVPMCP
                 ; AA2_$lvCc1987_TRKUPG      ; CM_$aaCcTRKUPG
                 ; AA2_$lvCc1987_THLPKW      ; CM_$aaCcPKWTHL
                 ; AA2_$lvCc1687_CORE_ESIPKG ; CM_$aaCcCoReESIPKG
                 ; AA2_$lvCc1987_KTS250SD    ; CM_$aaCcKTS250ECUD
                 ; AA2_$lvCc1987_TRKOHW3     ; CM_$aaCcETOHW3

INSERT_UPDATE App; code[unique = true]  ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$lvCc1687_CRR950 ; CM_$aaCcCRR950

INSERT_UPDATE App; code[unique = true]       ; $supercategories; $catalogVersion[unique = true];
                 ; AA2_$lvCc1687_CSFSA500    ; cat_201
                 ; AA2_$lvCc1687_CSFSA7XX    ; cat_201
                 ; AA2_$lvCc1687_DCICRI      ; cat_401
                 ; AA2_$lvCc1687_DCICRIN     ; cat_401
                 ; AA2_$lvCc1987_ESIADV      ; cat_10101
                 ; AA2_$lvCc1987_ESIREPCAT   ; cat_1010101
                 ; AA2_$lvCc1987_ESIREPD     ; cat_1010101
                 ; AA2_$lvCc1987_ESIREPE     ; cat_1010101
                 ; AA2_$lvCc1987_ESIDIAG     ; cat_10101
                 ; AA2_$lvCc1987_ESIMASTER   ; cat_10101
                 ; AA2_$lvCc1987_TRKOHW1     ; cat_10102
                 ; AA2_$lvCc1987_TRKOHW2     ; cat_10102
                 ; AA2_$lvCc1987_TRKTRUCK    ; cat_10102
                 ; AA2_$lvCc1987_TSTINFOAW   ; cat_40101
                 ; AA2_$lvCc1687_TSTINFODAT  ; cat_40101
                 ; AA2_$lvCc1987_TRKUPG      ; cat_10102
                 ; AA2_$lvCc1987_THLPKW      ; cat_1010102
                 ; AA2_$lvCc1687_CORE_ESIPKG ; cat_1010103
                 ; AA2_$lvCc1987_KTS250SD    ; cat_1010104
                 ; AA2_$lvCc1687_CRR950      ; cat_401
                 ; AA2_$lvCc1987_TRKOHW3     ; cat_10102
                 ; AA2_$lvCc1987_ADAS_ONE    ; cat_501

INSERT_UPDATE App; code[unique = true]       ; eulaContainers(code); $catalogVersion[unique = true]
                 ; AA2_$lvCc1987_TSTINFOAW   ; AA2_ESItronic
                 ; AA2_$lvCc1687_DCICRI      ; AA2_DCICRI
                 ; AA2_$lvCc1687_DCICRIN     ; AA2_DCICRIN
                 ; AA2_$lvCc1687_TSTINFODAT  ; AA2_ESItronic
                 ; AA2_$lvCc1687_CSFSA500    ; AA2_FSA
                 ; AA2_$lvCc1687_CSFSA7XX    ; AA2_FSA
                 ; AA2_$lvCc1687_CORE_ESIPKG ; AA2_CoRe
                 ; AA2_$lvCc1987_KTS250SD    ; AA2_ESItronic
                 ; AA2_$lvCc1687_CRR950      ; AA2_ESItronic
