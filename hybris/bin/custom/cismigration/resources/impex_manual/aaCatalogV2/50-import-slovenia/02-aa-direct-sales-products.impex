#% impex.setLocale( Locale.ENGLISH );

# numerical code for SI, used as prefix for product code
$siCc = 386
$aaCc = 040
$aaPackageName = com.sast.aa.si.

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf

# please make sure, to use right DELEVELOPER COMPANY for croatia (Croatia and Slovenia have the same seller) in the right environment.
# demo
# $companyId = b56cfd9a-9fb4-4af2-88ec-cab3807e3556
# dev
#$companyId = 44a942c2-ad8a-40dc-bf32-574b9bfaf473
# local
$companyId = e0080903-e57f-450a-bfdd-7d06420acbdd

$packDiagnosticDescription = Vstop v strokovno diagnostiko, popravilo in vzdrževanje. Omogoča kompetentno električno diagnostiko in ponuja številne druge funkcije za vsa zajeta vozila.
$packDiagnosticDescription_en = The entry into professional diagnosis, repair and maintenance. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.
$packAdvancedDescription = Naslednja stopnja profesionalne opreme za delavnice. Poleg paketa Diagnostic so priložena tudi navodila in priročniki. Connected Repair zagotavlja zgodovino servisiranja in vzdrževanja ter informacije o popravilih.
$packAdvancedDescription_en = The next level of professional workshop equipment. In addition to the Diagnostic package, instructions and manuals are included. Connected Repair provides service and maintenance history and repair information.
$packMasterDescription = Popolnoma celovit paket za profesionalno diagnostiko vozil. Zagotavlja vse potrebne informacije za diagnostiko, popravilo, vzdrževanje, nadomestne dele, dokumentacijo in upravljanje podatkov. Tehnična podpora vam pomaga pri iskanju rešitev.
$packMasterDescription_en = The fully comprehensive package for professional vehicle diagnostics. It provides all the necessary information for diagnosis, repair, maintenance, spare parts, documentation and data management. Technical Support assists you in finding solutions.

$packComponentCatDescription = Katalog nadomestnih delov vključuje aplikacije, funkcije in avtomobilsko opremo ter nadomestne dele za dizelske motorje in nadomestne dele za električne motorje, vključno z arhivom in nadomestnim delom za električne motorje ESI[tronic]-F.
$packComponentCatDescription_en = The spare parts catalog package includes the applications, functions and automotive equipment as well as the diesel spare parts and electrical spare parts incl. archive and electrical spare part ESI[tronic]-F.
$packComponentRepairDieselDescription = Paket Repair Diesel vsebuje informacije o nadomestnih delih in popravilu dizelskih in električnih komponent. Omogoča identifikacijo vozila, Boscheve avtomobilske opreme in vključuje navodila za popravila in servisne informacije.
$packComponentRepairDieselDescription_en = The Repair Diesel package provides information on spare parts and repair of diesel and electrical components. It allows identification of the vehicle, Bosch automotive equipment and includes repair instructions & service information.
$packComponentRepairElectricDescription = Zaradi naraščajočega števila modelov vozil imajo delavnice težko na voljo najnovejše informacije o električnih sistemih vozil. Paket Repair Electrics zagotavlja podporo s podatki o nadomestnih delih za elektroniko vozil v pregledni obliki.
$packComponentRepairElectricDescription_en = The increasing number of vehicle models makes it difficult for workshops to have up-to-date information on vehicle electrical systems at their fingertips. The Repair Electrics Package provides support with spare parts data on car electrics in a clear format.

$packTruckDescription = Paket Truck podpira delavnice pri zanesljivem diagnosticiranju, popolnem vzdrževanju in učinkovitem popravilu vseh običajnih lahkih in težkih gospodarskih vozil, prikolic, kombijev in avtobusov.
$packTruckDescription_en = The Truck package supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.

$ohw1Description = Diagnostični paket Agricultural Machinery zagotavlja informacije o diagnostiki, vzdrževanju in popravilu kmetijskih vozil. Med drugim so vključene funkcije prilagajanja in parametrizacije hidravličnih sistemov.
$ohw1Description_en = The Agricultural Machinery Diagnostic Package provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw2Description = Diagnostični paket Agricultural Machinery zagotavlja informacije o diagnostiki, vzdrževanju in popravilu kmetijskih vozil. Med drugim so vključene funkcije prilagajanja in parametrizacije hidravličnih sistemov.
$ohw2Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw3Description = Diagnostični paket za ravnanje z materialom (OHW 3) nudi informacije o diagnostiki, vzdrževanju in popravilu viličarjev, teleskopskih ploščadi in nakladalnikov.
$ohw3Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.

$thlPkwDescription = Potrebujete tehnično podporo za vzdrževanje ali popravilo avtomobila ali le zanesljivo drugo mnenje? Potem se obrnite na našo ekipo za podporo in pridobite hitre in zanesljive rešitve.
$thlPkwDescription_en = Do you need technical support to maintain or repair a car, or simply a reliable second opinion? Then contact our support team and get fast and sound solutions.
$thlPkwTruckDescription = Ekipa za podporo lahko zagotovi tehnično podporo in dokumentacijo za različna popravila lahkih in težkih gospodarskih vozil z obsežno pokritostjo znamk, modelov in sistemov.
$thlPkwTruckDescription_en = With extensive coverage of makes, models and systems, the support team can provide technical assistance and documentation for various light and heavy commercial vehicle repairs.
$thlTruckDescription = Potrebujete tehnično podporo za vzdrževanje ali popravilo tovornjaka ali le zanesljivo drugo mnenje? Potem se obrnite na našo ekipo za podporo in pridobite hitre in zanesljive rešitve.
$thlTruckDescription_en = Do you need technical support to maintain or repair a truck, or just a reliable second opinion? Then contact our support team and get fast and sound solutions.

$compacFsa500Description = CompacSoft[plus] za FSA 500 je opremljen z vnaprej nastavljenimi testi komponent in ga je mogoče priključiti na sedanje sisteme ter z njim postopoma razširiti vaš sistem za testiranje v delavnici.
$compacFsa500Description_en = The CompacSoft[plus] for FSA 500 is equipped with preset component tests and can be connected to existing systems, as well as used to gradually expand your workshop test system.
$compacFsa7xxDescription = S CompacSoft[plus] za FSA 7xx se udobje pri vseh merilnih opravilih na vozilu še poveča z menijsko vodenimi testnimi koraki, poljubnimi nastavljenimi vrednostmi za določena vozila in prikazom dejanskih vrednosti.
$compacFsa7xxDescription_en = With CompacSoft[plus] for FSA 7xx, the comfort for all measuring tasks on the vehicle is further increased by the menu-guided test steps, the optional vehicle-specific set values, as well as the display of the actual values.

$criDescription = Programska oprema CRI za DCI 700 zagotavlja najnovejše podatke, zagotavlja nemotene postopke in vključuje testiranje piezo injektorjev za sisteme skupnim vodom.
$criDescription_en = CRI for DCI 700 software provides up-to-date data, ensures smooth processes and includes testing of piezo injectors for common rail systems.
$crinDescription = Programska oprema CRIN za DCI 700 zagotavlja najsodobnejše podatke, nemotene postopke in vključuje testiranje elektromagnetnih vbrizgalnih ventilov za sisteme s skupnim vodom.
$crinDescription_en = CRIN for DCI 700 software provides up-to-date data, ensures smooth processes and includes the testing of solenoid valve injectors for common rail systems.

$coReName = CoRe Server + Online
$coReName_en = CoRe Server + Online
$coReDescription = Bosch Connected Repair je programska oprema, ki povezuje opremo delavnice, vozila in podatke o popravilih. CoRe je prilagojen potrebam strank, tako pri okvarah kot pri shranjevanju podatkov in slik v skladu s Splošno uredbo o varstvu podatkov.
$coReDescription_en = Bosch Connected Repair is software that connects workshop equipment, vehicle and repair data. Whether in the event of malfunctions or the storage of data and images in accordance with the Basic Data Protection Regulation - CoRe has been adapted to the needs of customers.

$kts250SDDescription = Vstop v strokovno diagnozo, popravilo in vzdrževanje posebej za KTS 250. Omogoča kompetentno električno diagnostiko in ponuja široko paleto drugih funkcij za vsa zajeta vozila.
$kts250SDDescription_en = The entry into professional diagnosis, repair and maintenance specifically for KTS 250. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.

$thlPkwDescription_en = Do you need technical support to maintain or repair a car, or simply a reliable second opinion? Then contact our support team and get fast and sound solutions.

$infoartWDescription = ESI[tronic] 2.0 Infoart W vsebuje informacije o testnih vrednostih za dizelske motorje za kombinacije vrstnih črpalk in za črpalke VE, celoten testni postopek od določitve izmerjenih vrednosti do izpisa poročila in prikaza testnih korakov v optimalnem zaporedju.
$infoartWDescription_en = The ESI[tronic] 2.0 Infoart W contains information about diesel test values for in-line pump combinations as well as for VE pumps, the complete test process test procedure from the determination of the measured values to the printout of the report and the display of the test steps in the optimum sequence.

$infoartTestdataDescription = ESI[tronic] 2.0-Infotype Testdata (CD) vsebuje testne vrednosti za Boscheve visokotlačne črpalke s skupnim vodom, vbrizgalnike s skupnim vodom in distribucijske vbrizgalne črpalke VP 29/30/44.
$infoartTestdataDescription_en = The ESI[tronic] 2.0-Infotype Testdata (CD) contains test values for Bosch Common Rail high pressure pumps, Common Rail injectors and VP 29 / 30 / 44 distributor injection pumps.
$truckUpgradeDescription = Paket za nadgradnjo tovornjakov je namenjen uporabnikom sistema ESI[tronic] Car in podpira delavnice pri zanesljivem diagnosticiranju, popolnem vzdrževanju in učinkovitem popravilu vseh običajnih lahkih in težkih gospodarskih vozil, prikolic, kombijev in avtobusov.
$truckUpgradeDescription_en = The Truck Upgrade package is dedicated to ESI[tronic] Car users and supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.

$crr950DieselInjectorsRepairSoftDescription = CRR 950 Programska oprema za popravilo dizelskih injektorjev
$crr950DieselInjectorsRepairSoftDescription_en = The CRR 950 guides and support the professional repairs of magnetic solenoid common-rail injectors from Bosch

# TODO: UPDATE TRANSLATION
$adasDescription = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.
$adasDescription_en = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.

$compacFsa500Full3YBrimName = CompacSoft[plus] FSA 500 (3 years)
$compacFsa500SubsBrimName = CompacSoft[plus] FSA 500
$compacFsa7xxFull3YBrimName = CompacSoft[plus] FSA 7xx (3 years)
$compacFsa7xxSubsBrimName = CompacSoft[plus] FSA 7xx
$coreS1BrimName = CoRe for ESI 2.0 packages
$criSubsBrimName = Component DCI-CRI
$criFull3YBrimName = Component DCI-CRI (3 year)
$crinSubsBrimName = Component DCI-CRIN
$crinFull3YBrimName = Component DCI-CRIN (3 year)
$packAdvancedSubsS1BrimName = ESI 2.0 Advanced Unlimited
$packAdvancedSubsM3BrimName = ESI 2.0 Advanced Unlimited  Multi
$packAdvancedFull3YS1BrimName = ESI 2.0 Advanced 3 years
$packAdvancedFull3YM3BrimName = ESI 2.0 Advanced 3 years Multi
$packComponentCatSubsS1BrimName = ESI 2.0 ComponentCatalog D+E Unlimited
$packComponentCatSubsM3BrimName = ESI 2.0 ComponentCatalog D+E Unlim Multi
$packComponentCatFull3YS1BrimName = ESI 2.0 ComponentCatalog D+E 3 years
$packComponentCatFull3YM3BrimName = ESI 2.0 ComponentCatalog D+E 3y Multi
$packComponentRepairDieselSubsS1BrimName = ESI 2.0 ComponentRepair D+E Unlimited
$packComponentRepairDieselSubsM3BrimName = ESI 2.0 ComponentRepair D+E Unlim Multi
$packComponentRepairDieselFull3YS1BrimName = ESI 2.0 ComponentRepair D+E 3 years
$packComponentRepairDieselFull3YM3BrimName = ESI 2.0 ComponentRepair D+E 3y Multi
$packComponentRepairElectricSubsS1BrimName = ESI 2.0 ComponentRepair E Unlimited
$packComponentRepairElectricSubsM3BrimName = ESI 2.0 ComponentRepair E Unlim Multi
$packComponentRepairElectricFull3YS1BrimName = ESI 2.0 ComponentRepair E 3 years
$packComponentRepairElectricFull3YM3BrimName = ESI 2.0 ComponentRepair E 3 years Multi
$packDiagnosticSubsS1BrimName = ESI 2.0 Diagnostic Unlimited
$packDiagnosticSubsM3BrimName = ESI 2.0 Diagnostic Unlimited Multi
$packDiagnosticFull3YS1BrimName = ESI 2.0 Diagnostic 3 years
$packDiagnosticFull3YM3BrimName = ESI 2.0 Diagnostic 3 years Multi
$packDiagnosticFullOneTimeBrimName = ESI[tronic] 2.0 SD (OTP one time purchase)
$packMasterSubsS1BrimName = ESI 2.0 Master Unlimited
$packMasterSubsM3BrimName = ESI 2.0 Master Unlimited Multi
$packMasterFull3YS1BrimName = ESI 2.0 Master 3 years
$packMasterFull3YM3BrimName = ESI 2.0 Master 3 years Multi

$ohw1SubsS1BrimName = ESI[tronic] 2.0 Truck OHW I
$ohw1SubsM3BrimName = ESI[tronic] 2.0 Truck OHW I Multi
$ohw1Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW I (3 years)
$ohw1Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW I (3y) Multi

$thlPkwBrimName = THL use technical hotline

$ohw2SubsS1BrimName = ESI[tronic] 2.0 Truck OHW II
$ohw2SubsM3BrimName = ESI[tronic] 2.0 Truck OHW II Multi
$ohw2Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW II (3 years)
$ohw2Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW II (3y) Multi

$ohw3S1BrimName = ESI[tronic] Truck OHW III Unlimited
$ohw3Single3YearBrimName = ESI[tronic] Truck OHW III (3y)
$ohw3M3BrimName = ESI[tronic] Truck OHW III Unlimited Multi
$ohw3Full3YM3BrimName = ESI[tronic] Truck OHW III (3y) Multi

$packTruckSubsS1BrimName = ESI[tronic] 2.0 Truck
$packTruckSubsM3BrimName = ESI[tronic] 2.0 Truck Multi
$packTruckFull3YS1BrimName = ESI[tronic] 2.0 Truck (3 years)
$packTruckFull3YM3BrimName = ESI[tronic] 2.0 Truck (3 years) Multi
$packTruckFullBrimName = ESI[tronic] 2.0 Truck (Einmalkauf)
$infoartWSubsBrimName = ESI[tronic] W Diesel Test Data (WP)
$infoartWFull3YBrimName = ESI[tronic] W (3 Jahre)
$thlPkwSubsBrimName = THL use technical hotline
$thlPkwFull3YBrimName = Technische Hotline LKW (3 Jahre)
$thlTrkSubsBrimName = Technische Hotline ESI[tronic] for Truck
$thlPkwTrkSubsBrimName = Technische Hotline fuer LKWs und PKWs
$infoartTestdataSubsBrimName = Testdata VP-M/CP
$truckUpgradeSubsS1BrimName = ESI[tronic] 2.0 Truck Upgrade
$truckUpgradeSubsM3BrimName = ESI[tronic] 2.0 Truck Upgrade Multi
$truckUpgradeFull3YS1BrimName = ESI[tronic] 2.0 Truck Upgrade (3 years)
$truckUpgradeFull3YM3BrimName = ESI[tronic] 2.0 Truck Upgrade (3y) Multi
$kts250SDFullBrimName = KTS 250 SD (OTP one time purchase)
$kts250SDSubsBrimName = KTS 250 SD ECU Diagnosis
$crr950DieselInjectorsRepairSoftwareBrimName1L = CRR 950 Diesel Injectors (1 License)
$crr950DieselInjectorsRepairSoftwareBrimName3L = CRR 950 Diesel Injectors (3 Licenses)
$fsa7xxMulti = CompacSoft[plus] FSA 7xx Multi
$fsa7xx3YearsMulti = CompacSoft[plus] FSA 7xx (3 years) Multi
$fsa5xxMulti = CompacSoft[plus] FSA 500 Multi
$fsa5xx3YearsMulti = CompacSoft[plus] FSA 500 (3 years)Multi
$adasSubsS1BrimName = ADAS ONE - DATA
$adasFull3YS1BrimName = ADAS ONE - DATA (3 years)

$countryRestricted = true
# @formatter:off
$enabledIn = SI
# @formatter:on

INSERT_UPDATE App; code[unique = true]       ; packageName                    ; name[lang = sl]                          ; summary[lang = sl]                          ; description[lang = sl]                      ; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$siCc1687_CSFSA500    ; $aaPackageName1687_CSFSA500    ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description                    ; $compacFsa500Description                    ;
                 ; AA2_$siCc1687_CSFSA7XX    ; $aaPackageName1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription                    ; $compacFsa7xxDescription                    ;
                 ; AA2_$siCc1687_DCICRI      ; $aaPackageName1687_DCICRI      ; Komponenta DCI-CRI                       ; $criDescription                             ; $criDescription                             ;
                 ; AA2_$siCc1687_DCICRIN     ; $aaPackageName1687_DCICRIN     ; Komponenta DCI-CRIN                      ; $crinDescription                            ; $crinDescription                            ;
                 ; AA2_$siCc1987_ESIADV      ; $aaPackageName1987_ESIADV      ; ESI[tronic] 2.0 Advanced                 ; $packAdvancedDescription                    ; $packAdvancedDescription                    ;
                 ; AA2_$siCc1987_ESIREPCAT   ; $aaPackageName1987_ESIREPCAT   ; ESI[tronic] 2.0 ComponentCat D+E         ; $packComponentCatDescription                ; $packComponentCatDescription                ;
                 ; AA2_$siCc1987_ESIREPD     ; $aaPackageName1987_ESIREPD     ; ESI[tronic] 2.0 ComponentRepair D+E      ; $packComponentRepairDieselDescription       ; $packComponentRepairDieselDescription       ;
                 ; AA2_$siCc1987_ESIREPE     ; $aaPackageName1987_ESIREPE     ; ESI[tronic] 2.0 ComponentRepair E        ; $packComponentRepairElectricDescription     ; $packComponentRepairElectricDescription     ;
                 ; AA2_$siCc1987_ESIDIAG     ; $aaPackageName1987_ESIDIAG     ; Diagnostika ESI[tronic] 2.0              ; $packDiagnosticDescription                  ; $packDiagnosticDescription                  ;
                 ; AA2_$siCc1987_ESIMASTER   ; $aaPackageName1987_ESIMASTER   ; ESI[tronic] 2.0 Master                   ; $packMasterDescription                      ; $packMasterDescription                      ;
                 ; AA2_$siCc1987_TRKOHW1     ; $aaPackageName1987_TRKOHW1     ; ESI[tronic] 2.0 Truck OHW I              ; $ohw1Description                            ; $ohw1Description                            ;
                 ; AA2_$siCc1987_TRKOHW2     ; $aaPackageName1987_TRKOHW2     ; ESI[tronic] 2.0 Truck OHW II             ; $ohw2Description                            ; $ohw2Description                            ;
                 ; AA2_$siCc1987_TRKOHW3     ; $aaPackageName1987_TRKOHW3     ; ESI[tronic] Truck OHW III                ; $ohw3Description                            ; $ohw3Description                            ;
                 ; AA2_$siCc1987_TRKTRUCK    ; $aaPackageName1987_TRKTRUCK    ; ESI[tronic] 2.0 Truck                    ; $packTruckDescription                       ; $packTruckDescription                       ;
                 ; AA2_$siCc1987_TSTINFOAW   ; $aaPackageName1987_TSTINFOAW   ; ESI[tronic] W                            ; $infoartWDescription                        ; $infoartWDescription                        ;
                 ; AA2_$siCc1687_TSTINFODAT  ; $aaPackageName1687_TSTINFODAT  ; Testdata VP-M/CP                         ; $infoartTestdataDescription                 ; $infoartTestdataDescription                 ;
                 ; AA2_$siCc1987_TRKUPG      ; $aaPackageName1987_TRUCKUPG    ; ESI[tronic] 2.0 Truck Upgrade            ; $truckUpgradeDescription                    ; $truckUpgradeDescription                    ;
                 ; AA2_$siCc1687_CORE_ESIPKG ; $aaPackageName1687_CORE_ESIPKG ; $coReName                                ; $coReDescription                            ; $coReDescription                            ;
                 ; AA2_$siCc1987_KTS250SD    ; $aaPackageName1987_KTS250      ; KTS 250 SD ECU Diagnosis                 ; $kts250SDDescription                        ; $kts250SDDescription                        ;
                 ; AA2_$siCc1687_CRR950      ; $aaPackageName1687_CRR950      ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription ; $crr950DieselInjectorsRepairSoftDescription ;
                 ; AA2_$siCc1987_THLPKW      ; $aaPackageName1987_THLPKW      ; Technical Hotline Car                    ; $thlPkwDescription_en                       ; $thlPkwDescription_en                       ;
                 ; AA2_$siCc1987_ADAS_ONE    ; $aaPackageName1987_ADAS_ONE    ; ADAS One Solution                        ; $adasDescription                            ; $adasDescription                            ;

INSERT_UPDATE App; code[unique = true]       ; name[lang = en]                          ; summary[lang = en]                             ; description[lang = en]; $catalogVersion[unique = true];
                 ; AA2_$siCc1687_CSFSA500    ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description_en                    ; $compacFsa500Description_en
                 ; AA2_$siCc1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription_en                    ; $compacFsa7xxDescription_en
                 ; AA2_$siCc1687_DCICRI      ; Component DCI-CRI                        ; $criDescription_en                             ; $criDescription_en
                 ; AA2_$siCc1687_DCICRIN     ; Component DCI-CRIN                       ; $crinDescription_en                            ; $crinDescription_en
                 ; AA2_$siCc1987_ESIADV      ; ESI[tronic] 2.0 Advanced                 ; $packAdvancedDescription_en                    ; $packAdvancedDescription_en
                 ; AA2_$siCc1987_ESIREPCAT   ; ESI[tronic] 2.0 ComponentCat D+E         ; $packComponentCatDescription_en                ; $packComponentCatDescription_en
                 ; AA2_$siCc1987_ESIREPD     ; ESI[tronic] 2.0 ComponentRepair D+E      ; $packComponentRepairDieselDescription_en       ; $packComponentRepairDieselDescription_en
                 ; AA2_$siCc1987_ESIREPE     ; ESI[tronic] 2.0 ComponentRepair E        ; $packComponentRepairElectricDescription_en     ; $packComponentRepairElectricDescription_en
                 ; AA2_$siCc1987_ESIDIAG     ; ESI[tronic] 2.0 Diagnostic               ; $packDiagnosticDescription_en                  ; $packDiagnosticDescription_en
                 ; AA2_$siCc1987_ESIMASTER   ; ESI[tronic] 2.0 Master                   ; $packMasterDescription_en                      ; $packMasterDescription_en
                 ; AA2_$siCc1987_TRKOHW1     ; ESI[tronic] 2.0 Truck OHW I              ; $ohw1Description_en                            ; $ohw1Description_en
                 ; AA2_$siCc1987_TRKOHW2     ; ESI[tronic] 2.0 Truck OHW II             ; $ohw2Description_en                            ; $ohw2Description_en
                 ; AA2_$siCc1987_TRKOHW3     ; ESI[tronic] Truck OHW III                ; $ohw3Description_en                            ; $ohw3Description_en
                 ; AA2_$siCc1987_TRKTRUCK    ; ESI[tronic] 2.0 Truck                    ; $packTruckDescription_en                       ; $packTruckDescription_en
                 ; AA2_$siCc1987_TSTINFOAW   ; ESI[tronic] W                            ; $infoartWDescription_en                        ; $infoartWDescription_en
                 ; AA2_$siCc1687_TSTINFODAT  ; Testdata VP-M/CP                         ; $infoartTestdataDescription_en                 ; $infoartTestdataDescription_en
                 ; AA2_$siCc1987_TRKUPG      ; ESI[tronic] 2.0 Truck Upgrade            ; $truckUpgradeDescription_en                    ; $truckUpgradeDescription_en
                 ; AA2_$siCc1687_CORE_ESIPKG ; $coReName_en                             ; $coReDescription_en                            ; $coReDescription_en
                 ; AA2_$siCc1987_KTS250SD    ; KTS 250 SD ECU Diagnosis                 ; $kts250SDDescription_en                        ; $kts250SDDescription_en
                 ; AA2_$siCc1687_CRR950      ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription_en ; $crr950DieselInjectorsRepairSoftDescription_en
                 ; AA2_$siCc1987_THLPKW      ; Technical Hotline Car                    ; $thlPkwDescription_en                          ; $thlPkwDescription_en
                 ; AA2_$siCc1987_ADAS_ONE    ; ADAS One Solution                        ; $adasDescription_en                            ; $adasDescription_en

INSERT_UPDATE ProductContainer; code[unique = true]        ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$siCc1687_CSFSA500    ; AA2_$siCc1687_CSFSA500
                              ; pcaa_$siCc1687_CSFSA7XX    ; AA2_$siCc1687_CSFSA7XX
                              ; pcaa_$siCc1687_DCICRI      ; AA2_$siCc1687_DCICRI
                              ; pcaa_$siCc1687_DCICRIN     ; AA2_$siCc1687_DCICRIN
                              ; pcaa_$siCc1987_ESIADV      ; AA2_$siCc1987_ESIADV
                              ; pcaa_$siCc1987_ESIREPCAT   ; AA2_$siCc1987_ESIREPCAT
                              ; pcaa_$siCc1987_ESIREPD     ; AA2_$siCc1987_ESIREPD
                              ; pcaa_$siCc1987_ESIREPE     ; AA2_$siCc1987_ESIREPE
                              ; pcaa_$siCc1987_ESIDIAG     ; AA2_$siCc1987_ESIDIAG
                              ; pcaa_$siCc1987_ESIMASTER   ; AA2_$siCc1987_ESIMASTER
                              ; pcaa_$siCc1987_TRKOHW1     ; AA2_$siCc1987_TRKOHW1
                              ; pcaa_$siCc1987_TRKOHW2     ; AA2_$siCc1987_TRKOHW2
                              ; pcaa_$siCc1987_TRKOHW3     ; AA2_$siCc1987_TRKOHW3
                              ; pcaa_$siCc1987_TRKTRUCK    ; AA2_$siCc1987_TRKTRUCK
                              ; pcaa_$siCc1987_TSTINFOAW   ; AA2_$siCc1987_TSTINFOAW
                              ; pcaa_$siCc1687_TSTINFODAT  ; AA2_$siCc1687_TSTINFODAT
                              ; pcaa_$siCc1987_TRKUPG      ; AA2_$siCc1987_TRKUPG
                              ; pcaa_$siCc1687_CORE_ESIPKG ; AA2_$siCc1687_CORE_ESIPKG
                              ; pcaa_$siCc1987_KTS250SD    ; AA2_$siCc1987_KTS250SD
                              ; pcaa_$siCc1687_CRR950      ; AA2_$siCc1687_CRR950
                              ; pcaa_$siCc1987_THLPKW      ; AA2_$siCc1987_THLPKW
                              ; pcaa_$siCc1987_ADAS_ONE    ; AA2_$siCc1987_ADAS_ONE

INSERT_UPDATE AppLicense; code[unique = true] ; $baseProduct              ; sellerProductId; brimName[lang = en]                            ; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); specifiedPrice; enabledCountries(isocode)[default = $enabledIn]; billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; availabilityStatus(code)[default = PUBLISHED]
                        ; AA2_$siCc1687P15063 ; AA2_$siCc1687_CSFSA500    ; 1687P15063     ; $compacFsa500Full3YBrimName                    ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 3             ; $enabledIn
                        ; AA2_$siCc1687P15060 ; AA2_$siCc1687_CSFSA500    ; 1687P15060     ; $compacFsa500SubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 2.6           ; $enabledIn
                        ; AA2_$siCc1687P15048 ; AA2_$siCc1687_CSFSA7XX    ; 1687P15048     ; $compacFsa7xxFull3YBrimName                    ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 777.7         ; $enabledIn
                        ; AA2_$siCc1687P15045 ; AA2_$siCc1687_CSFSA7XX    ; 1687P15045     ; $compacFsa7xxSubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 305.1         ; $enabledIn
                        ; AA2_$siCc1687P15093 ; AA2_$siCc1687_DCICRI      ; 1687P15093     ; $criFull3YBrimName                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 841.6         ; $enabledIn
                        ; AA2_$siCc1687P15090 ; AA2_$siCc1687_DCICRI      ; 1687P15090     ; $criSubsBrimName                               ;                                          ; runtime_subs_unlimited ; <ignore>        ; 331           ; $enabledIn
                        ; AA2_$siCc1687P15103 ; AA2_$siCc1687_DCICRIN     ; 1687P15103     ; $crinFull3YBrimName                            ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 841.6         ; $enabledIn
                        ; AA2_$siCc1687P15100 ; AA2_$siCc1687_DCICRIN     ; 1687P15100     ; $crinSubsBrimName                              ;                                          ; runtime_subs_unlimited ; <ignore>        ; 331           ; $enabledIn
                        ; AA2_$siCc1987P12843 ; AA2_$siCc1987_ESIADV      ; 1987P12843     ; $packAdvancedFull3YS1BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 2920.8        ; $enabledIn
                        ; AA2_$siCc1987P12840 ; AA2_$siCc1987_ESIADV      ; 1987P12840     ; $packAdvancedSubsS1BrimName                    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1136.3        ; $enabledIn
                        ; AA2_$siCc1987P12846 ; AA2_$siCc1987_ESIADV      ; 1987P12846     ; $packAdvancedFull3YM3BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 3220.8        ; $enabledIn
                        ; AA2_$siCc1987P12847 ; AA2_$siCc1987_ESIADV      ; 1987P12847     ; $packAdvancedSubsM3BrimName                    ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1236.3        ; $enabledIn
                        ; AA2_$siCc1987P12988 ; AA2_$siCc1987_ESIREPCAT   ; 1987P12988     ; $packComponentCatFull3YS1BrimName              ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 673.2         ; $enabledIn
                        ; AA2_$siCc1987P12998 ; AA2_$siCc1987_ESIREPCAT   ; 1987P12998     ; $packComponentCatSubsS1BrimName                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 264           ; $enabledIn
                        ; AA2_$siCc1987P12783 ; AA2_$siCc1987_ESIREPCAT   ; 1987P12783     ; $packComponentCatFull3YM3BrimName              ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 973.2         ; $enabledIn
                        ; AA2_$siCc1987P12784 ; AA2_$siCc1987_ESIREPCAT   ; 1987P12784     ; $packComponentCatSubsM3BrimName                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 364           ; $enabledIn
                        ; AA2_$siCc1987P12973 ; AA2_$siCc1987_ESIREPD     ; 1987P12973     ; $packComponentRepairDieselFull3YS1BrimName     ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1427.9        ; $enabledIn
                        ; AA2_$siCc1987P12970 ; AA2_$siCc1987_ESIREPD     ; 1987P12970     ; $packComponentRepairDieselSubsS1BrimName       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 560           ; $enabledIn
                        ; AA2_$siCc1987P12296 ; AA2_$siCc1987_ESIREPD     ; 1987P12296     ; $packComponentRepairDieselFull3YM3BrimName     ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1728          ; $enabledIn
                        ; AA2_$siCc1987P12297 ; AA2_$siCc1987_ESIREPD     ; 1987P12297     ; $packComponentRepairDieselSubsM3BrimName       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 660           ; $enabledIn
                        ; AA2_$siCc1987P12993 ; AA2_$siCc1987_ESIREPE     ; 1987P12993     ; $packComponentRepairElectricFull3YS1BrimName   ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 611.9         ; $enabledIn
                        ; AA2_$siCc1987P12990 ; AA2_$siCc1987_ESIREPE     ; 1987P12990     ; $packComponentRepairElectricSubsS1BrimName     ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 240           ; $enabledIn
                        ; AA2_$siCc1987P12294 ; AA2_$siCc1987_ESIREPE     ; 1987P12294     ; $packComponentRepairElectricFull3YM3BrimName   ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 912           ; $enabledIn
                        ; AA2_$siCc1987P12295 ; AA2_$siCc1987_ESIREPE     ; 1987P12295     ; $packComponentRepairElectricSubsM3BrimName     ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 340           ; $enabledIn
                        ; AA2_$siCc1987P12823 ; AA2_$siCc1987_ESIDIAG     ; 1987P12823     ; $packDiagnosticFull3YS1BrimName                ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1961.6        ; $enabledIn
                        ; AA2_$siCc1987P12820 ; AA2_$siCc1987_ESIDIAG     ; 1987P12820     ; $packDiagnosticSubsS1BrimName                  ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 759.5         ; $enabledIn
                        ; AA2_$siCc1987P12822 ; AA2_$siCc1987_ESIDIAG     ; 1987P12822     ; $packDiagnosticFull3YM3BrimName                ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 2261.6        ; $enabledIn
                        ; AA2_$siCc1987P12824 ; AA2_$siCc1987_ESIDIAG     ; 1987P12824     ; $packDiagnosticSubsM3BrimName                  ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 859.5         ; $enabledIn
                        ; AA2_$siCc1987P12051 ; AA2_$siCc1987_ESIDIAG     ; 1987P12051     ; $packDiagnosticFullOneTimeBrimName             ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 632.3         ; $enabledIn
                        ; AA2_$siCc1987P12913 ; AA2_$siCc1987_ESIMASTER   ; 1987P12913     ; $packMasterFull3YS1BrimName                    ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 4040.6        ; $enabledIn
                        ; AA2_$siCc1987P12910 ; AA2_$siCc1987_ESIMASTER   ; 1987P12910     ; $packMasterSubsS1BrimName                      ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1575.6        ; $enabledIn
                        ; AA2_$siCc1987P12916 ; AA2_$siCc1987_ESIMASTER   ; 1987P12916     ; $packMasterFull3YS1BrimName                    ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 4341          ; $enabledIn
                        ; AA2_$siCc1987P12917 ; AA2_$siCc1987_ESIMASTER   ; 1987P12917     ; $packMasterSubsM3BrimName                      ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1675.6        ; $enabledIn
                        ; AA2_$siCc1987P12263 ; AA2_$siCc1987_TRKOHW1     ; 1987P12263     ; $ohw1Full3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1624.1        ; $enabledIn
                        ; AA2_$siCc1987P12260 ; AA2_$siCc1987_TRKOHW1     ; 1987P12260     ; $ohw1SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 620.5         ; $enabledIn
                        ; AA2_$siCc1987P12262 ; AA2_$siCc1987_TRKOHW1     ; 1987P12262     ; $ohw1SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 720.5         ; $enabledIn
                        ; AA2_$siCc1987P12265 ; AA2_$siCc1987_TRKOHW1     ; 1987P12265     ; $ohw1Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1924.1        ; $enabledIn
                        ; AA2_$siCc1987P12280 ; AA2_$siCc1987_TRKOHW2     ; 1987P12280     ; $ohw2Full3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3180.1        ; $enabledIn
                        ; AA2_$siCc1987P12278 ; AA2_$siCc1987_TRKOHW2     ; 1987P12278     ; $ohw2SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1108          ; $enabledIn
                        ; AA2_$siCc1987P12275 ; AA2_$siCc1987_TRKOHW2     ; 1987P12275     ; $ohw2SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1208          ; $enabledIn
                        ; AA2_$siCc1987P12276 ; AA2_$siCc1987_TRKOHW2     ; 1987P12276     ; $ohw2Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 3480          ; $enabledIn
                        ; AA2_$siCc1987P12254 ; AA2_$siCc1987_TRKOHW3     ; 1987P12254     ; $ohw3S1BrimName                                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 450           ; SI
                        ; AA2_$siCc1987P12255 ; AA2_$siCc1987_TRKOHW3     ; 1987P12255     ; $ohw3Single3YearBrimName                       ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1107          ; SI
                        ; AA2_$siCc1987P12402 ; AA2_$siCc1987_TRKTRUCK    ; 1987P12402     ; $packTruckFull3YS1BrimName                     ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3330.6        ; $enabledIn
                        ; AA2_$siCc1987P12400 ; AA2_$siCc1987_TRKTRUCK    ; 1987P12400     ; $packTruckSubsS1BrimName                       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1306.2        ; $enabledIn
                        ; AA2_$siCc1987P12936 ; AA2_$siCc1987_TRKTRUCK    ; 1987P12936     ; $packTruckSubsM3BrimName                       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1406.2        ; $enabledIn
                        ; AA2_$siCc1987P12937 ; AA2_$siCc1987_TRKTRUCK    ; 1987P12937     ; $packTruckFull3YM3BrimName                     ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 3630.6        ; $enabledIn
                        ; AA2_$siCc1987P12412 ; AA2_$siCc1987_TRKTRUCK    ; 1987P12412     ; $packTruckFullBrimName                         ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 2556          ; $enabledIn
                        ; AA2_$siCc1987P12503 ; AA2_$siCc1987_TSTINFOAW   ; 1987P12503     ; $infoartWFull3YBrimName                        ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 1763.5        ; $enabledIn
                        ; AA2_$siCc1987P12500 ; AA2_$siCc1987_TSTINFOAW   ; 1987P12500     ; $infoartWSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; 691.6         ; $enabledIn
                        ; AA2_$siCc1687P15015 ; AA2_$siCc1687_TSTINFODAT  ; 1687P15015     ; $infoartTestdataSubsBrimName                   ;                                          ; runtime_subs_unlimited ; <ignore>        ; 344.6         ; $enabledIn
                        ; AA2_$siCc1987P12404 ; AA2_$siCc1987_TRKUPG      ; 1987P12404     ; $truckUpgradeSubsS1BrimName                    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1194.9        ; $enabledIn
                        ; AA2_$siCc1987P12140 ; AA2_$siCc1987_TRKUPG      ; 1987P12140     ; $truckUpgradeFull3YS1BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3047.2        ; $enabledIn
                        ; AA2_$siCc1987P12359 ; AA2_$siCc1987_TRKUPG      ; 1987P12359     ; $truckUpgradeSubsM3BrimName                    ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1294.9        ; $enabledIn
                        ; AA2_$siCc1987P12364 ; AA2_$siCc1987_TRKUPG      ; 1987P12364     ; $truckUpgradeFull3YM3BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 3347.2        ; $enabledIn
                        ; AA2_$siCc1687P15130 ; AA2_$siCc1687_CORE_ESIPKG ; 1687P15130     ; $coreS1BrimName                                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1.3           ; $enabledIn
                        ; AA2_$siCc1987P12389 ; AA2_$siCc1987_KTS250SD    ; 1987P12389     ; $kts250SDFullBrimName                          ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 700           ; $enabledIn
                        ; AA2_$siCc1987P12385 ; AA2_$siCc1987_KTS250SD    ; 1987P12385     ; $kts250SDSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; 671.1         ; $enabledIn
                        ; AA2_$siCc1687P15137 ; AA2_$siCc1687_CRR950      ; 1687P15137     ; $crr950DieselInjectorsRepairSoftwareBrimName1L ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 100           ; $enabledIn
                        ; AA2_$siCc1987729274 ; AA2_$siCc1987_THLPKW      ; 1987729274     ; $thlPkwBrimName                                ;                                          ; runtime_subs_unlimited ; <ignore>        ; 2             ; $enabledIn
                        ; AA2_$siCc1987P12257 ; AA2_$siCc1987_TRKOHW3     ; 1987P12257     ; $ohw3M3BrimName                                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 572           ; $enabledIn
                        ; AA2_$siCc1987P12256 ; AA2_$siCc1987_TRKOHW3     ; 1987P12256     ; $ohw3Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1229          ; $enabledIn
                        ; AA2_$siCc1987P12515 ; AA2_$siCc1987_ADAS_ONE    ; 1987P12515     ; $adasSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 0.01          ; $enabledIn
                        ; AA2_$siCc1987P12517 ; AA2_$siCc1987_ADAS_ONE    ; 1987P12517     ; $adasFull3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 0.01          ; $enabledIn
                        ; AA2_$siCc1687P15170 ; AA2_$siCc1687_CSFSA500    ; 1687P15170     ; $fsa5xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$siCc1687P15173 ; AA2_$siCc1687_CSFSA500    ; 1687P15173     ; $fsa5xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;
                        ; AA2_$siCc1687P15160 ; AA2_$siCc1687_CSFSA7XX    ; 1687P15160     ; $fsa7xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$siCc1687P15163 ; AA2_$siCc1687_CSFSA7XX    ; 1687P15163     ; $fsa7xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;

# The licenses below were discontinued by MA and should be available only for the contract migration.
UPDATE AppLicense; code[unique = true] ; $catalogVersion[unique = true]; availabilityStatus(code)[default = UNPUBLISHED]
                 ; AA2_$siCc1987P12051 ;
                 ; AA2_$siCc1987P12389 ;
                 ; AA2_$siCc1987P12412 ;

UPDATE AppLicense; code[unique = true] ; userGroups(uid)                                         ; $catalogVersion[unique = true];
                 ; AA2_$siCc1687P15063 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1687P15060 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1687P15048 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1687P15045 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1687P15093 ;                                                         ;
                 ; AA2_$siCc1687P15090 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1687P15103 ;                                                         ;
                 ; AA2_$siCc1687P15100 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12843 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12840 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12846 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12847 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12988 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12998 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12783 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12784 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12973 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12970 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12296 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12297 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12993 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12990 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12294 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12295 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12823 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12820 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12822 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12824 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12051 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12913 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12910 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12916 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12917 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12263 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12260 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12262 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12265 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12280 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12278 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12275 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12276 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12402 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12400 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12936 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12937 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12412 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12503 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12500 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1687P15015 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12404 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12140 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12359 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12364 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1687P15130 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12389 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1987P12385 ; IDW000,BCS000,BDS000,BDC000,BM0000,WD0001,WD0002,AUT000 ;
                 ; AA2_$siCc1687P15137 ; IDW000                                                  ;
                 ; AA2_$siCc1987P12254 ; IDW000, WD0001, WD0002                                  ;
                 ; AA2_$siCc1987P12255 ; IDW000, WD0001, WD0002                                  ;
                 ; AA2_$siCc1987729274 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000 ;
                 ; AA2_$siCc1987P12257 ; IDW000,WD0001                                           ;
                 ; AA2_$siCc1987P12256 ; IDW000,WD0001                                           ;
# The group assignment for the ADAS ONE is most probably not final. we're missing the prices for now
                 ; AA2_$siCc1987P12515 ; IDW000                                                  ;
                 ; AA2_$siCc1987P12517 ; IDW000                                                  ;
                 ; AA2_$siCc1687P15163 ; IDW000                                                  ;
                 ; AA2_$siCc1687P15170 ; IDW000                                                  ;
                 ; AA2_$siCc1687P15160 ; IDW000                                                  ;
                 ; AA2_$siCc1687P15173 ; IDW000                                                  ;



INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$siCc1687P15063                          ; 3
                      ; AA2_$siCc1687P15060                          ; 2.6
                      ; AA2_$siCc1687P15048                          ; 777.7
                      ; AA2_$siCc1687P15045                          ; 305.1
                      ; AA2_$siCc1687P15090                          ; 331
                      ; AA2_$siCc1687P15100                          ; 331
                      ; AA2_$siCc1987P12843                          ; 2920.8
                      ; AA2_$siCc1987P12840                          ; 1136.3
                      ; AA2_$siCc1987P12846                          ; 3220.8
                      ; AA2_$siCc1987P12847                          ; 1236.3
                      ; AA2_$siCc1987P12988                          ; 673.2
                      ; AA2_$siCc1987P12998                          ; 264
                      ; AA2_$siCc1987P12783                          ; 973.2
                      ; AA2_$siCc1987P12784                          ; 364
                      ; AA2_$siCc1987P12973                          ; 1427.9
                      ; AA2_$siCc1987P12970                          ; 560
                      ; AA2_$siCc1987P12296                          ; 1728
                      ; AA2_$siCc1987P12297                          ; 660
                      ; AA2_$siCc1987P12993                          ; 611.9
                      ; AA2_$siCc1987P12990                          ; 240
                      ; AA2_$siCc1987P12294                          ; 912
                      ; AA2_$siCc1987P12295                          ; 340
                      ; AA2_$siCc1987P12823                          ; 1961.6
                      ; AA2_$siCc1987P12820                          ; 759.5
                      ; AA2_$siCc1987P12822                          ; 2261.6
                      ; AA2_$siCc1987P12824                          ; 859.5
                      ; AA2_$siCc1987P12051                          ; 632.3
                      ; AA2_$siCc1987P12913                          ; 4040.6
                      ; AA2_$siCc1987P12910                          ; 1575.6
                      ; AA2_$siCc1987P12916                          ; 4341
                      ; AA2_$siCc1987P12917                          ; 1675.6
                      ; AA2_$siCc1987P12263                          ; 1624.1
                      ; AA2_$siCc1987P12260                          ; 620.5
                      ; AA2_$siCc1987P12262                          ; 720.5
                      ; AA2_$siCc1987P12265                          ; 1924.1
                      ; AA2_$siCc1987P12280                          ; 3180.1
                      ; AA2_$siCc1987P12278                          ; 1108
                      ; AA2_$siCc1987P12275                          ; 1208
                      ; AA2_$siCc1987P12276                          ; 3480
                      ; AA2_$siCc1987P12402                          ; 3330.6
                      ; AA2_$siCc1987P12400                          ; 1306.2
                      ; AA2_$siCc1987P12936                          ; 1406.2
                      ; AA2_$siCc1987P12937                          ; 3630.6
                      ; AA2_$siCc1987P12412                          ; 2556
                      ; AA2_$siCc1987P12503                          ; 1763.5
                      ; AA2_$siCc1987P12500                          ; 691.6
                      ; AA2_$siCc1687P15015                          ; 344.6
                      ; AA2_$siCc1987P12404                          ; 1194.9
                      ; AA2_$siCc1987P12140                          ; 3047.2
                      ; AA2_$siCc1987P12359                          ; 1294.9
                      ; AA2_$siCc1987P12364                          ; 3347.2
                      ; AA2_$siCc1687P15130                          ; 1.3
                      ; AA2_$siCc1987P12389                          ; 700
                      ; AA2_$siCc1987P12385                          ; 671.1
                      ; AA2_$siCc1687P15137                          ; 100  ;
                      ; AA2_$siCc1987P12254                          ; 450  ;
                      ; AA2_$siCc1987P12255                          ; 1107 ;
                      ; AA2_$siCc1987729274                          ; 2
                      ; AA2_$siCc1987P12257                          ; 572
                      ; AA2_$siCc1987P12256                          ; 1229
                      ; AA2_$siCc1987P12515                          ; 0.01 ;
                      ; AA2_$siCc1987P12517                          ; 0.01 ;
                      ; AA2_$siCc1687P15163                          ; 0.01 ;
                      ; AA2_$siCc1687P15170                          ; 0.01 ;
                      ; AA2_$siCc1687P15160                          ; 0.01 ;
                      ; AA2_$siCc1687P15173                          ; 0.01 ;


INSERT_UPDATE App; code[unique = true]     ; boms(code); $catalogVersion[unique = true]
                 ; AA2_$siCc1987_ESIADV    ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe
                 ; AA2_$siCc1987_ESIREPCAT ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE
                 ; AA2_$siCc1987_ESIREPD   ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE,MAT_$aaCcK3
                 ; AA2_$siCc1987_ESIREPE   ; MAT_$aaCcA,MAT_$aaCcE,MAT_$aaCcK2
                 ; AA2_$siCc1987_ESIDIAG   ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB
                 ; AA2_$siCc1987_ESIMASTER ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe,MAT_$aaCcM,MAT_$aaCcP
                 ; AA2_$siCc1987_TRKOHW1   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$siCc1987_TRKOHW2   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$siCc1987_TRKTRUCK  ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$siCc1987_TRKUPG    ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK

INSERT_UPDATE App; code[unique = true]       ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$siCc1687_CSFSA500    ; CM_$aaCcCSFSA5
                 ; AA2_$siCc1687_CSFSA7XX    ; CM_$aaCcCSS,CM_$aaCcCSK
                 ; AA2_$siCc1687_DCICRI      ; CM_$aaCcDCICRI
                 ; AA2_$siCc1687_DCICRIN     ; CM_$aaCcDCICRIN
                 ; AA2_$siCc1987_ESIADV      ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe
                 ; AA2_$siCc1987_ESIREPCAT   ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE
                 ; AA2_$siCc1987_ESIREPD     ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE,CM_$aaCcK3
                 ; AA2_$siCc1987_ESIREPE     ; CM_$aaCcA,CM_$aaCcE,CM_$aaCcK2
                 ; AA2_$siCc1987_ESIDIAG     ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB
                 ; AA2_$siCc1987_ESIMASTER   ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe,CM_$aaCcM,CM_$aaCcP
                 ; AA2_$siCc1987_TRKOHW1     ; CM_$aaCcETOHW1
                 ; AA2_$siCc1987_TRKOHW2     ; CM_$aaCcETOHW2
                 ; AA2_$siCc1987_TRKOHW3     ; CM_$aaCcETOHW3
                 ; AA2_$siCc1987_TRKTRUCK    ; CM_$aaCcETruck
                 ; AA2_$siCc1987_TSTINFOAW   ; CM_$aaCcEW
                 ; AA2_$siCc1687_TSTINFODAT  ; CM_$aaCcTVPMCP
                 ; AA2_$siCc1987_TRKUPG      ; CM_$aaCcTRKUPG
                 ; AA2_$siCc1687_CORE_ESIPKG ; CM_$aaCcCoReESIPKG
                 ; AA2_$siCc1987_KTS250SD    ; CM_$aaCcKTS250ECUD
                 ; AA2_$siCc1987_THLPKW      ; CM_$aaCcPKWTHL

INSERT_UPDATE App; code[unique = true]  ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$siCc1687_CRR950 ; CM_$aaCcCRR950

UPDATE AppLicense; code[unique = true] ; $catalogVersion[unique = true]; contentModules(code)[default = CM_$aaCcSDSDA];
                 ; AA2_$siCc1987P12051 ;


INSERT_UPDATE App; code[unique = true]       ; $supercategories; $catalogVersion[unique = true];
                 ; AA2_$siCc1687_CSFSA500    ; cat_201
                 ; AA2_$siCc1687_CSFSA7XX    ; cat_201
                 ; AA2_$siCc1687_DCICRI      ; cat_401
                 ; AA2_$siCc1687_DCICRIN     ; cat_401
                 ; AA2_$siCc1987_ESIADV      ; cat_10101
                 ; AA2_$siCc1987_ESIREPCAT   ; cat_1010101
                 ; AA2_$siCc1987_ESIREPD     ; cat_1010101
                 ; AA2_$siCc1987_ESIREPE     ; cat_1010101
                 ; AA2_$siCc1987_ESIDIAG     ; cat_10101
                 ; AA2_$siCc1987_ESIMASTER   ; cat_10101
                 ; AA2_$siCc1987_TRKOHW1     ; cat_10102
                 ; AA2_$siCc1987_TRKOHW2     ; cat_10102
                 ; AA2_$siCc1987_TRKOHW3     ; cat_10102
                 ; AA2_$siCc1987_TRKTRUCK    ; cat_10102
                 ; AA2_$siCc1987_TSTINFOAW   ; cat_40101
                 ; AA2_$siCc1687_TSTINFODAT  ; cat_40101
                 ; AA2_$siCc1987_TRKUPG      ; cat_10102
                 ; AA2_$siCc1687_CORE_ESIPKG ; cat_1010103
                 ; AA2_$siCc1987_KTS250SD    ; cat_1010104
                 ; AA2_$siCc1687_CRR950      ; cat_401
                 ; AA2_$siCc1987_THLPKW      ; cat_1010102

                 ; AA2_$siCc1987_ADAS_ONE    ; cat_501

INSERT_UPDATE App; code[unique = true]       ; eulaContainers(code); $catalogVersion[unique = true]
                 ; AA2_$siCc1687_DCICRI      ; AA2_DCICRI
                 ; AA2_$siCc1687_DCICRIN     ; AA2_DCICRIN
                 ; AA2_$siCc1687_TSTINFODAT  ; AA2_ESItronic
                 ; AA2_$siCc1687_CORE_ESIPKG ; AA2_CoRe
                 ; AA2_$siCc1987_TSTINFOAW   ; AA2_ESItronic
                 ; AA2_$siCc1687_CSFSA500    ; AA2_FSA
                 ; AA2_$siCc1687_CSFSA7XX    ; AA2_FSA
                 ; AA2_$siCc1987_KTS250SD    ; AA2_ESItronic
                 ; AA2_$siCc1687_CRR950      ; AA2_ESItronic
                 ; AA2_$siCc1987_TRKOHW3     ; AA2_ESItronic
                 ; AA2_$siCc1987_THLPKW      ; AA2_THL
