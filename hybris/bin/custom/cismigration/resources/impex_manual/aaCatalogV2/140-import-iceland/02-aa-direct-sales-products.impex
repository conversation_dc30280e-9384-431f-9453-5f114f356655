#% impex.setLocale( Locale.ENGLISH );

# numerical code for IS, used as prefix for product code
$isCc = 354
$aaCc = 040
$aaPackageName = com.sast.aa.is.

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf

# please make sure, to use right DELEVELOPER COMPANY for Denmark in the right environment.
# live
# $companyId =
# demo
# $companyId = dee8bdda-3321-4853-bc09-32f85de66c2f
# dev
#$companyId = b590385e-9141-4546-ae0f-5b2bfd8858a8
# local
$companyId = 8b871953-7b66-476d-9a42-4681a99bda2e

$packDiagnosticDescription = Indgangspakke til professionel diagnosticering, reparation og vedligehold. Pakken giver en kompetent elektronisk diagnose og tilbyder et bredt udvalg af andre funktioner til alle de dækkede køretøjer.
$packDiagnosticDescription_en = The entry into professional diagnosis, repair and maintenance. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.
$packAdvancedDescription = Det næste niveau af professionel værkstedsudstyr. Udover funktionerne fra Diagnostic-pakken inkluderes instruktioner og manualer. Connected Repair tilbyder service- vedligeholdelseshistorik samt reparationsinformation.
$packAdvancedDescription_en = The next level of professional workshop equipment. In addition to the Diagnostic package, instructions and manuals are included. Connected Repair provides service and maintenance history and repair information.
$packMasterDescription = Den komplette pakke til professionel køretøjsdiagnose. Pakken tilbyder al den nødvendige information til diagnosticering, reparation, vedligeholdelse, reservedele, dokumentation og data management. Den tekniske support assisterer med at finde løsninger.
$packMasterDescription_en = The fully comprehensive package for professional vehicle diagnostics. It provides all the necessary information for diagnosis, repair, maintenance, spare parts, documentation and data management. Technical Support assists you in finding solutions.

$packComponentCatDescription = Reservedelskatalog pakken inkluderer applikationer, funktioner og biludstyr, såvel som diesel og elektriske reservedele. Inklusiv arkiv og elektrisk reservedel ESI[tronic]-F.
$packComponentCatDescription_en = The spare parts catalog package includes the applications, functions and automotive equipment as well as the diesel spare parts and electrical spare parts incl. archive and electrical spare part ESI[tronic]-F.
$packComponentRepairDieselDescription = Repair Diesel pakken tilbyder information om reservedele og reparation af diesel og elektriske komponenter. Det tillader identifikation af køretøjet, Bosch biludstyr, og inkluderer reparationsinstruktioner og service information.
$packComponentRepairDieselDescription_en = The Repair Diesel package provides information on spare parts and repair of diesel and electrical components. It allows identification of the vehicle, Bosch automotive equipment and includes repair instructions & service information.
$packComponentRepairElectricDescription = Det stigende antal af køretøjs-modeller gør det svært for værksteder at have opdateret information om køretøjernes elektriske systemer tilgængeligt. Repair Electrics Package giver support til reservedels-data på bilens elsystem i et tydeligt format.
$packComponentRepairElectricDescription_en = The increasing number of vehicle models makes it difficult for workshops to have up-to-date information on vehicle electrical systems at their fingertips. The Repair Electrics Package provides support with spare parts data on car electrics in a clear format.

$packTruckDescription = Truck pakken hjælper værksteder med pålidelig diagnosticering, komplet vedligeholdelse og effektiv reparation af alle almene lette og tunge køretøjer, trailers, vans og busser.
$packTruckDescription_en = The Truck package supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.

$ohw1Description = Diagnostic pakken til landbrugsmaskiner tilbyder information om diagnose, vedligeholdelse og reparation. Inkluderer bl.a. justeringer og parametrisering af hydrauliksystemer.
$ohw1Description_en = The Agricultural Machinery Diagnostic Package provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw2Description = Diagnostic pakken til entreprenørmaskiner og motorer tilbyder information om diagnose, vedligeholdelse og reparation. Inkluderer bl.a. justeringer og parametrisering af hydrauliksystemer.
$ohw2Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.

$thlPkwDescription = Har du brug for teknisk support til at vedligeholde eller reparere en bil, eller bare en pålidelig second opinion? Så kontakt vores supportteam og få hurtige og gode løsninger.
$thlPkwDescription_en = Do you need technical support to maintain or repair a car, or simply a reliable second opinion? Then contact our support team and get fast and sound solutions.

$compacFsa500Description = CompacSoft[plus] til FSA500 er udstyret med forhåndsindstillede komponent-test og kan forbindes til eksisterende systemer samt anvendes til gradvist at ekspandere værkstedets test system.
$compacFsa500Description_en = The CompacSoft[plus] for FSA 500 is equipped with preset component tests and can be connected to existing systems, as well as used to gradually expand your workshop test system.
$compacFsa7xxDescription = CompacSoft[plus] til FSA 7xx tilbyder en øget komfort til alle målingsopgaver på køretøjet via de guidede menu test trin, muligheden for køretøjs-specifik indstillingsværdier, og display af faktiske værdier.
$compacFsa7xxDescription_en = With CompacSoft[plus] for FSA 7xx, the comfort for all measuring tasks on the vehicle is further increased by the menu-guided test steps, the optional vehicle-specific set values, as well as the display of the actual values.

$criDescription = ESI[tronic] CRI for DCI 700-softwaren giver opdaterede data, sikrer problemfri processer og omfatter test af piezoinjektorer til common rail-systemer.
$criDescription_en = CRI for DCI 700 software provides up-to-date data, ensures smooth processes and includes testing of piezo injectors for common rail systems.
$crinDescription = ESI[tronic] CRIN for DCI 700-softwaren leverer opdaterede data, sikrer problemfri processer og omfatter test af magnetventilinjektorer til common rail-systemer.
$crinDescription_en = CRIN for DCI 700 software provides up-to-date data, ensures smooth processes and includes the testing of solenoid valve injectors for common rail systems.

# TODO: UPDATE TRANSLATION
$adasDescription = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.
$adasDescription_en = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.

$coReDescription = Bosch Connected Repair er en software, der forbinder værkstedsudstyr, køretøjs- og reparationsdata. Uanset om det drejer sig om fejl eller lagring af data og billeder i overensstemmelse med den generelle databeskyttelsesforordning - CoRe er tilpasset til at opfylde kundernes behov.
$coReDescription_en = Bosch Connected Repair is software that connects workshop equipment, vehicle and repair data. Whether in the event of malfunctions or the storage of data and images in accordance with the Basic Data Protection Regulation - CoRe has been adapted to the needs of customers.

$kts250SDDescription = Indgangen til professionel diagnose, reparation og vedligeholdelse specifikt for KTS 250. Det muliggør kompetent elektrisk diagnose og tilbyder en bred vifte af andre funktioner for alle dækkede køretøjer.
$kts250SDDescription_en = The entry into professional diagnosis, repair and maintenance specifically for KTS 250. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.

$infoartWDescription = ESI[tronic] 2.0 Infoart W indeholder oplysninger om dieseltestværdier for in-line pumpekombinationer samt for VE-pumper, den komplette testprocedure fra bestemmelse af de målte værdier til udskrivning af rapporten og visning af testtrinene i den optimale rækkefølge.
$infoartWDescription_en = The ESI[tronic] 2.0 Infoart W contains information about diesel test values for in-line pump combinations as well as for VE pumps, the complete test process test procedure from the determination of the measured values to the printout of the report and the display of the test steps in the optimum sequence.

$infoartTestdataDescription = ESI[tronic] 2.0-Infotype Testdata (CD) indeholder testværdier for Bosch Common Rail-højtrykspumper, Common Rail-injektorer og VP 29/30/44-fordelerindsprøjtningspumper.
$infoartTestdataDescription_en = The ESI[tronic] 2.0-Infotype Testdata (CD) contains test values for Bosch Common Rail high pressure pumps, Common Rail injectors and VP 29 / 30 / 44 distributor injection pumps.
$truckUpgradeDescription = Truck opgraderings-pakke til eksisterende ESI[tronic] Advanced kunder. Indeholder diagnose, vedligeholdelse og reparation til lette og tunge erhvervskøretøjer, trailere, varevogne og busser.
$truckUpgradeDescription_en = The Truck Upgrade package is dedicated to ESI[tronic] Car users and supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.

$compacFsa500Full3YBrimName = CompacSoft[plus] FSA 500 (3 years)
$compacFsa500SubsBrimName = CompacSoft[plus] FSA 500
$compacFsa7xxFull3YBrimName = CompacSoft[plus] FSA 7xx (3 years)
$compacFsa7xxSubsBrimName = CompacSoft[plus] FSA 7xx
$criSubsS1BrimName = Component DCI-CRI
$criSubsM3BrimName = Component DCI-CRI_Multi-users_3
$criFull3YBrimName = Component DCI-CRI (3 year)
$crinSubsS1BrimName = Component DCI-CRIN
$crinSubsM3BrimName = Component DCI-CRIN_Multi-users_3
$crinFull3YBrimName = Component DCI-CRIN (3 year)
$packAdvancedSubsS1BrimName = ESI 2.0 Advanced Unlimited
$packAdvancedSubsM3BrimName = ESI 2.0 Advanced Unlimited Multi
$packAdvancedFull3YS1BrimName = ESI 2.0 Advanced 3 years
$packAdvancedFull3YM3BrimName = ESI 2.0 Advanced 3 years Multi
$packComponentCatSubsS1BrimName = ESI 2.0 ComponentCatalog D+E Unlimited
$packComponentCatSubsM3BrimName = ESI 2.0 ComponentCatalog D+E Unlim Multi
$packComponentCatFull3YS1BrimName = ESI 2.0 ComponentCatalog D+E 3 years
$packComponentCatFull3YM3BrimName = ESI 2.0 ComponentCatalog D+E 3y Multi
$packComponentRepairDieselSubsS1BrimName = ESI 2.0 ComponentRepair D+E Unlimited
$packComponentRepairDieselSubsM3BrimName = ESI 2.0 ComponentRepair D+E Unlim Multi
$packComponentRepairDieselFull3YS1BrimName = ESI 2.0 ComponentRepair D+E 3 years
$packComponentRepairDieselFull3YM3BrimName = ESI 2.0 ComponentRepair D+E 3y Multi
$packComponentRepairElectricSubsS1BrimName = ESI 2.0 ComponentRepair E Unlimited
$packComponentRepairElectricSubsM3BrimName = ESI 2.0 ComponentRepair E Unlim Multi
$packComponentRepairElectricFull3YS1BrimName = ESI 2.0 ComponentRepair E 3 years
$packComponentRepairElectricFull3YM3BrimName = ESI 2.0 ComponentRepair E 3 years Multi
$packDiagnosticSubsS1BrimName = ESI 2.0 Diagnostic Unlimited
$packDiagnosticSubsM3BrimName = ESI 2.0 Diagnostic Unlimited Multi
$packDiagnosticFull3YS1BrimName = ESI 2.0 Diagnostic 3 years
$packDiagnosticFull3YM3BrimName = ESI 2.0 Diagnostic 3 years Multi
$packMasterSubsS1BrimName = ESI 2.0 Master Unlimited
$packMasterSubsM3BrimName = ESI 2.0 Master Unlimited Multi
$packMasterFull3YS1BrimName = ESI 2.0 Master 3 years
$packMasterFull3YM3BrimName = ESI 2.0 Master 3 years Multi

$ohw1SubsS1BrimName = ESI[tronic] 2.0 Truck OHW I
$ohw1SubsM3BrimName = ESI[tronic] 2.0 Truck OHW I Multi
$ohw1Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW I (3 years)
$ohw1Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW I (3y) Multi

$ohw2SubsS1BrimName = ESI[tronic] 2.0 Truck OHW II
$ohw2SubsM3BrimName = ESI[tronic] 2.0 Truck OHW II Multi
$ohw2Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW II (3 years)
$ohw2Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW II (3y) Multi

$packTruckSubsS1BrimName = ESI[tronic] 2.0 Truck
$packTruckSubsM3BrimName = ESI[tronic] 2.0 Truck Multi
$packTruckFull3YS1BrimName = ESI[tronic] 2.0 Truck (3 years)
$packTruckFull3YM3BrimName = ESI[tronic] 2.0 Truck (3 years) Multi
$packTruckFullBrimName = ESI[tronic] 2.0 Truck (Einmalkauf)
$infoartWSubsBrimName = ESI[tronic] W Diesel Test Data (WP)
$infoartWFull3YBrimName = ESI[tronic] W (3 Jahre)
$thlPkwSubsBrimName = THL use technical hotline
$thlPkwFull3YBrimName = Technische Hotline LKW (3 Jahre)
$thlTrkSubsBrimName = Technische Hotline ESI[tronic] for Truck
$thlPkwTrkSubsBrimName = Technische Hotline fuer LKWs und PKWs
$infoartTestdataSubsBrimName = Testdata VP-M/CP
$truckUpgradeSubsS1BrimName = ESI[tronic] 2.0 Truck Upgrade
$truckUpgradeSubsM3BrimName = ESI[tronic] 2.0 Truck Upgrade Multi
$truckUpgradeFull3YS1BrimName = ESI[tronic] 2.0 Truck Upgrade (3 years)
$truckUpgradeFull3YM3BrimName = ESI[tronic] 2.0 Truck Upgrade (3y) Multi

$coreSubsS1BrimName = CoRe for ESI 2.0 packages

$kts250SDFullBrimName = KTS 250 SD (OTP one time purchase)
$kts250SDSubsBrimName = KTS 250 SD ECU Diagnosis

$ohw3Description = In dem Diagnose Paket Baumaschinen und Motoren stehen Informationen zur Diagnose, Wartung und Reparatur von landwirtschaftlich genutzten Fahrzeugen zur Verfügung. Enthalten sind unter anderem Einstell- und Parametrierfunktionen an Hydrauliksystemen.
$ohw3Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.

$ohw3S1BrimName = ESI[tronic] Truck OHW III Unlimited
$ohw3M3BrimName = ESI[tronic] Truck OHW III Unlimited Multi
$ohw3Full3YM3BrimName = ESI[tronic] Truck OHW III (3y) Multi

$crr950DieselInjectorsRepairSoftDescription = CRR 950 Diesel-Injektor-Reparatur-Software
$crr950DieselInjectorsRepairSoftDescription_en = The CRR 950 guides and support the professional repairs of magnetic solenoid common-rail injectors from Bosch
$crr950DieselInjectorsRepairSoftwareBrimName1L = CRR 950 Diesel Injectors (1 License)
$crr950DieselInjectorsRepairSoftwareBrimName3L = CRR 950 Diesel Injectors (3 Licenses)
$fsa7xxMulti = CompacSoft[plus] FSA 7xx Multi
$fsa7xx3YearsMulti = CompacSoft[plus] FSA 7xx (3 years) Multi
$fsa5xxMulti = CompacSoft[plus] FSA 500 Multi
$fsa5xx3YearsMulti = CompacSoft[plus] FSA 500 (3 years)Multi
$adasSubsS1BrimName = ADAS ONE - DATA
$adasFull3YS1BrimName = ADAS ONE - DATA (3 years)

$countryRestricted = true
# @formatter:off
$enabledIn = IS
# @formatter:on

INSERT_UPDATE App; code[unique = true]       ; packageName                    ; name[lang = da]                          ; summary[lang = da]                          ; description[lang = da]                      ; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$isCc1687_CSFSA500    ; $aaPackageName1687_CSFSA500    ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description                    ; $compacFsa500Description                    ;
                 ; AA2_$isCc1687_CSFSA7XX    ; $aaPackageName1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription                    ; $compacFsa7xxDescription                    ;
                 ; AA2_$isCc1687_DCICRI      ; $aaPackageName1687_DCICRI      ; Komponent DCI-CRI                        ; $criDescription                             ; $criDescription                             ;
                 ; AA2_$isCc1687_DCICRIN     ; $aaPackageName1687_DCICRIN     ; Komponent DCI-CRIN                       ; $crinDescription                            ; $crinDescription                            ;
                 ; AA2_$isCc1987_ESIADV      ; $aaPackageName1987_ESIADV      ; ESI[tronic] 2.0 Advanced                 ; $packAdvancedDescription                    ; $packAdvancedDescription                    ;
                 ; AA2_$isCc1987_ESIREPCAT   ; $aaPackageName1987_ESIREPCAT   ; ESI[tronic] 2.0 Catalogue                ; $packComponentCatDescription                ; $packComponentCatDescription                ;
                 ; AA2_$isCc1987_ESIREPD     ; $aaPackageName1987_ESIREPD     ; ESI[tronic] 2.0 Repair 2                 ; $packComponentRepairDieselDescription       ; $packComponentRepairDieselDescription       ;
                 ; AA2_$isCc1987_ESIREPE     ; $aaPackageName1987_ESIREPE     ; ESI[tronic] 2.0 Repair 1                 ; $packComponentRepairElectricDescription     ; $packComponentRepairElectricDescription     ;
                 ; AA2_$isCc1987_ESIDIAG     ; $aaPackageName1987_ESIDIAG     ; ESI[tronic] 2.0 Diagnostic               ; $packDiagnosticDescription                  ; $packDiagnosticDescription                  ;
                 ; AA2_$isCc1987_ESIMASTER   ; $aaPackageName1987_ESIMASTER   ; ESI[tronic] 2.0 Master                   ; $packMasterDescription                      ; $packMasterDescription                      ;
                 ; AA2_$isCc1987_TRKOHW1     ; $aaPackageName1987_TRKOHW1     ; ESI[tronic] 2.0 Truck OHW I              ; $ohw1Description                            ; $ohw1Description                            ;
                 ; AA2_$isCc1987_TRKOHW2     ; $aaPackageName1987_TRKOHW2     ; ESI[tronic] 2.0 Truck OHW II             ; $ohw2Description                            ; $ohw2Description                            ;
                 ; AA2_$isCc1987_TRKTRUCK    ; $aaPackageName1987_TRKTRUCK    ; ESI[tronic] 2.0 Truck                    ; $packTruckDescription                       ; $packTruckDescription                       ;
                 ; AA2_$isCc1987_TSTINFOAW   ; $aaPackageName1987_TSTINFOAW   ; ESI[tronic] W                            ; $infoartWDescription                        ; $infoartWDescription                        ;
                 ; AA2_$isCc1987_THLPKW      ; $aaPackageName1987_THLPKW      ; Teknisk hotline bil                      ; $thlPkwDescription                          ; $thlPkwDescription                          ;
                 ; AA2_$isCc1687_TSTINFODAT  ; $aaPackageName1687_TSTINFODAT  ; Testdata VP-M/CP                         ; $infoartTestdataDescription                 ; $infoartTestdataDescription                 ;
                 ; AA2_$isCc1987_TRKUPG      ; $aaPackageName1987_TRUCKUPG    ; ESI[tronic] 2.0 Truck Upgrade            ; $truckUpgradeDescription                    ; $truckUpgradeDescription                    ;
                 ; AA2_$isCc1687_CORE_ESIPKG ; $aaPackageName1687_CORE_ESIPKG ; Connected Repair                         ; $coReDescription                            ; $coReDescription                            ;
                 ; AA2_$isCc1987_KTS250SD    ; $aaPackageName1987_KTS250      ; KTS 250 SD ECU-diagnose                  ; $kts250SDDescription                        ; $kts250SDDescription                        ;
                 ; AA2_$isCc1987_TRKOHW3     ; $aaPackageName1987_TRKOHW3     ; Off Highway III (OHW III)                ; $ohw3Description                            ; $ohw3Description                            ;
                 ; AA2_$isCc1687_CRR950      ; $aaPackageName1687_CRR950      ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription ; $crr950DieselInjectorsRepairSoftDescription ;
                 ; AA2_$isCc1987_ADAS_ONE    ; $aaPackageName1987_ADAS_ONE    ; ADAS One Solution                        ; $adasDescription                            ; $adasDescription                            ;

INSERT_UPDATE App; code[unique = true]       ; name[lang = en]                          ; summary[lang = en]                             ; description[lang = en]                         ; $catalogVersion[unique = true];
                 ; AA2_$isCc1687_CSFSA500    ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description_en                    ; $compacFsa500Description_en
                 ; AA2_$isCc1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription_en                    ; $compacFsa7xxDescription_en
                 ; AA2_$isCc1687_DCICRI      ; Component DCI-CRI                        ; $criDescription_en                             ; $criDescription_en
                 ; AA2_$isCc1687_DCICRIN     ; Component DCI-CRIN                       ; $crinDescription_en                            ; $crinDescription_en
                 ; AA2_$isCc1987_ESIADV      ; ESI[tronic] 2.0 Advanced                 ; $packAdvancedDescription_en                    ; $packAdvancedDescription_en
                 ; AA2_$isCc1987_ESIREPCAT   ; ESI[tronic] 2.0 ComponentCat D+E         ; $packComponentCatDescription_en                ; $packComponentCatDescription_en
                 ; AA2_$isCc1987_ESIREPD     ; ESI[tronic] 2.0 ComponentRepair D+E      ; $packComponentRepairDieselDescription_en       ; $packComponentRepairDieselDescription_en
                 ; AA2_$isCc1987_ESIREPE     ; ESI[tronic] 2.0 ComponentRepair E        ; $packComponentRepairElectricDescription_en     ; $packComponentRepairElectricDescription_en
                 ; AA2_$isCc1987_ESIDIAG     ; ESI[tronic] 2.0 Diagnostic               ; $packDiagnosticDescription_en                  ; $packDiagnosticDescription_en
                 ; AA2_$isCc1987_ESIMASTER   ; ESI[tronic] 2.0 Master                   ; $packMasterDescription_en                      ; $packMasterDescription_en
                 ; AA2_$isCc1987_TRKOHW1     ; ESI[tronic] 2.0 Truck OHW I              ; $ohw1Description_en                            ; $ohw1Description_en
                 ; AA2_$isCc1987_TRKOHW2     ; ESI[tronic] 2.0 Truck OHW II             ; $ohw2Description_en                            ; $ohw2Description_en
                 ; AA2_$isCc1987_TRKTRUCK    ; ESI[tronic] 2.0 Truck                    ; $packTruckDescription_en                       ; $packTruckDescription_en
                 ; AA2_$isCc1987_TSTINFOAW   ; ESI[tronic] W                            ; $infoartWDescription_en                        ; $infoartWDescription_en
                 ; AA2_$isCc1987_THLPKW      ; Technical Hotline Cars                   ; $thlPkwDescription_en                          ; $thlPkwDescription_en
                 ; AA2_$isCc1687_TSTINFODAT  ; Testdata VP-M/CP                         ; $infoartTestdataDescription_en                 ; $infoartTestdataDescription_en
                 ; AA2_$isCc1987_TRKUPG      ; ESI[tronic] 2.0 Truck Upgrade            ; $truckUpgradeDescription_en                    ; $truckUpgradeDescription_en
                 ; AA2_$isCc1687_CORE_ESIPKG ; CoRe Server + Online                     ; $coReDescription_en                            ; $coReDescription_en
                 ; AA2_$isCc1987_KTS250SD    ; KTS 250 SD ECU Diagnosis                 ; $kts250SDDescription_en                        ; $kts250SDDescription_en
                 ; AA2_$isCc1987_TRKOHW3     ; ESI[tronic] Truck OHW III                ; $ohw3Description_en                            ; $ohw3Description_en                            ;
                 ; AA2_$isCc1687_CRR950      ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription_en ; $crr950DieselInjectorsRepairSoftDescription_en ;
                 ; AA2_$isCc1987_ADAS_ONE    ; ADAS One Solution                        ; $adasDescription_en                            ; $adasDescription_en                            ;


INSERT_UPDATE ProductContainer; code[unique = true]        ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$isCc1687_CSFSA500    ; AA2_$isCc1687_CSFSA500
                              ; pcaa_$isCc1687_CSFSA7XX    ; AA2_$isCc1687_CSFSA7XX
                              ; pcaa_$isCc1687_DCICRI      ; AA2_$isCc1687_DCICRI
                              ; pcaa_$isCc1687_DCICRIN     ; AA2_$isCc1687_DCICRIN
                              ; pcaa_$isCc1987_ESIADV      ; AA2_$isCc1987_ESIADV
                              ; pcaa_$isCc1987_ESIREPCAT   ; AA2_$isCc1987_ESIREPCAT
                              ; pcaa_$isCc1987_ESIREPD     ; AA2_$isCc1987_ESIREPD
                              ; pcaa_$isCc1987_ESIREPE     ; AA2_$isCc1987_ESIREPE
                              ; pcaa_$isCc1987_ESIDIAG     ; AA2_$isCc1987_ESIDIAG
                              ; pcaa_$isCc1987_ESIMASTER   ; AA2_$isCc1987_ESIMASTER
                              ; pcaa_$isCc1987_TRKOHW1     ; AA2_$isCc1987_TRKOHW1
                              ; pcaa_$isCc1987_TRKOHW2     ; AA2_$isCc1987_TRKOHW2
                              ; pcaa_$isCc1987_TRKTRUCK    ; AA2_$isCc1987_TRKTRUCK
                              ; pcaa_$isCc1987_TSTINFOAW   ; AA2_$isCc1987_TSTINFOAW
                              ; pcaa_$isCc1987_THLPKW      ; AA2_$isCc1987_THLPKW
                              ; pcaa_$isCc1687_TSTINFODAT  ; AA2_$isCc1687_TSTINFODAT
                              ; pcaa_$isCc1987_TRKUPG      ; AA2_$isCc1987_TRKUPG
                              ; pcaa_$isCc1687_CORE_ESIPKG ; AA2_$isCc1687_CORE_ESIPKG
                              ; pcaa_$isCc1987_KTS250SD    ; AA2_$isCc1987_KTS250SD
                              ; pcaa_$isCc1987_TRKOHW3     ; AA2_$isCc1987_TRKOHW3
                              ; pcaa_$isCc1687_CRR950      ; AA2_$isCc1687_CRR950
                              ; pcaa_$isCc1987_ADAS_ONE    ; AA2_$isCc1987_ADAS_ONE

INSERT_UPDATE AppLicense; code[unique = true] ; $baseProduct              ; sellerProductId; brimName[lang = en]                            ; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); userGroups(uid); specifiedPrice; enabledCountries(isocode)[default = $enabledIn]; billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; availabilityStatus(code)[default = PUBLISHED]
                        ; AA2_$isCc1687P15063 ; AA2_$isCc1687_CSFSA500    ; 1687P15063     ; $compacFsa500Full3YBrimName                    ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; IDW000,WD0001  ; 319.00        ; $enabledIn
                        ; AA2_$isCc1687P15060 ; AA2_$isCc1687_CSFSA500    ; 1687P15060     ; $compacFsa500SubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; IDW000,WD0001  ; 121.00        ; $enabledIn
                        ; AA2_$isCc1687P15048 ; AA2_$isCc1687_CSFSA7XX    ; 1687P15048     ; $compacFsa7xxFull3YBrimName                    ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; IDW000,WD0001  ; 1170.00       ; $enabledIn
                        ; AA2_$isCc1687P15045 ; AA2_$isCc1687_CSFSA7XX    ; 1687P15045     ; $compacFsa7xxSubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; IDW000,WD0001  ; 443.00        ; $enabledIn
                        ; AA2_$isCc1687P15093 ; AA2_$isCc1687_DCICRI      ; 1687P15093     ; $criFull3YBrimName                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; IDW000,WD0001  ; 911.00        ; $enabledIn
                        ; AA2_$isCc1687P15090 ; AA2_$isCc1687_DCICRI      ; 1687P15090     ; $criSubsS1BrimName                             ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 345.00        ; $enabledIn
                        ; AA2_$isCc1687P15102 ; AA2_$isCc1687_DCICRI      ; 1687P15102     ; $criSubsM3BrimName                             ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000,WD0001  ; 415.00        ; $enabledIn
                        ; AA2_$isCc1687P15103 ; AA2_$isCc1687_DCICRIN     ; 1687P15103     ; $crinFull3YBrimName                            ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; IDW000,WD0001  ; 911.00        ; $enabledIn
                        ; AA2_$isCc1687P15100 ; AA2_$isCc1687_DCICRIN     ; 1687P15100     ; $crinSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 345.00        ; $enabledIn
                        ; AA2_$isCc1687P15107 ; AA2_$isCc1687_DCICRIN     ; 1687P15107     ; $crinSubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000,WD0001  ; 415.00        ; $enabledIn
                        ; AA2_$isCc1987P12840 ; AA2_$isCc1987_ESIADV      ; 1987P12840     ; $packAdvancedSubsS1BrimName                    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 1500.00       ; $enabledIn
                        ; AA2_$isCc1987P12847 ; AA2_$isCc1987_ESIADV      ; 1987P12847     ; $packAdvancedSubsM3BrimName                    ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000,WD0001  ; 1630.00       ; $enabledIn
                        ; AA2_$isCc1987P12988 ; AA2_$isCc1987_ESIREPCAT   ; 1987P12988     ; $packComponentCatFull3YS1BrimName              ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; IDW000,WD0001  ; 845.00        ; $enabledIn
                        ; AA2_$isCc1987P12998 ; AA2_$isCc1987_ESIREPCAT   ; 1987P12998     ; $packComponentCatSubsS1BrimName                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 320.00        ; $enabledIn
                        ; AA2_$isCc1987P12783 ; AA2_$isCc1987_ESIREPCAT   ; 1987P12783     ; $packComponentCatFull3YM3BrimName              ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; IDW000,WD0001  ; 1030.00       ; $enabledIn
                        ; AA2_$isCc1987P12784 ; AA2_$isCc1987_ESIREPCAT   ; 1987P12784     ; $packComponentCatSubsM3BrimName                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000,WD0001  ; 390.00        ; $enabledIn
                        ; AA2_$isCc1987P12973 ; AA2_$isCc1987_ESIREPD     ; 1987P12973     ; $packComponentRepairDieselFull3YS1BrimName     ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; IDW000,WD0001  ; 1769.00       ; $enabledIn
                        ; AA2_$isCc1987P12970 ; AA2_$isCc1987_ESIREPD     ; 1987P12970     ; $packComponentRepairDieselSubsS1BrimName       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 670.00        ; $enabledIn
                        ; AA2_$isCc1987P12296 ; AA2_$isCc1987_ESIREPD     ; 1987P12296     ; $packComponentRepairDieselFull3YM3BrimName     ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; IDW000,WD0001  ; 1954.00       ; $enabledIn
                        ; AA2_$isCc1987P12297 ; AA2_$isCc1987_ESIREPD     ; 1987P12297     ; $packComponentRepairDieselSubsM3BrimName       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000,WD0001  ; 740.00        ; $enabledIn
                        ; AA2_$isCc1987P12993 ; AA2_$isCc1987_ESIREPE     ; 1987P12993     ; $packComponentRepairElectricFull3YS1BrimName   ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; IDW000,WD0001  ; 766.00        ; $enabledIn
                        ; AA2_$isCc1987P12990 ; AA2_$isCc1987_ESIREPE     ; 1987P12990     ; $packComponentRepairElectricSubsS1BrimName     ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 290.00        ; $enabledIn
                        ; AA2_$isCc1987P12294 ; AA2_$isCc1987_ESIREPE     ; 1987P12294     ; $packComponentRepairElectricFull3YM3BrimName   ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; IDW000,WD0001  ; 950.00        ; $enabledIn
                        ; AA2_$isCc1987P12295 ; AA2_$isCc1987_ESIREPE     ; 1987P12295     ; $packComponentRepairElectricSubsM3BrimName     ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000,WD0001  ; 360.00        ; $enabledIn
                        ; AA2_$isCc1987P12820 ; AA2_$isCc1987_ESIDIAG     ; 1987P12820     ; $packDiagnosticSubsS1BrimName                  ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 850.00        ; $enabledIn
                        ; AA2_$isCc1987P12824 ; AA2_$isCc1987_ESIDIAG     ; 1987P12824     ; $packDiagnosticSubsM3BrimName                  ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000,WD0001  ; 980.00        ; $enabledIn
                        ; AA2_$isCc1987P12913 ; AA2_$isCc1987_ESIMASTER   ; 1987P12913     ; $packMasterFull3YS1BrimName                    ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; IDW000,WD0001  ; 5544.00       ; $enabledIn
                        ; AA2_$isCc1987P12910 ; AA2_$isCc1987_ESIMASTER   ; 1987P12910     ; $packMasterSubsS1BrimName                      ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 2100.00       ; $enabledIn
                        ; AA2_$isCc1987P12916 ; AA2_$isCc1987_ESIMASTER   ; 1987P12916     ; $packMasterFull3YS1BrimName                    ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; IDW000,WD0001  ; 5887.80       ; $enabledIn
                        ; AA2_$isCc1987P12917 ; AA2_$isCc1987_ESIMASTER   ; 1987P12917     ; $packMasterSubsM3BrimName                      ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000,WD0001  ; 2230.00       ; $enabledIn
                        ; AA2_$isCc1987P12263 ; AA2_$isCc1987_TRKOHW1     ; 1987P12263     ; $ohw1Full3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; IDW000,WD0001  ; 1769.00       ; $enabledIn
                        ; AA2_$isCc1987P12260 ; AA2_$isCc1987_TRKOHW1     ; 1987P12260     ; $ohw1SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 670.00        ; $enabledIn
                        ; AA2_$isCc1987P12262 ; AA2_$isCc1987_TRKOHW1     ; 1987P12262     ; $ohw1SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000,WD0001  ; 800.00        ; $enabledIn
                        ; AA2_$isCc1987P12265 ; AA2_$isCc1987_TRKOHW1     ; 1987P12265     ; $ohw1Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; IDW000,WD0001  ; 2112.00       ; $enabledIn
                        ; AA2_$isCc1987P12280 ; AA2_$isCc1987_TRKOHW2     ; 1987P12280     ; $ohw2Full3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; IDW000,WD0001  ; 3155.00       ; $enabledIn
                        ; AA2_$isCc1987P12278 ; AA2_$isCc1987_TRKOHW2     ; 1987P12278     ; $ohw2SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 1195.00       ; $enabledIn
                        ; AA2_$isCc1987P12275 ; AA2_$isCc1987_TRKOHW2     ; 1987P12275     ; $ohw2SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000,WD0001  ; 1325.00       ; $enabledIn
                        ; AA2_$isCc1987P12276 ; AA2_$isCc1987_TRKOHW2     ; 1987P12276     ; $ohw2Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; IDW000,WD0001  ; 3498.00       ; $enabledIn
                        ; AA2_$isCc1987P12402 ; AA2_$isCc1987_TRKTRUCK    ; 1987P12402     ; $packTruckFull3YS1BrimName                     ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; IDW000,WD0001  ; 3722.00       ; $enabledIn
                        ; AA2_$isCc1987P12400 ; AA2_$isCc1987_TRKTRUCK    ; 1987P12400     ; $packTruckSubsS1BrimName                       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 1410.00       ; $enabledIn
                        ; AA2_$isCc1987P12936 ; AA2_$isCc1987_TRKTRUCK    ; 1987P12936     ; $packTruckSubsM3BrimName                       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000,WD0001  ; 1540.00       ; $enabledIn
                        ; AA2_$isCc1987P12937 ; AA2_$isCc1987_TRKTRUCK    ; 1987P12937     ; $packTruckFull3YM3BrimName                     ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; IDW000,WD0001  ; 4066.00       ; $enabledIn
                        ; AA2_$isCc1987P12503 ; AA2_$isCc1987_TSTINFOAW   ; 1987P12503     ; $infoartWFull3YBrimName                        ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; IDW000,WD0001  ; 2112.00       ; $enabledIn
                        ; AA2_$isCc1987P12500 ; AA2_$isCc1987_TSTINFOAW   ; 1987P12500     ; $infoartWSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; IDW000,WD0001  ; 800.00        ; $enabledIn
                        ; AA2_$isCc1987729274 ; AA2_$isCc1987_THLPKW      ; 1987729274     ; $thlPkwSubsBrimName                            ;                                          ; runtime_subs_unlimited ; <ignore>        ; IDW000,WD0001  ; 645.00        ; $enabledIn
                        ; AA2_$isCc1687P15015 ; AA2_$isCc1687_TSTINFODAT  ; 1687P15015     ; $infoartTestdataSubsBrimName                   ;                                          ; runtime_subs_unlimited ; <ignore>        ; IDW000,WD0001  ; 363.00        ; $enabledIn
                        ; AA2_$isCc1987P12404 ; AA2_$isCc1987_TRKUPG      ; 1987P12404     ; $truckUpgradeSubsS1BrimName                    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 1200.00       ; $enabledIn
                        ; AA2_$isCc1987P12140 ; AA2_$isCc1987_TRKUPG      ; 1987P12140     ; $truckUpgradeFull3YS1BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; IDW000,WD0001  ; 3168.00       ; $enabledIn
                        ; AA2_$isCc1987P12359 ; AA2_$isCc1987_TRKUPG      ; 1987P12359     ; $truckUpgradeSubsM3BrimName                    ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000,WD0001  ; 1330.00       ; $enabledIn
                        ; AA2_$isCc1987P12364 ; AA2_$isCc1987_TRKUPG      ; 1987P12364     ; $truckUpgradeFull3YM3BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; IDW000,WD0001  ; 3511.00       ; $enabledIn
                        ; AA2_$isCc1687P15130 ; AA2_$isCc1687_CORE_ESIPKG ; 1687P15130     ; $coreSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 1.20          ; $enabledIn
                        ; AA2_$isCc1987P12389 ; AA2_$isCc1987_KTS250SD    ; 1987P12389     ; $kts250SDFullBrimName                          ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; IDW000,WD0001  ; 800.00        ; $enabledIn
                        ; AA2_$isCc1987P12385 ; AA2_$isCc1987_KTS250SD    ; 1987P12385     ; $kts250SDSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; IDW000,WD0001  ; 715.00        ; $enabledIn
                        ; AA2_$isCc1987P12254 ; AA2_$isCc1987_TRKOHW3     ; 1987P12254     ; $ohw3S1BrimName                                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 380           ; $enabledIn
                        ; AA2_$isCc1987P12257 ; AA2_$isCc1987_TRKOHW3     ; 1987P12257     ; $ohw3M3BrimName                                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000,WD0001  ; 484           ; $enabledIn
                        ; AA2_$isCc1987P12256 ; AA2_$isCc1987_TRKOHW3     ; 1987P12256     ; $ohw3Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; IDW000,WD0001  ; 1597          ; $enabledIn
                        ; AA2_$isCc1687P15137 ; AA2_$isCc1687_CRR950      ; 1687P15137     ; $crr950DieselInjectorsRepairSoftwareBrimName1L ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 140           ; $enabledIn
                        ; AA2_$isCc1687P15139 ; AA2_$isCc1687_CRR950      ; 1687P15139     ; $crr950DieselInjectorsRepairSoftwareBrimName3L ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000,WD0001  ; 210           ; $enabledIn
                        ; AA2_$isCc1987P12515 ; AA2_$isCc1987_ADAS_ONE    ; 1987P12515     ; $adasSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; IDW000,WD0001  ; 0.01          ; $enabledIn
                        ; AA2_$isCc1987P12517 ; AA2_$isCc1987_ADAS_ONE    ; 1987P12517     ; $adasFull3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; IDW000,WD0001  ; 0.01          ; $enabledIn
                        ; AA2_$isCc1687P15170 ; AA2_$isCc1687_CSFSA500    ; 1687P15170     ; $fsa5xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000         ; 0.01          ;
                        ; AA2_$isCc1687P15173 ; AA2_$isCc1687_CSFSA500    ; 1687P15173     ; $fsa5xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; IDW000         ; 0.01          ;
                        ; AA2_$isCc1687P15160 ; AA2_$isCc1687_CSFSA7XX    ; 1687P15160     ; $fsa7xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000         ; 0.01          ;
                        ; AA2_$isCc1687P15163 ; AA2_$isCc1687_CSFSA7XX    ; 1687P15163     ; $fsa7xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; IDW000         ; 0.01          ;

# The licenses below were discontinued by MA and should be available only for the contract migration.
UPDATE AppLicense; code[unique = true] ; $catalogVersion[unique = true]; availabilityStatus(code)[default = UNPUBLISHED]
                 ; AA2_$isCc1987P12389 ;

INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price  ; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$isCc1687P15063                          ; 319.00
                      ; AA2_$isCc1687P15060                          ; 121.00
                      ; AA2_$isCc1687P15048                          ; 1170.00
                      ; AA2_$isCc1687P15045                          ; 443.00
                      ; AA2_$isCc1687P15093                          ; 911.00
                      ; AA2_$isCc1687P15090                          ; 345.00
                      ; AA2_$isCc1687P15102                          ; 415.00
                      ; AA2_$isCc1687P15103                          ; 911.00
                      ; AA2_$isCc1687P15100                          ; 345.00
                      ; AA2_$isCc1687P15107                          ; 415.00
                      ; AA2_$isCc1987P12840                          ; 1500.00
                      ; AA2_$isCc1987P12847                          ; 1630.00
                      ; AA2_$isCc1987P12988                          ; 845.00
                      ; AA2_$isCc1987P12998                          ; 320.00
                      ; AA2_$isCc1987P12783                          ; 1030.00
                      ; AA2_$isCc1987P12784                          ; 390.00
                      ; AA2_$isCc1987P12973                          ; 1769.00
                      ; AA2_$isCc1987P12970                          ; 670.00
                      ; AA2_$isCc1987P12296                          ; 1954.00
                      ; AA2_$isCc1987P12297                          ; 740.00
                      ; AA2_$isCc1987P12993                          ; 766.00
                      ; AA2_$isCc1987P12990                          ; 290.00
                      ; AA2_$isCc1987P12294                          ; 950.00
                      ; AA2_$isCc1987P12295                          ; 360.00
                      ; AA2_$isCc1987P12820                          ; 850.00
                      ; AA2_$isCc1987P12824                          ; 980.00
                      ; AA2_$isCc1987P12913                          ; 5544.00
                      ; AA2_$isCc1987P12910                          ; 2100.00
                      ; AA2_$isCc1987P12916                          ; 5887.80
                      ; AA2_$isCc1987P12917                          ; 2230.00
                      ; AA2_$isCc1987P12263                          ; 1769.00
                      ; AA2_$isCc1987P12260                          ; 670.00
                      ; AA2_$isCc1987P12262                          ; 800.00
                      ; AA2_$isCc1987P12265                          ; 2112.00
                      ; AA2_$isCc1987P12280                          ; 3155.00
                      ; AA2_$isCc1987P12278                          ; 1195.00
                      ; AA2_$isCc1987P12275                          ; 1325.00
                      ; AA2_$isCc1987P12276                          ; 3498.00
                      ; AA2_$isCc1987P12402                          ; 3722.00
                      ; AA2_$isCc1987P12400                          ; 1410.00
                      ; AA2_$isCc1987P12936                          ; 1540.00
                      ; AA2_$isCc1987P12937                          ; 4066.00
                      ; AA2_$isCc1987P12503                          ; 2112.00
                      ; AA2_$isCc1987P12500                          ; 800.00
                      ; AA2_$isCc1987729274                          ; 645.00
                      ; AA2_$isCc1687P15015                          ; 363.00
                      ; AA2_$isCc1987P12404                          ; 1200.00
                      ; AA2_$isCc1987P12140                          ; 3168.00
                      ; AA2_$isCc1987P12359                          ; 1330.00
                      ; AA2_$isCc1987P12364                          ; 3511.00
                      ; AA2_$isCc1687P15130                          ; 1.20
                      ; AA2_$isCc1987P12389                          ; 800.00
                      ; AA2_$isCc1987P12385                          ; 715.00
                      ; AA2_$isCc1987P12254                          ; 475.00
                      ; AA2_$isCc1987P12257                          ; 605.00
                      ; AA2_$isCc1987P12256                          ; 1597.00
                      ; AA2_$isCc1687P15137                          ; 140.00 ;
                      ; AA2_$isCc1687P15139                          ; 210.00 ;
                      ; AA2_$isCc1987P12515                          ; 0.01   ;
                      ; AA2_$isCc1987P12517                          ; 0.01   ;
                      ; AA2_$isCc1687P15163                          ; 0.01   ;
                      ; AA2_$isCc1687P15170                          ; 0.01   ;
                      ; AA2_$isCc1687P15160                          ; 0.01   ;
                      ; AA2_$isCc1687P15173                          ; 0.01   ;




INSERT_UPDATE App; code[unique = true]     ; boms(code); $catalogVersion[unique = true]
                 ; AA2_$isCc1987_ESIADV    ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe
                 ; AA2_$isCc1987_ESIREPCAT ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE
                 ; AA2_$isCc1987_ESIREPD   ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE,MAT_$aaCcK3
                 ; AA2_$isCc1987_ESIREPE   ; MAT_$aaCcA,MAT_$aaCcE,MAT_$aaCcK2
                 ; AA2_$isCc1987_ESIDIAG   ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB
                 ; AA2_$isCc1987_ESIMASTER ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe,MAT_$aaCcM,MAT_$aaCcP
                 ; AA2_$isCc1987_TRKOHW1   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$isCc1987_TRKOHW2   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$isCc1987_TRKTRUCK  ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$isCc1987_TRKUPG    ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$isCc1987_TRKOHW3   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK


INSERT_UPDATE App; code[unique = true]       ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$isCc1687_CSFSA500    ; CM_$aaCcCSFSA5
                 ; AA2_$isCc1687_CSFSA7XX    ; CM_$aaCcCSS,CM_$aaCcCSK
                 ; AA2_$isCc1687_DCICRI      ; CM_$aaCcDCICRI
                 ; AA2_$isCc1687_DCICRIN     ; CM_$aaCcDCICRIN
                 ; AA2_$isCc1987_ESIADV      ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe
                 ; AA2_$isCc1987_ESIREPCAT   ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE
                 ; AA2_$isCc1987_ESIREPD     ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE,CM_$aaCcK3
                 ; AA2_$isCc1987_ESIREPE     ; CM_$aaCcA,CM_$aaCcE,CM_$aaCcK2
                 ; AA2_$isCc1987_ESIDIAG     ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB
                 ; AA2_$isCc1987_ESIMASTER   ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe,CM_$aaCcM,CM_$aaCcP
                 ; AA2_$isCc1987_TRKOHW1     ; CM_$aaCcETOHW1
                 ; AA2_$isCc1987_TRKOHW2     ; CM_$aaCcETOHW2
                 ; AA2_$isCc1987_TRKTRUCK    ; CM_$aaCcETruck
                 ; AA2_$isCc1987_TSTINFOAW   ; CM_$aaCcEW
                 ; AA2_$isCc1687_TSTINFODAT  ; CM_$aaCcTVPMCP
                 ; AA2_$isCc1987_TRKUPG      ; CM_$aaCcTRKUPG
                 ; AA2_$isCc1987_THLPKW      ; CM_$aaCcPKWTHL
                 ; AA2_$isCc1687_CORE_ESIPKG ; CM_$aaCcCoReESIPKG
                 ; AA2_$isCc1987_KTS250SD    ; CM_$aaCcKTS250ECUD
                 ; AA2_$isCc1987_TRKOHW3     ; CM_$aaCcETOHW3
                 ; AA2_$isCc1687_CRR950      ; CM_$aaCcCRR9501LDIRS


INSERT_UPDATE App; code[unique = true]       ; $supercategories; $catalogVersion[unique = true];
                 ; AA2_$isCc1687_CSFSA500    ; cat_201
                 ; AA2_$isCc1687_CSFSA7XX    ; cat_201
                 ; AA2_$isCc1687_DCICRI      ; cat_401
                 ; AA2_$isCc1687_DCICRIN     ; cat_401
                 ; AA2_$isCc1987_ESIADV      ; cat_10101
                 ; AA2_$isCc1987_ESIREPCAT   ; cat_1010101
                 ; AA2_$isCc1987_ESIREPD     ; cat_1010101
                 ; AA2_$isCc1987_ESIREPE     ; cat_1010101
                 ; AA2_$isCc1987_ESIDIAG     ; cat_10101
                 ; AA2_$isCc1987_ESIMASTER   ; cat_10101
                 ; AA2_$isCc1987_TRKOHW1     ; cat_10102
                 ; AA2_$isCc1987_TRKOHW2     ; cat_10102
                 ; AA2_$isCc1987_TRKTRUCK    ; cat_10102
                 ; AA2_$isCc1987_TSTINFOAW   ; cat_40101
                 ; AA2_$isCc1687_TSTINFODAT  ; cat_40101
                 ; AA2_$isCc1987_TRKUPG      ; cat_10102
                 ; AA2_$isCc1987_THLPKW      ; cat_1010102
                 ; AA2_$isCc1687_CORE_ESIPKG ; cat_1010103
                 ; AA2_$isCc1987_KTS250SD    ; cat_1010104
                 ; AA2_$isCc1987_TRKOHW3     ; cat_10102
                 ; AA2_$isCc1687_CRR950      ; cat_401
                 ; AA2_$isCc1987_ADAS_ONE    ; cat_501         ;

INSERT_UPDATE App; code[unique = true]       ; eulaContainers(code); $catalogVersion[unique = true]
                 ; AA2_$isCc1687_DCICRI      ; AA2_DCICRI
                 ; AA2_$isCc1687_DCICRIN     ; AA2_DCICRIN
                 ; AA2_$isCc1687_TSTINFODAT  ; AA2_ESItronic
                 ; AA2_$isCc1987_TSTINFOAW   ; AA2_ESItronic
                 ; AA2_$isCc1687_CORE_ESIPKG ; AA2_CoRe
                 ; AA2_$isCc1687_CSFSA500    ; AA2_FSA
                 ; AA2_$isCc1687_CSFSA7XX    ; AA2_FSA
                 ; AA2_$isCc1987_KTS250SD    ; AA2_ESItronic
                 ; AA2_$isCc1687_CRR950      ; AA2_CRR
