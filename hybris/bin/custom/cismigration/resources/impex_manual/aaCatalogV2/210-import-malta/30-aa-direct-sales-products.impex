#% impex.setLocale( Locale.ENGLISH );

# numerical code for MT, used as prefix for product code
$mtCc = 356
$aaCc = 040
$aaPackageName = com.sast.aa.mt.

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf

# please make sure, to use right DELEVELOPER COMPANY for Malta in the right environment.
# live
$companyId = f90f9664-7871-4161-bbb2-cc2f5abc8a56
# demo
# $companyId = b5172175-79aa-47c4-af18-cfe459264f84
# dev
# $companyId = 5a36e24c-93f0-47bd-9c9a-98208811f5bc
# $companyId =

$compacFsa500Description_en = The CompacSoft[plus] for FSA 500 is equipped with preset component tests and can be connected to existing systems, as well as used to gradually expand your workshop test system.
$compacFsa7xxDescription_en = With CompacSoft[plus] for FSA 7xx, the comfort for all measuring tasks on the vehicle is further increased by the menu-guided test steps, the optional vehicle-specific set values, as well as the display of the actual values.
$criDescription_en = CRI for DCI 700 software provides up-to-date data, ensures smooth processes and includes testing of piezo injectors for common rail systems.
$crinDescription_en = CRIN for DCI 700 software provides up-to-date data, ensures smooth processes and includes the testing of solenoid valve injectors for common rail systems.
$packDiagnosticDescription_en = The entry into professional diagnosis, repair and maintenance. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.
$packAdvancedDescription_en = The next level of professional workshop equipment. In addition to the Diagnostic package, instructions and manuals are included. Connected Repair provides service and maintenance history and repair information.
$packMasterDescription_en = The fully comprehensive package for professional vehicle diagnostics. It provides all the necessary information for diagnosis, repair, maintenance, spare parts, documentation and data management. Technical Support assists you in finding solutions.
$packComponentCatDescription_en = The spare parts catalog package includes the applications, functions and automotive equipment as well as the diesel spare parts and electrical spare parts incl. archive and electrical spare part ESI[tronic]-F.
$packComponentRepairDieselDescription_en = The Repair Diesel package provides information on spare parts and repair of diesel and electrical components. It allows identification of the vehicle, Bosch automotive equipment and includes repair instructions & service information.
$packComponentRepairElectricDescription_en = The increasing number of vehicle models makes it difficult for workshops to have up-to-date information on vehicle electrical systems at their fingertips. The Repair Electrics Package provides support with spare parts data on car electrics in a clear format.
$ohw1Description_en = The Agricultural Machinery Diagnostic Package provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw2Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw3Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$alltrucksDescription_en = The Alltrucks software contains important information about commercial vehicles such as model series, performance, engine identification as well as axle configuration. Includes ESI[tronic] Truck, NEO | orange from Knorr-Bremse and ZF-TESTMAN from ZF.
$packTruckDescription_en = The Truck package supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.
$infoartWDescription_en = The ESI[tronic] 2.0 Infoart W contains information about diesel test values for in-line pump combinations as well as for VE pumps, the complete test process test procedure from the determination of the measured values to the printout of the report and the display of the test steps in the optimum sequence.
$thlPkwDescription_en = Do you need technical support to maintain or repair a car, or simply a reliable second opinion? Then contact our support team and get fast and sound solutions.
$thlTruckDescription_en = Do you need technical support to maintain or repair a truck, or just a reliable second opinion? Then contact our support team and get fast and sound solutions.
$thlPkwTruckDescription_en = With extensive coverage of makes, models and systems, the support team can provide technical assistance and documentation for various light and heavy commercial vehicle repairs.
$infoartTestdataDescription_en = The ESI[tronic] 2.0-Infotype Testdata (CD) contains test values for Bosch Common Rail high pressure pumps, Common Rail injectors and VP 29 / 30 / 44 distributor injection pumps.
$truckUpgradeDescription_en = The Truck Upgrade package is dedicated to ESI[tronic]tronic Car users and supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.
$coReDescription_en = Bosch Connected Repair is software that connects workshop equipment, vehicle and repair data. Whether in the event of malfunctions or the storage of data and images in accordance with the Basic Data Protection Regulation - CoRe has been adapted to the needs of customers.
$kts250SDDescription_en = The entry into professional diagnosis, repair and maintenance specifically for KTS 250. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.

# TODO: UPDATE TRANSLATION
$adasDescription = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.
$adasDescription_en = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.

$alltrucksSubsS1BrimName = Alltrucks Diagnose-Paket
$compacFsa500SubsBrimName = CompacSoft[plus] FSA 500
$compacFsa7xxSubsBrimName = CompacSoft[plus] FSA 7xx
$criSubsS1BrimName = Component DCI-CRI
$crinSubsS1BrimName = Component DCI-CRIN
$packAdvancedSubsS1BrimName = ESI 2.0 Advanced Unlimited
$packAdvancedSubsM3BrimName = ESI 2.0 Advanced Unlimited Multi
$packAdvancedFull3YS1BrimName = ESI 2.0 Advanced 3 years
$packAdvancedFull3YM3BrimName = ESI 2.0 Advanced 3 years Multi
$packComponentCatSubsS1BrimName = ESI 2.0 ComponentCatalog D+E Unlimited
$packComponentCatSubsM3BrimName = ESI 2.0 ComponentCatalog D+E Unlim Multi
$packComponentCatFull3YS1BrimName = ESI 2.0 ComponentCatalog D+E 3 years
$packComponentCatFull3YM3BrimName = ESI 2.0 ComponentCatalog D+E 3y Multi
$packComponentRepairDieselSubsS1BrimName = ESI 2.0 ComponentRepair D+E Unlimited
$packComponentRepairDieselSubsM3BrimName = ESI 2.0 ComponentRepair D+E Unlim Multi
$packComponentRepairDieselFull3YS1BrimName = ESI 2.0 ComponentRepair D+E 3 years
$packComponentRepairDieselFull3YM3BrimName = ESI 2.0 ComponentRepair D+E 3y Multi
$packComponentRepairElectricSubsS1BrimName = ESI 2.0 ComponentRepair E Unlimited
$packComponentRepairElectricSubsM3BrimName = ESI 2.0 ComponentRepair E Unlim Multi
$packComponentRepairElectricFull3YS1BrimName = ESI 2.0 ComponentRepair E 3 years
$packComponentRepairElectricFull3YM3BrimName = ESI 2.0 ComponentRepair E 3 years Multi
$packDiagnosticSubsS1BrimName = ESI 2.0 Diagnostic Unlimited
$packDiagnosticSubsM3BrimName = ESI 2.0 Diagnostic Unlimited Multi
$packDiagnosticFull3YS1BrimName = ESI 2.0 Diagnostic 3 years
$packDiagnosticFull3YM3BrimName = ESI 2.0 Diagnostic 3 years Multi
$packDiagnosticFullOneTimeBrimName = ESI[tronic] 2.0 SD (OTP one time purchase)
$packMasterSubsS1BrimName = ESI 2.0 Master Unlimited
$packMasterSubsM3BrimName = ESI 2.0 Master Unlimited Multi
$packMasterFull3YS1BrimName = ESI 2.0 Master 3 years
$packMasterFull3YM3BrimName = ESI 2.0 Master 3 years Multi
$ohw1SubsS1BrimName = ESI[tronic] 2.0 Truck OHW I
$ohw1SubsM3BrimName = ESI[tronic] 2.0 Truck OHW I Multi
$ohw1Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW I (3 years)
$ohw1Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW I (3y) Multi
$ohw2SubsS1BrimName = ESI[tronic] 2.0 Truck OHW II
$ohw2SubsM3BrimName = ESI[tronic] 2.0 Truck OHW II Multi
$ohw2Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW II (3 years)
$ohw2Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW II (3y) Multi
$ohw3S1BrimName = ESI[tronic] 2.0 Truck OHW III Unlimited
$ohw3M3BrimName = ESI[tronic] 2.0 Truck OHW III Unlimited Multi
$ohw3Single3YearBrimName = ESI[tronic] Truck OHW III (3y)
$ohw3Multi3YearBrimName = ESI[tronic] Truck OHW III (3y) Multi.
$packTruckSubsS1BrimName = ESI[tronic] 2.0 Truck
$packTruckSubsM3BrimName = ESI[tronic] 2.0 Truck Multi
$packTruckFull3YS1BrimName = ESI[tronic] 2.0 Truck (3 years)
$packTruckFull3YM3BrimName = ESI[tronic] 2.0 Truck (3 years) Multi
$packTruckFullBrimName = ESI[tronic] 2.0 Truck (Einmalkauf)
$infoartWSubsBrimName = ESI[tronic] W Diesel Test Data (WP)
$infoartWFull3YBrimName = ESI[tronic] W (3 Jahre)
$thlPkwSubsBrimName = THL use technical hotline
$thlPkwFull3YBrimName = Technische Hotline LKW (3 Jahre)
$thlPkwTrkSubsBrimName = Technische Hotline fuer LKWs und PKWs
$infoartTestdataSubsBrimName = Testdata VP-M/CP
$coreSubsS1BrimName = CoRe for ESI 2.0 packages
$kts250SDFullBrimName = KTS 250 SD (OTP one time purchase)
$kts250SDSubsBrimName = KTS 250 SD ECU Diagnosis
$crr950DieselInjectorsRepairSoftDescription = CRR 950 Diesel-Injektor-Reparatur-Software
$crr950DieselInjectorsRepairSoftDescription_en = The CRR 950 guides and support the professional repairs of magnetic solenoid common-rail injectors from Bosch
$crr950DieselInjectorsRepairSoftwareBrimName1L = CRR 950 Diesel Injectors (1 License)
$crr950DieselInjectorsRepairSoftwareBrimName3L = CRR 950 Diesel Injectors (3 Licenses)
$compacFsa7xxFull3YBrimName = CompacSoft[plus] FSA 7xx (3 years)
$criSubsM3BrimName = Component DCI-CRI_Multi-users_3
$crinSubsM3BrimName = Component DCI-CRIN_Multi-users_3
$adasSubsS1BrimName = ADAS ONE - DATA
$adasFull3YS1BrimName = ADAS ONE - DATA (3 years)
$fsa7xxMulti = CompacSoft[plus] FSA 7xx Multi
$fsa7xx3YearsMulti = CompacSoft[plus] FSA 7xx (3 years) Multi
$fsa5xxMulti = CompacSoft[plus] FSA 500 Multi
$fsa5xx3YearsMulti = CompacSoft[plus] FSA 500 (3 years)Multi
$countryRestricted = true
$enabledIn = MT


INSERT_UPDATE App; code[unique = true]       ; packageName                    ; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$mtCc1987_ALLTRUCKS   ; $aaPackageName1987_ALLTRUCKS   ;
                 ; AA2_$mtCc1687_CSFSA500    ; $aaPackageName1687_CSFSA500    ;
                 ; AA2_$mtCc1687_CSFSA7XX    ; $aaPackageName1687_CSFSA7XX    ;
                 ; AA2_$mtCc1687_DCICRI      ; $aaPackageName1687_DCICRI      ;
                 ; AA2_$mtCc1687_DCICRIN     ; $aaPackageName1687_DCICRIN     ;
                 ; AA2_$mtCc1987_ESIADV      ; $aaPackageName1987_ESIADV      ;
                 ; AA2_$mtCc1987_ESIREPCAT   ; $aaPackageName1987_ESIREPCAT   ;
                 ; AA2_$mtCc1987_ESIREPD     ; $aaPackageName1987_ESIREPD     ;
                 ; AA2_$mtCc1987_ESIREPE     ; $aaPackageName1987_ESIREPE     ;
                 ; AA2_$mtCc1987_ESIDIAG     ; $aaPackageName1987_ESIDIAG     ;
                 ; AA2_$mtCc1987_ESIMASTER   ; $aaPackageName1987_ESIMASTER   ;
                 ; AA2_$mtCc1987_TRKOHW1     ; $aaPackageName1987_TRKOHW1     ;
                 ; AA2_$mtCc1987_TRKOHW2     ; $aaPackageName1987_TRKOHW2     ;
                 ; AA2_$mtCc1987_TRKTRUCK    ; $aaPackageName1987_TRKTRUCK    ;
                 ; AA2_$mtCc1987_TSTINFOAW   ; $aaPackageName1987_TSTINFOAW   ;
                 ; AA2_$mtCc1987_THLPKW      ; $aaPackageName1987_THLPKW      ;
                 ; AA2_$mtCc1687_TSTINFODAT  ; $aaPackageName1687_TSTINFODAT  ;
                 ; AA2_$mtCc1687_CORE_ESIPKG ; $aaPackageName1687_CORE_ESIPKG ;
                 ; AA2_$mtCc1987_KTS250SD    ; $aaPackageName1987_KTS250      ;
                 ; AA2_$mtCc1987_TRKOHW3     ; $aaPackageName1987_TRKOHW3     ;
                 ; AA2_$mtCc1687_CRR950      ; $aaPackageName1687_CRR950      ;
                 ; AA2_$mtCc1987_ADAS_ONE    ; $aaPackageName1987_ADAS_ONE    ;

INSERT_UPDATE App; code[unique = true]       ; name[lang = en]                          ; summary[lang = en]                             ; description[lang = en]                         ; $catalogVersion[unique = true];
                 ; AA2_$mtCc1987_ALLTRUCKS   ; Alltrucks Diagnosis                      ; $alltrucksDescription_en                       ; $alltrucksDescription_en                       ;
                 ; AA2_$mtCc1687_CSFSA500    ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description_en                    ; $compacFsa500Description_en                    ;
                 ; AA2_$mtCc1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription_en                    ; $compacFsa7xxDescription_en                    ;
                 ; AA2_$mtCc1687_DCICRI      ; Component DCI-CRI                        ; $criDescription_en                             ; $criDescription_en                             ;
                 ; AA2_$mtCc1687_DCICRIN     ; Component DCI-CRIN                       ; $crinDescription_en                            ; $crinDescription_en                            ;
                 ; AA2_$mtCc1987_ESIADV      ; ESI[tronic] 2.0 Advanced                 ; $packAdvancedDescription_en                    ; $packAdvancedDescription_en                    ;
                 ; AA2_$mtCc1987_ESIREPCAT   ; ESI[tronic] 2.0 ComponentCat D+E         ; $packComponentCatDescription_en                ; $packComponentCatDescription_en                ;
                 ; AA2_$mtCc1987_ESIREPD     ; ESI[tronic] 2.0 ComponentRepair D+E      ; $packComponentRepairDieselDescription_en       ; $packComponentRepairDieselDescription_en       ;
                 ; AA2_$mtCc1987_ESIREPE     ; ESI[tronic] 2.0 ComponentRepair E        ; $packComponentRepairElectricDescription_en     ; $packComponentRepairElectricDescription_en     ;
                 ; AA2_$mtCc1987_ESIDIAG     ; ESI[tronic] 2.0 Diagnostic               ; $packDiagnosticDescription_en                  ; $packDiagnosticDescription_en                  ;
                 ; AA2_$mtCc1987_ESIMASTER   ; ESI[tronic] 2.0 Master                   ; $packMasterDescription_en                      ; $packMasterDescription_en                      ;
                 ; AA2_$mtCc1987_TRKOHW1     ; ESI[tronic] 2.0 Truck OHW I              ; $ohw1Description_en                            ; $ohw1Description_en                            ;
                 ; AA2_$mtCc1987_TRKOHW2     ; ESI[tronic] 2.0 Truck OHW II             ; $ohw2Description_en                            ; $ohw2Description_en                            ;
                 ; AA2_$mtCc1987_TRKTRUCK    ; ESI[tronic] 2.0 Truck                    ; $packTruckDescription_en                       ; $packTruckDescription_en                       ;
                 ; AA2_$mtCc1987_TSTINFOAW   ; ESI[tronic] W                            ; $infoartWDescription_en                        ; $infoartWDescription_en                        ;
                 ; AA2_$mtCc1987_THLPKW      ; Technical Hotline Car                    ; $thlPkwDescription_en                          ; $thlPkwDescription_en                          ;
                 ; AA2_$mtCc1687_TSTINFODAT  ; Testdata VP-M/CP                         ; $infoartTestdataDescription_en                 ; $infoartTestdataDescription_en                 ;
                 ; AA2_$mtCc1687_CORE_ESIPKG ; CoRe Server + Online                     ; $coReDescription_en                            ; $coReDescription_en                            ;
                 ; AA2_$mtCc1987_KTS250SD    ; KTS 250 SD ECU Diagnosis                 ; $kts250SDDescription_en                        ; $kts250SDDescription_en                        ;
                 ; AA2_$mtCc1987_TRKOHW3     ; ESI[tronic] 2.0 Truck OHW III            ; $ohw3Description_en                            ; $ohw3Description_en                            ;
                 ; AA2_$mtCc1687_CRR950      ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription_en ; $crr950DieselInjectorsRepairSoftDescription_en ;
                 ; AA2_$mtCc1987_ADAS_ONE    ; ADAS One Solution                        ; $adasDescription_en                            ; $adasDescription_en                            ;

INSERT_UPDATE ProductContainer; code[unique = true]        ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$mtCc1987_ALLTRUCKS   ; AA2_$mtCc1987_ALLTRUCKS
                              ; pcaa_$mtCc1687_CSFSA500    ; AA2_$mtCc1687_CSFSA500
                              ; pcaa_$mtCc1687_CSFSA7XX    ; AA2_$mtCc1687_CSFSA7XX
                              ; pcaa_$mtCc1687_DCICRI      ; AA2_$mtCc1687_DCICRI
                              ; pcaa_$mtCc1687_DCICRIN     ; AA2_$mtCc1687_DCICRIN
                              ; pcaa_$mtCc1987_ESIADV      ; AA2_$mtCc1987_ESIADV
                              ; pcaa_$mtCc1987_ESIREPCAT   ; AA2_$mtCc1987_ESIREPCAT
                              ; pcaa_$mtCc1987_ESIREPD     ; AA2_$mtCc1987_ESIREPD
                              ; pcaa_$mtCc1987_ESIREPE     ; AA2_$mtCc1987_ESIREPE
                              ; pcaa_$mtCc1987_ESIDIAG     ; AA2_$mtCc1987_ESIDIAG
                              ; pcaa_$mtCc1987_ESIMASTER   ; AA2_$mtCc1987_ESIMASTER
                              ; pcaa_$mtCc1987_TRKOHW1     ; AA2_$mtCc1987_TRKOHW1
                              ; pcaa_$mtCc1987_TRKOHW2     ; AA2_$mtCc1987_TRKOHW2
                              ; pcaa_$mtCc1987_TRKTRUCK    ; AA2_$mtCc1987_TRKTRUCK
                              ; pcaa_$mtCc1987_TSTINFOAW   ; AA2_$mtCc1987_TSTINFOAW
                              ; pcaa_$mtCc1987_THLPKW      ; AA2_$mtCc1987_THLPKW
                              ; pcaa_$mtCc1687_TSTINFODAT  ; AA2_$mtCc1687_TSTINFODAT
                              ; pcaa_$mtCc1687_CORE_ESIPKG ; AA2_$mtCc1687_CORE_ESIPKG
                              ; pcaa_$mtCc1987_KTS250SD    ; AA2_$mtCc1987_KTS250SD
                              ; pcaa_$mtCc1987_TRKOHW3     ; AA2_$mtCc1987_TRKOHW3
                              ; pcaa_$mtCc1687_CRR950      ; AA2_$mtCc1687_CRR950
                              ; pcaa_$mtCc1987_ADAS_ONE    ; AA2_$mtCc1987_ADAS_ONE

INSERT_UPDATE AppLicense; code[unique = true] ; $baseProduct              ; sellerProductId; brimName[lang = en]                            ; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); specifiedPrice; billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; enabledCountries(isocode)[default = $enabledIn]; availabilityStatus(code)[default = PUBLISHED]
                        ; AA2_$mtCc1987P12760 ; AA2_$mtCc1987_ALLTRUCKS   ; 1987P12760     ; $alltrucksSubsS1BrimName                       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 2385.00       ;
                        ; AA2_$mtCc1687P15060 ; AA2_$mtCc1687_CSFSA500    ; 1687P15060     ; $compacFsa500SubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 2.00          ;
                        ; AA2_$mtCc1687P15045 ; AA2_$mtCc1687_CSFSA7XX    ; 1687P15045     ; $compacFsa7xxSubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 340.00        ;
                        ; AA2_$mtCc1687P15090 ; AA2_$mtCc1687_DCICRI      ; 1687P15090     ; $criSubsS1BrimName                             ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 300.00        ;
                        ; AA2_$mtCc1687P15100 ; AA2_$mtCc1687_DCICRIN     ; 1687P15100     ; $crinSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 300.00        ;
                        ; AA2_$mtCc1987P12843 ; AA2_$mtCc1987_ESIADV      ; 1987P12843     ; $packAdvancedFull3YS1BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3755.00       ;
                        ; AA2_$mtCc1987P12840 ; AA2_$mtCc1987_ESIADV      ; 1987P12840     ; $packAdvancedSubsS1BrimName                    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1470.00       ;
                        ; AA2_$mtCc1987P12846 ; AA2_$mtCc1987_ESIADV      ; 1987P12846     ; $packAdvancedFull3YM3BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 4575.00       ;
                        ; AA2_$mtCc1987P12847 ; AA2_$mtCc1987_ESIADV      ; 1987P12847     ; $packAdvancedSubsM3BrimName                    ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1790.00       ;
                        ; AA2_$mtCc1987P12988 ; AA2_$mtCc1987_ESIREPCAT   ; 1987P12988     ; $packComponentCatFull3YS1BrimName              ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 912.50        ;
                        ; AA2_$mtCc1987P12998 ; AA2_$mtCc1987_ESIREPCAT   ; 1987P12998     ; $packComponentCatSubsS1BrimName                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 365.00        ;
                        ; AA2_$mtCc1987P12783 ; AA2_$mtCc1987_ESIREPCAT   ; 1987P12783     ; $packComponentCatFull3YM3BrimName              ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1062.50       ;
                        ; AA2_$mtCc1987P12784 ; AA2_$mtCc1987_ESIREPCAT   ; 1987P12784     ; $packComponentCatSubsM3BrimName                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 425.00        ;
                        ; AA2_$mtCc1987P12973 ; AA2_$mtCc1987_ESIREPD     ; 1987P12973     ; $packComponentRepairDieselFull3YS1BrimName     ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1900.00       ;
                        ; AA2_$mtCc1987P12970 ; AA2_$mtCc1987_ESIREPD     ; 1987P12970     ; $packComponentRepairDieselSubsS1BrimName       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 760.00        ;
                        ; AA2_$mtCc1987P12296 ; AA2_$mtCc1987_ESIREPD     ; 1987P12296     ; $packComponentRepairDieselFull3YM3BrimName     ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 2500.00       ;
                        ; AA2_$mtCc1987P12297 ; AA2_$mtCc1987_ESIREPD     ; 1987P12297     ; $packComponentRepairDieselSubsM3BrimName       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1000.00       ;
                        ; AA2_$mtCc1987P12993 ; AA2_$mtCc1987_ESIREPE     ; 1987P12993     ; $packComponentRepairElectricFull3YS1BrimName   ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 825.00        ;
                        ; AA2_$mtCc1987P12990 ; AA2_$mtCc1987_ESIREPE     ; 1987P12990     ; $packComponentRepairElectricSubsS1BrimName     ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 330.00        ;
                        ; AA2_$mtCc1987P12294 ; AA2_$mtCc1987_ESIREPE     ; 1987P12294     ; $packComponentRepairElectricFull3YM3BrimName   ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1125.00       ;
                        ; AA2_$mtCc1987P12295 ; AA2_$mtCc1987_ESIREPE     ; 1987P12295     ; $packComponentRepairElectricSubsM3BrimName     ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 450.00        ;
                        ; AA2_$mtCc1987P12823 ; AA2_$mtCc1987_ESIDIAG     ; 1987P12823     ; $packDiagnosticFull3YS1BrimName                ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 2036.00       ;
                        ; AA2_$mtCc1987P12820 ; AA2_$mtCc1987_ESIDIAG     ; 1987P12820     ; $packDiagnosticSubsS1BrimName                  ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 795.00        ;
                        ; AA2_$mtCc1987P12822 ; AA2_$mtCc1987_ESIDIAG     ; 1987P12822     ; $packDiagnosticFull3YM3BrimName                ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 2856.00       ;
                        ; AA2_$mtCc1987P12824 ; AA2_$mtCc1987_ESIDIAG     ; 1987P12824     ; $packDiagnosticSubsM3BrimName                  ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1115.00       ;
                        ; AA2_$mtCc1987P12913 ; AA2_$mtCc1987_ESIMASTER   ; 1987P12913     ; $packMasterFull3YS1BrimName                    ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 4945.00       ;
                        ; AA2_$mtCc1987P12910 ; AA2_$mtCc1987_ESIMASTER   ; 1987P12910     ; $packMasterSubsS1BrimName                      ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1935.00       ;
                        ; AA2_$mtCc1987P12916 ; AA2_$mtCc1987_ESIMASTER   ; 1987P12916     ; $packMasterFull3YS1BrimName                    ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 5765.00       ;
                        ; AA2_$mtCc1987P12917 ; AA2_$mtCc1987_ESIMASTER   ; 1987P12917     ; $packMasterSubsM3BrimName                      ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 2255.00       ;
                        ; AA2_$mtCc1987P12263 ; AA2_$mtCc1987_TRKOHW1     ; 1987P12263     ; $ohw1Full3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1565.00       ;
                        ; AA2_$mtCc1987P12260 ; AA2_$mtCc1987_TRKOHW1     ; 1987P12260     ; $ohw1SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 625.00        ;
                        ; AA2_$mtCc1987P12262 ; AA2_$mtCc1987_TRKOHW1     ; 1987P12262     ; $ohw1SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 945.00        ;
                        ; AA2_$mtCc1987P12265 ; AA2_$mtCc1987_TRKOHW1     ; 1987P12265     ; $ohw1Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1885.00       ;
                        ; AA2_$mtCc1987P12280 ; AA2_$mtCc1987_TRKOHW2     ; 1987P12280     ; $ohw2Full3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3050.00       ;
                        ; AA2_$mtCc1987P12278 ; AA2_$mtCc1987_TRKOHW2     ; 1987P12278     ; $ohw2SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1220.00       ;
                        ; AA2_$mtCc1987P12275 ; AA2_$mtCc1987_TRKOHW2     ; 1987P12275     ; $ohw2SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1540.00       ;
                        ; AA2_$mtCc1987P12276 ; AA2_$mtCc1987_TRKOHW2     ; 1987P12276     ; $ohw2Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 3870.00       ;
                        ; AA2_$mtCc1987P12402 ; AA2_$mtCc1987_TRKTRUCK    ; 1987P12402     ; $packTruckFull3YS1BrimName                     ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3385.00       ;
                        ; AA2_$mtCc1987P12400 ; AA2_$mtCc1987_TRKTRUCK    ; 1987P12400     ; $packTruckSubsS1BrimName                       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1325.00       ;
                        ; AA2_$mtCc1987P12936 ; AA2_$mtCc1987_TRKTRUCK    ; 1987P12936     ; $packTruckSubsM3BrimName                       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1645.00       ;
                        ; AA2_$mtCc1987P12937 ; AA2_$mtCc1987_TRKTRUCK    ; 1987P12937     ; $packTruckFull3YM3BrimName                     ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1645.00       ;
                        ; AA2_$mtCc1987P12412 ; AA2_$mtCc1987_TRKTRUCK    ; 1987P12412     ; $packTruckFullBrimName                         ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 2320.00       ;
                        ; AA2_$mtCc1987P12503 ; AA2_$mtCc1987_TSTINFOAW   ; 1987P12503     ; $infoartWFull3YBrimName                        ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 1875.00       ;
                        ; AA2_$mtCc1987P12500 ; AA2_$mtCc1987_TSTINFOAW   ; 1987P12500     ; $infoartWSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; 750.00        ;
                        ; AA2_$mtCc1987P13523 ; AA2_$mtCc1987_THLPKW      ; 1987P13523     ; $thlPkwFull3YBrimName                          ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 825.00        ;
                        ; AA2_$mtCc1987729274 ; AA2_$mtCc1987_THLPKW      ; 1987729274     ; $thlPkwSubsBrimName                            ;                                          ; runtime_subs_unlimited ; <ignore>        ; 310.00        ;
                        ; AA2_$mtCc1687P15015 ; AA2_$mtCc1687_TSTINFODAT  ; 1687P15015     ; $infoartTestdataSubsBrimName                   ;                                          ; runtime_subs_unlimited ; <ignore>        ; 340.00        ;
                        ; AA2_$mtCc1687P15130 ; AA2_$mtCc1687_CORE_ESIPKG ; 1687P15130     ; $coreSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1.00          ;
                        ; AA2_$mtCc1987P12385 ; AA2_$mtCc1987_KTS250SD    ; 1987P12385     ; $kts250SDSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; 765.00        ;
                        ; AA2_$mtCc1987P12254 ; AA2_$mtCc1987_TRKOHW3     ; 1987P12254     ; $ohw3S1BrimName                                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 450           ;
                        ; AA2_$mtCc1987P12257 ; AA2_$mtCc1987_TRKOHW3     ; 1987P12257     ; $ohw3M3BrimName                                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 550           ;
                        ; AA2_$mtCc1987P12255 ; AA2_$mtCc1987_TRKOHW3     ; 1987P12255     ; $ohw3Single3YearBrimName                       ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 832.50        ;
                        ; AA2_$mtCc1987P12256 ; AA2_$mtCc1987_TRKOHW3     ; 1987P12256     ; $ohw3Multi3YearBrimName                        ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1102.50       ;
                        ; AA2_$mtCc1687P15137 ; AA2_$mtCc1687_CRR950      ; 1687P15137     ; $crr950DieselInjectorsRepairSoftwareBrimName1L ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 140           ;
                        ; AA2_$mtCc1687P15139 ; AA2_$mtCc1687_CRR950      ; 1687P15139     ; $crr950DieselInjectorsRepairSoftwareBrimName3L ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 210           ;
                        ; AA2_$mtCc1987P12515 ; AA2_$mtCc1987_ADAS_ONE    ; 1987P12515     ; $adasSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 0.01          ;
                        ; AA2_$mtCc1987P12517 ; AA2_$mtCc1987_ADAS_ONE    ; 1987P12517     ; $adasFull3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 0.01          ;
                        ; AA2_$mtCc1687P15048 ; AA2_$mtCc1687_CSFSA7XX    ; 1687P15048     ; $compacFsa7xxFull3YBrimName                    ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 850           ;
                        ; AA2_$mtCc1687P15102 ; AA2_$mtCc1687_DCICRI      ; 1687P15102     ; $criSubsM3BrimName                             ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 420           ;
                        ; AA2_$mtCc1687P15107 ; AA2_$mtCc1687_DCICRIN     ; 1687P15107     ; $crinSubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 420           ;
                        ; AA2_$mtCc1987P12389 ; AA2_$mtCc1987_KTS250SD    ; 1987P12389     ; $kts250SDFullBrimName                          ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 840.00        ;
                        ; AA2_$mtCc1687P15170 ; AA2_$mtCc1687_CSFSA500    ; 1687P15170     ; $fsa5xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$mtCc1687P15173 ; AA2_$mtCc1687_CSFSA500    ; 1687P15173     ; $fsa5xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;
                        ; AA2_$mtCc1687P15160 ; AA2_$mtCc1687_CSFSA7XX    ; 1687P15160     ; $fsa7xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$mtCc1687P15163 ; AA2_$mtCc1687_CSFSA7XX    ; 1687P15163     ; $fsa7xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;

# The licenses below were discontinued by MA and should be available only for the contract migration.
UPDATE AppLicense; code[unique = true] ; $catalogVersion[unique = true]; availabilityStatus(code)[default = UNPUBLISHED]
                 ; AA2_$mtCc1987P12389 ;
                 ; AA2_$mtCc1987P12412 ;

UPDATE AppLicense; code[unique = true] ; userGroups(uid); $catalogVersion[unique = true];
                 ; AA2_$mtCc1987P12760 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1687P15060 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1687P15045 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1687P15090 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1687P15100 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12843 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12840 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12846 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12847 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12988 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12998 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12783 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12784 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12973 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12970 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12296 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12297 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12993 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12990 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12294 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12295 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12823 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12820 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12822 ;                ;
                 ; AA2_$mtCc1987P12824 ;                ;
                 ; AA2_$mtCc1987P12913 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12910 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12916 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12917 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12263 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12260 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12262 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12265 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12280 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12278 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12275 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12276 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12402 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12400 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12936 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12937 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12412 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12503 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12500 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P13523 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987729274 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1687P15015 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1687P15130 ;                ;
                 ; AA2_$mtCc1987P12385 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12254 ; IDW000, WD0006 ;
                 ; AA2_$mtCc1987P12257 ; IDW000, WD0006 ;
                 ; AA2_$mtCc1987P12255 ; IDW000, WD0006 ;
                 ; AA2_$mtCc1987P12256 ; IDW000, WD0006 ;
                 ; AA2_$mtCc1687P15137 ; IDW000, WD0006 ;
                 ; AA2_$mtCc1687P15139 ; IDW000, WD0006 ;
# The group assignment for the ADAS ONE is most probably not final. we're missing the prices for now
                 ; AA2_$mtCc1987P12515 ; IDW000         ;
                 ; AA2_$mtCc1987P12517 ; IDW000         ;
                 ; AA2_$mtCc1687P15048 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1687P15102 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1687P15107 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1987P12389 ; IDW000,WD0006  ;
                 ; AA2_$mtCc1687P15163 ; IDW000         ;
                 ; AA2_$mtCc1687P15170 ; IDW000         ;
                 ; AA2_$mtCc1687P15160 ; IDW000         ;
                 ; AA2_$mtCc1687P15173 ; IDW000         ;


INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$mtCc1987P12760                          ; 2235.00
                      ; AA2_$mtCc1687P15060                          ; 2.00
                      ; AA2_$mtCc1687P15045                          ; 340.00
                      ; AA2_$mtCc1687P15090                          ; 300.00
                      ; AA2_$mtCc1687P15100                          ; 300.00
                      ; AA2_$mtCc1987P12843                          ; 3758.00
                      ; AA2_$mtCc1987P12840                          ; 1471.00
                      ; AA2_$mtCc1987P12846                          ; 4578.00
                      ; AA2_$mtCc1987P12847                          ; 1791.00
                      ; AA2_$mtCc1987P12988                          ; 912.50
                      ; AA2_$mtCc1987P12998                          ; 365.00
                      ; AA2_$mtCc1987P12783                          ; 1062.50
                      ; AA2_$mtCc1987P12784                          ; 425.00
                      ; AA2_$mtCc1987P12973                          ; 1900.00
                      ; AA2_$mtCc1987P12970                          ; 760.00
                      ; AA2_$mtCc1987P12296                          ; 2500.00
                      ; AA2_$mtCc1987P12297                          ; 1000.00
                      ; AA2_$mtCc1987P12993                          ; 825.00
                      ; AA2_$mtCc1987P12990                          ; 330.00
                      ; AA2_$mtCc1987P12294                          ; 1125.00
                      ; AA2_$mtCc1987P12295                          ; 450.00
                      ; AA2_$mtCc1987P12823                          ; 2036.00
                      ; AA2_$mtCc1987P12820                          ; 795.00
                      ; AA2_$mtCc1987P12913                          ; 4948.00
                      ; AA2_$mtCc1987P12910                          ; 1936.00
                      ; AA2_$mtCc1987P12916                          ; 5768.00
                      ; AA2_$mtCc1987P12917                          ; 2256.00
                      ; AA2_$mtCc1987P12263                          ; 1565.00
                      ; AA2_$mtCc1987P12260                          ; 625.00
                      ; AA2_$mtCc1987P12262                          ; 945.00
                      ; AA2_$mtCc1987P12265                          ; 1885.00
                      ; AA2_$mtCc1987P12280                          ; 3050.00
                      ; AA2_$mtCc1987P12278                          ; 1220.00
                      ; AA2_$mtCc1987P12275                          ; 1540.00
                      ; AA2_$mtCc1987P12276                          ; 3870.00
                      ; AA2_$mtCc1987P12402                          ; 3385.00
                      ; AA2_$mtCc1987P12400                          ; 1325.00
                      ; AA2_$mtCc1987P12936                          ; 1645.00
                      ; AA2_$mtCc1987P12937                          ; 1645.00
                      ; AA2_$mtCc1987P12412                          ; 2320.00
                      ; AA2_$mtCc1987P12503                          ; 1875.00
                      ; AA2_$mtCc1987P12500                          ; 750.00
                      ; AA2_$mtCc1987P13523                          ; 825.00
                      ; AA2_$mtCc1987729274                          ; 310.00
                      ; AA2_$mtCc1687P15015                          ; 340.00
                      ; AA2_$mtCc1987P12385                          ; 765.00
                      ; AA2_$mtCc1987P12254                          ; 450
                      ; AA2_$mtCc1987P12257                          ; 550
                      ; AA2_$mtCc1987P12255                          ; 1125
                      ; AA2_$mtCc1987P12256                          ; 1125
                      ; AA2_$mtCc1687P15137                          ; 140  ;
                      ; AA2_$mtCc1687P15139                          ; 210  ;
                      ; AA2_$mtCc1987P12515                          ; 0.01 ;
                      ; AA2_$mtCc1987P12517                          ; 0.01 ;
                      ; AA2_$mtCc1687P15048                          ; 850
                      ; AA2_$mtCc1687P15102                          ; 420
                      ; AA2_$mtCc1687P15107                          ; 420
                      ; AA2_$mtCc1987P12389                          ; 840
                      ; AA2_$mtCc1687P15163                          ; 0.01 ;
                      ; AA2_$mtCc1687P15170                          ; 0.01 ;
                      ; AA2_$mtCc1687P15160                          ; 0.01 ;
                      ; AA2_$mtCc1687P15173                          ; 0.01 ;


INSERT_UPDATE App; code[unique = true]     ; boms(code)                                                                                           ; $catalogVersion[unique = true]
                 ; AA2_$mtCc1987_ALLTRUCKS ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                  ;
                 ; AA2_$mtCc1987_ESIADV    ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe                       ;
                 ; AA2_$mtCc1987_ESIREPCAT ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE                                                                     ;
                 ; AA2_$mtCc1987_ESIREPD   ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE,MAT_$aaCcK3                                                         ;
                 ; AA2_$mtCc1987_ESIREPE   ; MAT_$aaCcA,MAT_$aaCcE,MAT_$aaCcK2                                                                    ;
                 ; AA2_$mtCc1987_ESIDIAG   ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB                                                  ;
                 ; AA2_$mtCc1987_ESIMASTER ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe,MAT_$aaCcM,MAT_$aaCcP ;
                 ; AA2_$mtCc1987_TRKOHW1   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                  ;
                 ; AA2_$mtCc1987_TRKOHW2   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                  ;
                 ; AA2_$mtCc1987_TRKTRUCK  ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                  ;
                 ; AA2_$mtCc1987_TRKOHW3   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                  ;


INSERT_UPDATE App; code[unique = true]       ; contentModules(code)                                                                         ; $catalogVersion[unique = true]
                 ; AA2_$mtCc1987_ALLTRUCKS   ; CM_$aaCcNEOOrangeATruck, CM_$aaCcETruck                                                      ;
                 ; AA2_$mtCc1687_CSFSA500    ; CM_$aaCcCSFSA5                                                                               ;
                 ; AA2_$mtCc1687_CSFSA7XX    ; CM_$aaCcCSS,CM_$aaCcCSK                                                                      ;
                 ; AA2_$mtCc1687_DCICRI      ; CM_$aaCcDCICRI                                                                               ;
                 ; AA2_$mtCc1687_DCICRIN     ; CM_$aaCcDCICRIN                                                                              ;
                 ; AA2_$mtCc1987_ESIADV      ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe                     ;
                 ; AA2_$mtCc1987_ESIREPCAT   ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE                                                                ;
                 ; AA2_$mtCc1987_ESIREPD     ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE,CM_$aaCcK3                                                     ;
                 ; AA2_$mtCc1987_ESIREPE     ; CM_$aaCcA,CM_$aaCcE,CM_$aaCcK2                                                               ;
                 ; AA2_$mtCc1987_ESIDIAG     ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB                                              ;
                 ; AA2_$mtCc1987_ESIMASTER   ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe,CM_$aaCcM,CM_$aaCcP ;
                 ; AA2_$mtCc1987_TRKOHW1     ; CM_$aaCcETOHW1                                                                               ;
                 ; AA2_$mtCc1987_TRKOHW2     ; CM_$aaCcETOHW2                                                                               ;
                 ; AA2_$mtCc1987_TRKTRUCK    ; CM_$aaCcETruck                                                                               ;
                 ; AA2_$mtCc1987_TSTINFOAW   ; CM_$aaCcEW                                                                                   ;
                 ; AA2_$mtCc1987_THLPKW      ; CM_$aaCcPKWTHL                                                                               ;
                 ; AA2_$mtCc1687_TSTINFODAT  ; CM_$aaCcTVPMCP                                                                               ;
                 ; AA2_$mtCc1687_CORE_ESIPKG ; CM_$aaCcCoReESIPKG                                                                           ;
                 ; AA2_$mtCc1987_KTS250SD    ; CM_$aaCcKTS250ECUD                                                                           ;
                 ; AA2_$mtCc1987_TRKOHW3     ; CM_$aaCcETOHW3                                                                               ;
                 ; AA2_$mtCc1687_CRR950      ; CM_$aaCcCRR950                                                                               ;

INSERT_UPDATE App; code[unique = true]       ; $supercategories; $catalogVersion[unique = true]
                 ; AA2_$mtCc1987_ALLTRUCKS   ; cat_1010201
                 ; AA2_$mtCc1687_CSFSA500    ; cat_201
                 ; AA2_$mtCc1687_CSFSA7XX    ; cat_201
                 ; AA2_$mtCc1687_DCICRI      ; cat_401
                 ; AA2_$mtCc1687_DCICRIN     ; cat_401
                 ; AA2_$mtCc1987_ESIADV      ; cat_10101
                 ; AA2_$mtCc1987_ESIREPCAT   ; cat_1010101
                 ; AA2_$mtCc1987_ESIREPD     ; cat_1010101
                 ; AA2_$mtCc1987_ESIREPE     ; cat_1010101
                 ; AA2_$mtCc1987_ESIDIAG     ; cat_10101
                 ; AA2_$mtCc1987_ESIMASTER   ; cat_10101
                 ; AA2_$mtCc1987_TRKOHW1     ; cat_10102
                 ; AA2_$mtCc1987_TRKOHW2     ; cat_10102
                 ; AA2_$mtCc1987_TRKTRUCK    ; cat_10102
                 ; AA2_$mtCc1987_TSTINFOAW   ; cat_40101
                 ; AA2_$mtCc1987_THLPKW      ; cat_1010102
                 ; AA2_$mtCc1687_TSTINFODAT  ; cat_40101
                 ; AA2_$mtCc1687_CORE_ESIPKG ; cat_1010103
                 ; AA2_$mtCc1987_KTS250SD    ; cat_1010104
                 ; AA2_$mtCc1987_TRKOHW3     ; cat_10102
                 ; AA2_$mtCc1987_ADAS_ONE    ; cat_501         ;



INSERT_UPDATE App; code[unique = true]       ; eulaContainers(code); $catalogVersion[unique = true]
                 ; AA2_$mtCc1987_TSTINFOAW   ; AA2_ESItronic
                 ; AA2_$mtCc1987_THLPKW      ; AA2_THL
                 ; AA2_$mtCc1687_DCICRI      ; AA2_DCICRI
                 ; AA2_$mtCc1687_DCICRIN     ; AA2_DCICRIN
                 ; AA2_$mtCc1687_TSTINFODAT  ; AA2_ESItronic
                 ; AA2_$mtCc1687_CSFSA500    ; AA2_FSA
                 ; AA2_$mtCc1687_CSFSA7XX    ; AA2_FSA
                 ; AA2_$mtCc1987_KTS250SD    ; AA2_ESItronic
                 ; AA2_$mtCc1687_CORE_ESIPKG ; AA2_CoRe
                 ; AA2_$mtCc1687_CRR950      ; AA2_CRR
