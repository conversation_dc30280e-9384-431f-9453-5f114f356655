#% impex.setLocale( Locale.ENGLISH );

# numerical code for DE, used as prefix for product code
$deCc = 049
$aaCc = 040
$aaPackageName = com.sast.aa.de.

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf

# please make sure, to use right DELEVELOPER COMPANY for Germany in the right environment.
# live
# $companyId =
# demo
# $companyId = 774ae2d7-2ca2-42f7-b7bd-eba0128a666f
# dev
# $companyId = c7cc0be2-8851-4947-b9b7-51a8bb1261fd
$companyId = 91ae83b9-302b-4da2-8b15-814675361228

$compacFsa500Description = Die CompacSoft[plus] für FSA 500 ist mit voreingestellten Komponententests ausgestattet und kann an vorhandene Systeme angeschlossen, sowie für den schrittweisen Ausbau Ihres Werkstatt-Testsystems genutzt werden.
$compacFsa500Description_en = The CompacSoft[plus] for FSA 500 is equipped with preset component tests and can be connected to existing systems, as well as used to gradually expand your workshop test system.
$compacFsa7xxDescription = Mit CompacSoft[plus] für FSA 7xx wird der Komfort für alle Messaufgaben am Fahrzeug durch die menügeführten Prüfschritte, den optionalen fahrzeugspezifischen Sollwerten, sowie der Anzeige der Ist-Werte noch weiter erhöht.
$compacFsa7xxDescription_en = With CompacSoft[plus] for FSA 7xx, the comfort for all measuring tasks on the vehicle is further increased by the menu-guided test steps, the optional vehicle-specific set values, as well as the display of the actual values.
$bea750Description = AU Software für BEA 750 nach Leitfaden 5.01 mit Fahrzeugsolldatenbank für Benzin-, Gas-, Hybrid- sowie Dieselfahrzeuge. Einfache Fahrzeug-identifizierung und intuitive Menüführung, Vollautomatische Prüfabläufe mit Schritt-für-Schritt Anleitung durch den Testprozess.
$bea750Description_en = AU software for BEA 750 according to guideline 5.01 with vehicle target database for gasoline, gas, hybrid, and diesel vehicles. Easy vehicle identification and intuitive menu navigation, fully automatic test procedures with step-by-step guidance through the test process.
$beaPcDescription = AU Software nach aktuellen gesetzlichen Vorgaben mit Fahrzeugsolldatenbank für Benzin-, Gas-, Hybrid- sowie Dieselfahrzeuge. Einfache Fahrzeug-identifizierung und intuitive Menüführung, Vollautomatische Prüfabläufe mit Schritt-für-Schritt Anleitung durch den Testprozess.
$beaPcDescription_en = AU software according to current legal requirements with a vehicle target database for gasoline, gas, hybrid, and diesel vehicles. Easy vehicle identification and intuitive menu navigation, fully automatic test procedures with step-by-step guidance through the test process.
$criDescription = CRI-Lizenz stellt den Anwender während der Vertragslaufzeit die DCI-Software und CRI-Datenbank zur Verfügung für den Betrieb eines DCI 200 oder DCI 700. Die Datenbank beinhaltet die aktuelle zur Verfügung Bosch-CRI-Injektoren für Pkws und kleine Transportern. Updates werden während der Vertragslaufzeit über das Internet zur Verfügung gestellt.
$criDescription_en = CRI for DCI 700 software provides up-to-date data, ensures smooth processes and includes testing of piezo injectors for common rail systems.
$crinDescription = CRIN-Lizenz stellt den Anwender während der Vertragslaufzeit die DCI-Software und CRIN-Datenbank zur Verfügung für den Betrieb eines DCI 200 oder DCI 700. Die Datenbank beinhaltet die aktuelle zur Verfügung Bosch-CRIN-Injektoren für Nutzfahrzeuge (Inklusiv CRIN 4.2 Injektoren). Updates werden während der Vertragslaufzeit über das Internet zur Verfügung gestellt.
$crinDescription_en = CRIN for DCI 700 software provides up-to-date data, ensures smooth processes and includes the testing of solenoid valve injectors for common rail systems.
$packDiagnosticDescription = Der Einstieg in die professionelle Diagnose, Reparatur und Wartung. Es ermöglicht eine kompetente elektrische Diagnose und bietet eine Vielzahl weiterer Funktionen für alle abgedeckten Fahrzeuge.
$packDiagnosticDescription_en = The entry into professional diagnosis, repair and maintenance. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.
$packAdvancedDescription = Die nächste Stufe der professionellen Werkstattausrüstung. Zusätzlich zum Paket Diagnostic sind Anleitungen und Handbücher enthalten. Connected Repair liefert die Service- und Wartungshistorie sowie Reparaturinformationen.
$packAdvancedDescription_en = The next level of professional workshop equipment. In addition to the Diagnostic package, instructions and manuals are included. Connected Repair provides service and maintenance history and repair information.
$packMasterDescription = Das vollumfängliche Paket für professionelle Fahrzeugdiagnose. Es liefert alle nötigen Informationen zur Diagnose, Reparatur, Wartung, Ersatzteile, Dokumentation und Datenmanagement. Der Technische Support unterstützt Sie bei der Lösungsfindung.
$packMasterDescription_en = The fully comprehensive package for professional vehicle diagnostics. It provides all the necessary information for diagnosis, repair, maintenance, spare parts, documentation and data management. Technical Support assists you in finding solutions.
$packComponentCatDescription = Das Ersatzteile-Katalog-Paket beinhaltet die Anwendungen, Funktionen und Kfz-Ausrüstung sowie die Diesel Ersatzteile und Elektrik Ersatzteile inkl. Archiv und Elektrik Ersatzteil ESI[tronic]-F.
$packComponentCatDescription_en = The spare parts catalog package includes the applications, functions and automotive equipment as well as the diesel spare parts and electrical spare parts incl. archive and electrical spare part ESI[tronic]-F.
$packComponentRepairDieselDescription = Das Repair Diesel-Paket bietet Informationen zu Ersatzteilen und zur Reparatur von Diesel- und Elektrik-Komponenten. Es ermöglicht die Identifikation des Fahrzeugs, der Bosch-Kfz-Ausrüstung und beinhaltet Reparaturanleitungen & Serviceinformationen.
$packComponentRepairDieselDescription_en = The Repair Diesel package provides information on spare parts and repair of diesel and electrical components. It allows identification of the vehicle, Bosch automotive equipment and includes repair instructions & service information.
$packComponentRepairElectricDescription = Der steigende Umfang an Fahrzeugmodellen erschwert es Werkstätten, laufend aktuelle Informationen für Aggregate der Fahrzeugelektrik parat zu haben. Das Repair Elektrik Paket unterstützt mit Ersatzteildaten zur Autoelektrik in übersichtlicher Form.
$packComponentRepairElectricDescription_en = The increasing number of vehicle models makes it difficult for workshops to have up-to-date information on vehicle electrical systems at their fingertips. The Repair Electrics Package provides support with spare parts data on car electrics in a clear format.
$ohw1Description = In dem Diagnose Paket Landmaschinen stehen Informationen zur Diagnose, Wartung und Reparatur von landwirtschaftlich genutzten Fahrzeugen zur Verfügung. Enthalten sind unter anderem Einstell- und Parametrierfunktionen an Hydrauliksystemen.
$ohw1Description_en = The Agricultural Machinery Diagnostic Package provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw2Description = In dem Diagnose Paket Baumaschinen und Motoren stehen Informationen zur Diagnose, Wartung und Reparatur von landwirtschaftlich genutzten Fahrzeugen zur Verfügung. Enthalten sind unter anderem Einstell- und Parametrierfunktionen an Hydrauliksystemen.
$ohw2Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw3Description = In dem Diagnose Paket Baumaschinen und Motoren stehen Informationen zur Diagnose, Wartung und Reparatur von landwirtschaftlich genutzten Fahrzeugen zur Verfügung. Enthalten sind unter anderem Einstell- und Parametrierfunktionen an Hydrauliksystemen.
$ohw3Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$alltrucksDescription = Die Alltrucks Diagnose-Software beinhaltet wichtige Informationen zu den Nutzfahrzeugen wie Modellreihe, Leistung, Motorkennzeichnung sowie Achskonfiguration. Beinhaltet ESI[tronic] Truck, NEO | orange von Knorr-Bremse.
$alltrucksDescription_en = The Alltrucks software contains important information about commercial vehicles such as model series, performance, engine identification as well as axle configuration. Includes ESI[tronic] Truck, NEO | orange from Knorr-Bremse.
$packTruckDescription = Das Paket Truck unterstützt Werkstätten bei der zuverlässigen Diagnose, kompletten Wartung und effizienten Reparatur von allen gängigen leichten und schweren Nutzfahrzeugen, Anhängern, Transportern und Bussen.
$packTruckDescription_en = The Truck package supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.
$infoartWDescription = Die ESI[tronic] 2.0-Infoart W beinhaltet Informationen zu Diesel Prüfwerten für Reihenpumpen-Kombinationen sowie für VE Pumpen, den kompletten Prüfvorgang von der Messwerteermittlung bis zum Protokollausdruck und die Anzeige der Prüfschritte in optimaler Reihenfolge.
$infoartWDescription_en = The ESI[tronic] 2.0 Infoart W contains information about diesel test values for in-line pump combinations as well as for VE pumps, the complete test process test procedure from the determination of the measured values to the printout of the report and the display of the test steps in the optimum sequence.
$thlPkwDescription = Sie benötigen technische Unterstützung beim Warten oder Reparieren eines Pkw oder einfach nur eine zuverlässige zweite Meinung? Dann wenden Sie sich an unser Support-Team und erhalten Sie schnelle und fundierte Lösungen.
$thlPkwDescription_en = Do you need technical support to maintain or repair a car, or simply a reliable second opinion? Then contact our support team and get fast and sound solutions.
$thlTruckDescription = Sie benötigen technische Unterstützung beim Warten oder Reparieren eines Trucks oder einfach nur eine zuverlässige zweite Meinung? Dann wenden Sie sich an unser Support-Team und erhalten Sie schnelle und fundierte Lösungen.
$thlTruckDescription_en = Do you need technical support to maintain or repair a truck, or just a reliable second opinion? Then contact our support team and get fast and sound solutions.
$thlPkwTruckDescription = Mit einer umfangreichen Abdeckung von Marken, Modellen und Systemen kann das Support-Team technische Unterstützung und Unterlagen für unterschiedliche Reparaturen an leichten und schweren Nutzfahrzeugen anbieten.
$thlPkwTruckDescription_en = With extensive coverage of makes, models and systems, the support team can provide technical assistance and documentation for various light and heavy commercial vehicle repairs.
$infoartTestdataDescription = Die ESI[tronic] 2.0-Infoart Testdata (CD) enthält Prüfwerte für Bosch Common Rail Hochdruckpumpen, Common Rail Injektoren und VP 29 / 30 / 44 Verteilereinspritzpumpen.
$infoartTestdataDescription_en = The ESI[tronic] 2.0-Infotype Testdata (CD) contains test values for Bosch Common Rail high pressure pumps, Common Rail injectors and VP 29 / 30 / 44 distributor injection pumps.
$truckUpgradeDescription = Das Paket Truck unterstützt Bestandskunden der ESI[tronic] Car Produkte bei der zuverlässigen Diagnose, kompletten Wartung und effizienten Reparatur von allen gängigen leichten und schweren Nutzfahrzeugen, Anhängern, Transportern und Bussen.
$truckUpgradeDescription_en = The Truck Upgrade package is dedicated to ESI[tronic]tronic Car users and supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.
$coReDescription = Bosch Connected Repair ist eine Software, die Werkstattausrüstung, Fahrzeug- und Reparaturdaten miteinander verbindet. Ob bei Störungen oder bei der Speicherung von Daten und Bildern gemäß der Datenschutzgrundverordnung - CoRe wurde an die Bedürfnisse der Kunden angepasst.
$coReDescription_en = Bosch Connected Repair is software that connects workshop equipment, vehicle and repair data. Whether in the event of malfunctions or the storage of data and images in accordance with the Basic Data Protection Regulation - CoRe has been adapted to the needs of customers.
$kts250SDDescription = Der Einstieg in die professionelle Diagnose, Reparatur und Wartung speziell für KTS 250. Es ermöglicht eine kompetente, vielseitige Diagnosetechnik und bietet zahlreiche weitere Funktionen für alle abgedeckten Fahrzeuge.
$kts250SDDescription_en = The entry into professional diagnosis, repair and maintenance specifically for KTS 250. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.
$crr950DieselInjectorsRepairSoftDescription = CRR 950 Diesel-Injektor-Reparatur-Software
$crr950DieselInjectorsRepairSoftDescription_en = The CRR 950 guides and support the professional repairs of magnetic solenoid common-rail injectors from Bosch

# TODO: UPDATE TRANSLATION
$adasDescription = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.
$adasDescription_sv = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.
$adasDescription_en = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.

$alltrucksSubsS1BrimName = Alltrucks Diagnose-Paket
$alltrucksSubsM3BrimName = ESI[tronic] 2.0 All Trucks Unlim Multi
$bea750FullBrimName = AU Daten + SystemSoft for BEA750 DE OTP
$bea750SubsBrimName = AU Daten + SystemSoft for BEA750 DE ABO
$beaPcFullBrimName = AU Daten + SystemSoft for BEA PC DE OTP
$beaPcSubsBrimName = AU Daten + SystemSoft for BEA PC DE ABO
$compacFsa500SubsBrimName = CompacSoft[plus] FSA 500
$compacFsa7xxSubsBrimName = CompacSoft[plus] FSA 7xx
$criSubsS1BrimName = Component DCI-CRI
$criSubsM3BrimName = Component DCI-CRI_Multi-users_3
$crinSubsS1BrimName = Component DCI-CRIN
$crinSubsM3BrimName = Component DCI-CRIN_Multi-users_3
$packAdvancedSubsS1BrimName = ESI 2.0 Advanced Unlimited
$packAdvancedSubsM3BrimName = ESI 2.0 Advanced Unlimited Multi
$packComponentCatSubsS1BrimName = ESI 2.0 ComponentCatalog D+E Unlimited
$packComponentCatSubsM3BrimName = ESI 2.0 ComponentCatalog D+E Unlim Multi
$packComponentRepairDieselSubsS1BrimName = ESI 2.0 ComponentRepair D+E Unlimited
$packComponentRepairDieselSubsM3BrimName = ESI 2.0 ComponentRepair D+E Unlim Multi
$packComponentRepairElectricSubsS1BrimName = ESI 2.0 ComponentRepair E Unlimited
$packComponentRepairElectricSubsM3BrimName = ESI 2.0 ComponentRepair E Unlim Multi
$packDiagnosticSubsS1BrimName = ESI 2.0 Diagnostic Unlimited
$packDiagnosticSubsM3BrimName = ESI 2.0 Diagnostic Unlimited Multi
$packDiagnosticFullOneTimeBrimName = ESI[tronic] 2.0 SD (OTP one time purchase)
$packMasterSubsS1BrimName = ESI 2.0 Master Unlimited
$packMasterSubsM3BrimName = ESI 2.0 Master Unlimited Multi
$ohw1SubsS1BrimName = ESI[tronic] 2.0 Truck OHW I
$ohw1SubsM3BrimName = ESI[tronic] 2.0 Truck OHW I Multi
$ohw2SubsS1BrimName = ESI[tronic] 2.0 Truck OHW II
$ohw2SubsM3BrimName = ESI[tronic] 2.0 Truck OHW II Multi


$ohw3SubsS1BrimName = ESI[tronic] 2.0 Truck OHW III Unlimited
$ohw3SubsM3BrimName = ESI[tronic] 2.0 Truck OHW III Unlimited Multi

$packTruckSubsS1BrimName = ESI[tronic] 2.0 Truck
$packTruckSubsM3BrimName = ESI[tronic] 2.0 Truck Multi
$packTruckFullBrimName = ESI[tronic] 2.0 Truck (Einmalkauf)
$infoartWSubsBrimName = ESI[tronic] W Diesel Test Data (WP)
$thlPkwSubsBrimName = THL use technical hotline
$thlTrkSubsBrimName = Technische Hotline ESI[tronic] for Truck
$thlPkwTrkSubsBrimName = Technische Hotline fuer LKWs und PKWs
$infoartTestdataSubsBrimName = Testdata VP-M/CP
$truckUpgradeSubsS1BrimName = ESI[tronic] 2.0 Truck Upgrade
$truckUpgradeSubsM3BrimName = ESI[tronic] 2.0 Truck Upgrade Multi
$coreSubsS1BrimName = CoRe for ESI 2.0 packages
$kts250SDFullBrimName = KTS 250 SD (OTP one time purchase)
$kts250SDSubsBrimName = KTS 250 SD ECU Diagnosis

$crr950DieselInjectorsRepairSoftwareBrimName1L = CRR 950 Diesel Injectors (1 License)
$crr950DieselInjectorsRepairSoftwareBrimName3L = CRR 950 Diesel Injectors (3 Licenses)
$fsa7xxMulti = CompacSoft[plus] FSA 7xx Multi
$fsa7xx3YearsMulti = CompacSoft[plus] FSA 7xx (3 years) Multi
$fsa5xxMulti = CompacSoft[plus] FSA 500 Multi
$fsa5xx3YearsMulti = CompacSoft[plus] FSA 500 (3 years)Multi
$adasSubsS1BrimName = ADAS ONE - DATA
$adasFull3YS1BrimName = ADAS ONE - DATA (3 years)

$countryRestricted = true
$enabledIn = DE


INSERT_UPDATE App; code[unique = true]      ; packageName                   ; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$deCc1987_ALLTRUCKS  ; $aaPackageName1987_ALLTRUCKS  ;
                 ; AA2_$deCc1687_CSBEA750   ; $aaPackageName1687_CSBEA750   ;
                 ; AA2_$deCc1687_CSBEAPC    ; $aaPackageName1687_CSBEAPC    ;
                 ; AA2_$deCc1687_CSFSA500   ; $aaPackageName1687_CSFSA500   ;
                 ; AA2_$deCc1687_CSFSA7XX   ; $aaPackageName1687_CSFSA7XX   ;
                 ; AA2_$deCc1687_DCICRI     ; $aaPackageName1687_DCICRI     ;
                 ; AA2_$deCc1687_DCICRIN    ; $aaPackageName1687_DCICRIN    ;
                 ; AA2_$deCc1987_ESIADV     ; $aaPackageName1987_ESIADV     ;
                 ; AA2_$deCc1987_ESIREPCAT  ; $aaPackageName1987_ESIREPCAT  ;
                 ; AA2_$deCc1987_ESIREPD    ; $aaPackageName1987_ESIREPD    ;
                 ; AA2_$deCc1987_ESIREPE    ; $aaPackageName1987_ESIREPE    ;
                 ; AA2_$deCc1987_ESIDIAG    ; $aaPackageName1987_ESIDIAG    ;
                 ; AA2_$deCc1987_ESIMASTER  ; $aaPackageName1987_ESIMASTER  ;
                 ; AA2_$deCc1987_TRKOHW1    ; $aaPackageName1987_TRKOHW1    ;
                 ; AA2_$deCc1987_TRKOHW2    ; $aaPackageName1987_TRKOHW2    ;
                 ; AA2_$deCc1987_TRKOHW3    ; $aaPackageName1987_TRKOHW3    ;
                 ; AA2_$deCc1987_TRKTRUCK   ; $aaPackageName1987_TRKTRUCK   ;
                 ; AA2_$deCc1987_TSTINFOAW  ; $aaPackageName1987_TSTINFOAW  ;
                 ; AA2_$deCc1987_THLPKW     ; $aaPackageName1987_THLPKW     ;
                 ; AA2_$deCc1987_THLPKWTRK  ; $aaPackageName1987_THLPKWTRK  ;
                 ; AA2_$deCc1987_THLTRK     ; $aaPackageName1987_THLTRK     ;
                 ; AA2_$deCc1687_TSTINFODAT ; $aaPackageName1687_TSTINFODAT ;
                 ; AA2_$deCc1987_TRKUPG     ; $aaPackageName1987_TRUCKUPG   ;
                 ; AA2_$deCc1987_KTS250SD   ; $aaPackageName1987_KTS250     ;
                 ; AA2_$deCc1687_CRR950     ; $aaPackageName1687_CRR950     ;
                 ; AA2_$deCc1987_ADAS_ONE   ; $aaPackageName1987_ADAS_ONE   ;

INSERT_UPDATE App; code[unique = true]      ; name[lang = de]                          ; summary[lang = de]                          ; description[lang = de]                      ; $catalogVersion[unique = true];
                 ; AA2_$deCc1987_ALLTRUCKS  ; Alltrucks                                ; $alltrucksDescription                       ; $alltrucksDescription                       ;
                 ; AA2_$deCc1687_CSBEA750   ; CompacSoft AU BEA 750                    ; $bea750Description                          ; $bea750Description                          ;
                 ; AA2_$deCc1687_CSBEAPC    ; CompacSoft AU BEA-PC                     ; $beaPcDescription                           ; $beaPcDescription                           ;
                 ; AA2_$deCc1687_CSFSA500   ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description                    ; $compacFsa500Description                    ;
                 ; AA2_$deCc1687_CSFSA7XX   ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription                    ; $compacFsa7xxDescription                    ;
                 ; AA2_$deCc1687_DCICRI     ; CRI Bosch                                ; $criDescription                             ; $criDescription                             ;
                 ; AA2_$deCc1687_DCICRIN    ; CRIN Bosch                               ; $crinDescription                            ; $crinDescription                            ;
                 ; AA2_$deCc1987_ESIADV     ; Paket Advanced                           ; $packAdvancedDescription                    ; $packAdvancedDescription                    ;
                 ; AA2_$deCc1987_ESIREPCAT  ; Paket Ersatzteile-Kat.                   ; $packComponentCatDescription                ; $packComponentCatDescription                ;
                 ; AA2_$deCc1987_ESIREPD    ; Paket Repair Diesel                      ; $packComponentRepairDieselDescription       ; $packComponentRepairDieselDescription       ;
                 ; AA2_$deCc1987_ESIREPE    ; Paket Repair Elektrik                    ; $packComponentRepairElectricDescription     ; $packComponentRepairElectricDescription     ;
                 ; AA2_$deCc1987_ESIDIAG    ; Paket Diagnostic                         ; $packDiagnosticDescription                  ; $packDiagnosticDescription                  ;
                 ; AA2_$deCc1987_ESIMASTER  ; Paket Master                             ; $packMasterDescription                      ; $packMasterDescription                      ;
                 ; AA2_$deCc1987_TRKOHW1    ; Off Highway I (OHW I)                    ; $ohw1Description                            ; $ohw1Description                            ;
                 ; AA2_$deCc1987_TRKOHW2    ; Off Highway II (OHW II)                  ; $ohw2Description                            ; $ohw2Description                            ;
                 ; AA2_$deCc1987_TRKOHW3    ; Off Highway III (OHW III)                ; $ohw3Description                            ; $ohw3Description                            ;
                 ; AA2_$deCc1987_TRKTRUCK   ; Paket Truck                              ; $packTruckDescription                       ; $packTruckDescription                       ;
                 ; AA2_$deCc1987_TSTINFOAW  ; Infoart W                                ; $infoartWDescription                        ; $infoartWDescription                        ;
                 ; AA2_$deCc1987_THLPKW     ; Technische Hotline PKW                   ; $thlPkwDescription                          ; $thlPkwDescription                          ;
                 ; AA2_$deCc1987_THLPKWTRK  ; Technische Hotline PKW + Truck           ; $thlPkwTruckDescription                     ; $thlPkwTruckDescription                     ;
                 ; AA2_$deCc1987_THLTRK     ; Technische Hotline Truck                 ; $thlTruckDescription                        ; $thlTruckDescription                        ;
                 ; AA2_$deCc1687_TSTINFODAT ; Infoart Testdata                         ; $infoartTestdataDescription                 ; $infoartTestdataDescription                 ;
                 ; AA2_$deCc1987_TRKUPG     ; ESI[tronic] 2.0 Truck Upgrade            ; $truckUpgradeDescription                    ; $truckUpgradeDescription                    ;
                 ; AA2_$deCc1987_KTS250SD   ; KTS 250 SD ECU-diagnose                  ; $kts250SDDescription                        ; $kts250SDDescription                        ;
                 ; AA2_$deCc1687_CRR950     ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription ; $crr950DieselInjectorsRepairSoftDescription ;
                 ; AA2_$deCc1987_ADAS_ONE   ; ADAS One Solution                        ; $adasDescription                            ; $adasDescription                            ;

INSERT_UPDATE App; code[unique = true]      ; name[lang = en]                          ; summary[lang = en]                             ; description[lang = en]                         ; $catalogVersion[unique = true];
                 ; AA2_$deCc1987_ALLTRUCKS  ; Alltrucks Diagnosis                      ; $alltrucksDescription_en                       ; $alltrucksDescription_en                       ;
                 ; AA2_$deCc1687_CSBEA750   ; BEA 750 SW                               ; $bea750Description_en                          ; $bea750Description_en                          ;
                 ; AA2_$deCc1687_CSBEAPC    ; BEA PC SW                                ; $beaPcDescription_en                           ; $beaPcDescription_en                           ;
                 ; AA2_$deCc1687_CSFSA500   ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description_en                    ; $compacFsa500Description_en                    ;
                 ; AA2_$deCc1687_CSFSA7XX   ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription_en                    ; $compacFsa7xxDescription_en                    ;
                 ; AA2_$deCc1687_DCICRI     ; Component DCI-CRI                        ; $criDescription_en                             ; $criDescription_en                             ;
                 ; AA2_$deCc1687_DCICRIN    ; Component DCI-CRIN                       ; $crinDescription_en                            ; $crinDescription_en                            ;
                 ; AA2_$deCc1987_ESIADV     ; ESI[tronic] 2.0 Advanced                 ; $packAdvancedDescription_en                    ; $packAdvancedDescription_en                    ;
                 ; AA2_$deCc1987_ESIREPCAT  ; ESI[tronic] 2.0 ComponentCat D+E         ; $packComponentCatDescription_en                ; $packComponentCatDescription_en                ;
                 ; AA2_$deCc1987_ESIREPD    ; ESI[tronic] 2.0 ComponentRepair D+E      ; $packComponentRepairDieselDescription_en       ; $packComponentRepairDieselDescription_en       ;
                 ; AA2_$deCc1987_ESIREPE    ; ESI[tronic] 2.0 ComponentRepair E        ; $packComponentRepairElectricDescription_en     ; $packComponentRepairElectricDescription_en     ;
                 ; AA2_$deCc1987_ESIDIAG    ; ESI[tronic] 2.0 Diagnostic               ; $packDiagnosticDescription_en                  ; $packDiagnosticDescription_en                  ;
                 ; AA2_$deCc1987_ESIMASTER  ; ESI[tronic] 2.0 Master                   ; $packMasterDescription_en                      ; $packMasterDescription_en                      ;
                 ; AA2_$deCc1987_TRKOHW1    ; ESI[tronic] 2.0 Truck OHW I              ; $ohw1Description_en                            ; $ohw1Description_en                            ;
                 ; AA2_$deCc1987_TRKOHW2    ; ESI[tronic] 2.0 Truck OHW II             ; $ohw2Description_en                            ; $ohw2Description_en                            ;
                 ; AA2_$deCc1987_TRKOHW3    ; ESI[tronic] 2.0 Truck OHW III            ; $ohw3Description_en                            ; $ohw3Description_en                            ;
                 ; AA2_$deCc1987_TRKTRUCK   ; ESI[tronic] 2.0 Truck                    ; $packTruckDescription_en                       ; $packTruckDescription_en                       ;
                 ; AA2_$deCc1987_TSTINFOAW  ; ESI[tronic] W                            ; $infoartWDescription_en                        ; $infoartWDescription_en                        ;
                 ; AA2_$deCc1987_THLPKW     ; Technical Hotline Car                    ; $thlPkwDescription_en                          ; $thlPkwDescription_en                          ;
                 ; AA2_$deCc1987_THLPKWTRK  ; Technical Hotline Car + Truck            ; $thlPkwTruckDescription_en                     ; $thlPkwTruckDescription_en                     ;
                 ; AA2_$deCc1987_THLTRK     ; Technical Hotline Truck                  ; $thlTruckDescription_en                        ; $thlTruckDescription_en                        ;
                 ; AA2_$deCc1687_TSTINFODAT ; Testdata VP-M/CP                         ; $infoartTestdataDescription_en                 ; $infoartTestdataDescription_en                 ;
                 ; AA2_$deCc1987_TRKUPG     ; ESI[tronic] 2.0 Truck Upgrade            ; $truckUpgradeDescription_en                    ; $truckUpgradeDescription_en                    ;
                 ; AA2_$deCc1987_KTS250SD   ; KTS 250 SD ECU Diagnosis                 ; $kts250SDDescription_en                        ; $kts250SDDescription_en                        ;
                 ; AA2_$deCc1687_CRR950     ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription_en ; $crr950DieselInjectorsRepairSoftDescription_en ;
                 ; AA2_$deCc1987_ADAS_ONE   ; ADAS One Solution                        ; $adasDescription_en                            ; $adasDescription_en                            ;

INSERT_UPDATE ProductContainer; code[unique = true]       ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$deCc1987_ALLTRUCKS  ; AA2_$deCc1987_ALLTRUCKS
                              ; pcaa_$deCc1687_CSBEA750   ; AA2_$deCc1687_CSBEA750
                              ; pcaa_$deCc1687_CSBEAPC    ; AA2_$deCc1687_CSBEAPC
                              ; pcaa_$deCc1687_CSFSA500   ; AA2_$deCc1687_CSFSA500
                              ; pcaa_$deCc1687_CSFSA7XX   ; AA2_$deCc1687_CSFSA7XX
                              ; pcaa_$deCc1687_DCICRI     ; AA2_$deCc1687_DCICRI
                              ; pcaa_$deCc1687_DCICRIN    ; AA2_$deCc1687_DCICRIN
                              ; pcaa_$deCc1987_ESIADV     ; AA2_$deCc1987_ESIADV
                              ; pcaa_$deCc1987_ESIREPCAT  ; AA2_$deCc1987_ESIREPCAT
                              ; pcaa_$deCc1987_ESIREPD    ; AA2_$deCc1987_ESIREPD
                              ; pcaa_$deCc1987_ESIREPE    ; AA2_$deCc1987_ESIREPE
                              ; pcaa_$deCc1987_ESIDIAG    ; AA2_$deCc1987_ESIDIAG
                              ; pcaa_$deCc1987_ESIMASTER  ; AA2_$deCc1987_ESIMASTER
                              ; pcaa_$deCc1987_TRKOHW1    ; AA2_$deCc1987_TRKOHW1
                              ; pcaa_$deCc1987_TRKOHW2    ; AA2_$deCc1987_TRKOHW2
                              ; pcaa_$deCc1987_TRKOHW3    ; AA2_$deCc1987_TRKOHW3
                              ; pcaa_$deCc1987_TRKTRUCK   ; AA2_$deCc1987_TRKTRUCK
                              ; pcaa_$deCc1987_TSTINFOAW  ; AA2_$deCc1987_TSTINFOAW
                              ; pcaa_$deCc1987_THLPKW     ; AA2_$deCc1987_THLPKW
                              ; pcaa_$deCc1987_THLPKWTRK  ; AA2_$deCc1987_THLPKWTRK
                              ; pcaa_$deCc1987_THLTRK     ; AA2_$deCc1987_THLTRK
                              ; pcaa_$deCc1687_TSTINFODAT ; AA2_$deCc1687_TSTINFODAT
                              ; pcaa_$deCc1987_TRKUPG     ; AA2_$deCc1987_TRKUPG
                              ; pcaa_$deCc1987_KTS250SD   ; AA2_$deCc1987_KTS250SD
                              ; pcaa_$deCc1687_CRR950     ; AA2_$deCc1687_CRR950
                              ; pcaa_$deCc1987_ADAS_ONE   ; AA2_$deCc1987_ADAS_ONE

INSERT_UPDATE AppLicense; code[unique = true] ; $baseProduct             ; sellerProductId; brimName[lang = en]                            ; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); specifiedPrice; billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; enabledCountries(isocode)[default = $enabledIn]; availabilityStatus(code)[default = PUBLISHED]
                        ; AA2_$deCc1987P12760 ; AA2_$deCc1987_ALLTRUCKS  ; 1987P12760     ; $alltrucksSubsS1BrimName                       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 2150          ;
                        ; AA2_$deCc1987P12949 ; AA2_$deCc1987_ALLTRUCKS  ; 1987P12949     ; $alltrucksSubsM3BrimName                       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 2190          ;
                        ; AA2_$deCc1687P15152 ; AA2_$deCc1687_CSBEA750   ; 1687P15152     ; $bea750FullBrimName                            ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 385           ;
                        ; AA2_$deCc1687P15150 ; AA2_$deCc1687_CSBEA750   ; 1687P15150     ; $bea750SubsBrimName                            ;                                          ; runtime_subs_unlimited ; <ignore>        ; 265           ;
                        ; AA2_$deCc1687P15142 ; AA2_$deCc1687_CSBEAPC    ; 1687P15142     ; $beaPcFullBrimName                             ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 385           ;
                        ; AA2_$deCc1687P15140 ; AA2_$deCc1687_CSBEAPC    ; 1687P15140     ; $beaPcSubsBrimName                             ;                                          ; runtime_subs_unlimited ; <ignore>        ; 265           ;
                        ; AA2_$deCc1687P15060 ; AA2_$deCc1687_CSFSA500   ; 1687P15060     ; $compacFsa500SubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 1             ;
                        ; AA2_$deCc1687P15045 ; AA2_$deCc1687_CSFSA7XX   ; 1687P15045     ; $compacFsa7xxSubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 310           ;
                        ; AA2_$deCc1687P15090 ; AA2_$deCc1687_DCICRI     ; 1687P15090     ; $criSubsS1BrimName                             ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 300           ;
                        ; AA2_$deCc1687P15102 ; AA2_$deCc1687_DCICRI     ; 1687P15102     ; $criSubsM3BrimName                             ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 400           ;
                        ; AA2_$deCc1687P15100 ; AA2_$deCc1687_DCICRIN    ; 1687P15100     ; $crinSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 300           ;
                        ; AA2_$deCc1687P15107 ; AA2_$deCc1687_DCICRIN    ; 1687P15107     ; $crinSubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 400           ;
                        ; AA2_$deCc1987P12840 ; AA2_$deCc1987_ESIADV     ; 1987P12840     ; $packAdvancedSubsS1BrimName                    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1380          ;
                        ; AA2_$deCc1987P12847 ; AA2_$deCc1987_ESIADV     ; 1987P12847     ; $packAdvancedSubsM3BrimName                    ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1480          ;
                        ; AA2_$deCc1987P12998 ; AA2_$deCc1987_ESIREPCAT  ; 1987P12998     ; $packComponentCatSubsS1BrimName                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 360           ;
                        ; AA2_$deCc1987P12784 ; AA2_$deCc1987_ESIREPCAT  ; 1987P12784     ; $packComponentCatSubsM3BrimName                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 460           ;
                        ; AA2_$deCc1987P12970 ; AA2_$deCc1987_ESIREPD    ; 1987P12970     ; $packComponentRepairDieselSubsS1BrimName       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 664           ;
                        ; AA2_$deCc1987P12297 ; AA2_$deCc1987_ESIREPD    ; 1987P12297     ; $packComponentRepairDieselSubsM3BrimName       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 764           ;
                        ; AA2_$deCc1987P12990 ; AA2_$deCc1987_ESIREPE    ; 1987P12990     ; $packComponentRepairElectricSubsS1BrimName     ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 220           ;
                        ; AA2_$deCc1987P12295 ; AA2_$deCc1987_ESIREPE    ; 1987P12295     ; $packComponentRepairElectricSubsM3BrimName     ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 320           ;
                        ; AA2_$deCc1987P12820 ; AA2_$deCc1987_ESIDIAG    ; 1987P12820     ; $packDiagnosticSubsS1BrimName                  ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 775           ;
                        ; AA2_$deCc1987P12824 ; AA2_$deCc1987_ESIDIAG    ; 1987P12824     ; $packDiagnosticSubsM3BrimName                  ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 875           ;
                        ; AA2_$deCc1987P12051 ; AA2_$deCc1987_ESIDIAG    ; 1987P12051     ; $packDiagnosticFullOneTimeBrimName             ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 1360          ;
                        ; AA2_$deCc1987P12910 ; AA2_$deCc1987_ESIMASTER  ; 1987P12910     ; $packMasterSubsS1BrimName                      ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1985          ;
                        ; AA2_$deCc1987P12917 ; AA2_$deCc1987_ESIMASTER  ; 1987P12917     ; $packMasterSubsM3BrimName                      ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 2085          ;
                        ; AA2_$deCc1987P12260 ; AA2_$deCc1987_TRKOHW1    ; 1987P12260     ; $ohw1SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 600           ;
                        ; AA2_$deCc1987P12262 ; AA2_$deCc1987_TRKOHW1    ; 1987P12262     ; $ohw1SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 700           ;
                        ; AA2_$deCc1987P12278 ; AA2_$deCc1987_TRKOHW2    ; 1987P12278     ; $ohw2SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1180          ;
                        ; AA2_$deCc1987P12275 ; AA2_$deCc1987_TRKOHW2    ; 1987P12275     ; $ohw2SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1280          ;
                        ; AA2_$deCc1987P12254 ; AA2_$deCc1987_TRKOHW3    ; 1987P12254     ; $ohw3SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 450           ;
                        ; AA2_$deCc1987P12257 ; AA2_$deCc1987_TRKOHW3    ; 1987P12257     ; $ohw3SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 550           ;
                        ; AA2_$deCc1987P12400 ; AA2_$deCc1987_TRKTRUCK   ; 1987P12400     ; $packTruckSubsS1BrimName                       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1280          ;
                        ; AA2_$deCc1987P12936 ; AA2_$deCc1987_TRKTRUCK   ; 1987P12936     ; $packTruckSubsM3BrimName                       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1380          ;
                        ; AA2_$deCc1987P12412 ; AA2_$deCc1987_TRKTRUCK   ; 1987P12412     ; $packTruckFullBrimName                         ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 2890          ;
                        ; AA2_$deCc1987P12500 ; AA2_$deCc1987_TSTINFOAW  ; 1987P12500     ; $infoartWSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; 680           ;
                        ; AA2_$deCc1987729274 ; AA2_$deCc1987_THLPKW     ; 1987729274     ; $thlPkwSubsBrimName                            ;                                          ; runtime_subs_unlimited ; <ignore>        ; 450           ;
                        ; AA2_$deCc1987P13515 ; AA2_$deCc1987_THLTRK     ; 1987P13515     ; $thlTrkSubsBrimName                            ;                                          ; runtime_subs_unlimited ; <ignore>        ; 675           ;
                        ; AA2_$deCc1987P13516 ; AA2_$deCc1987_THLPKWTRK  ; 1987P13516     ; $thlPkwTrkSubsBrimName                         ;                                          ; runtime_subs_unlimited ; <ignore>        ; 915           ;
                        ; AA2_$deCc1687P15015 ; AA2_$deCc1687_TSTINFODAT ; 1687P15015     ; $infoartTestdataSubsBrimName                   ;                                          ; runtime_subs_unlimited ; <ignore>        ; 304           ;
                        ; AA2_$deCc1987P12404 ; AA2_$deCc1987_TRKUPG     ; 1987P12404     ; $truckUpgradeSubsS1BrimName                    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1070          ;
                        ; AA2_$deCc1987P12359 ; AA2_$deCc1987_TRKUPG     ; 1987P12359     ; $truckUpgradeSubsM3BrimName                    ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1170          ;
                        ; AA2_$deCc1987P12389 ; AA2_$deCc1987_KTS250SD   ; 1987P12389     ; $kts250SDFullBrimName                          ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 399           ;
                        ; AA2_$deCc1987P12385 ; AA2_$deCc1987_KTS250SD   ; 1987P12385     ; $kts250SDSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; 645           ;
                        ; AA2_$deCc1687P15137 ; AA2_$deCc1687_CRR950     ; 1687P15137     ; $crr950DieselInjectorsRepairSoftwareBrimName1L ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 100           ;
                        ; AA2_$deCc1687P15139 ; AA2_$deCc1687_CRR950     ; 1687P15139     ; $crr950DieselInjectorsRepairSoftwareBrimName3L ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 200           ;
                        ; AA2_$deCc1987P12515 ; AA2_$deCc1987_ADAS_ONE   ; 1987P12515     ; $adasSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 0.01          ;
                        ; AA2_$deCc1987P12517 ; AA2_$deCc1987_ADAS_ONE   ; 1987P12517     ; $adasFull3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 0.01          ;
                        ; AA2_$deCc1687P15170 ; AA2_$deCc1687_CSFSA500   ; 1687P15170     ; $fsa5xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$deCc1687P15173 ; AA2_$deCc1687_CSFSA500   ; 1687P15173     ; $fsa5xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;
                        ; AA2_$deCc1687P15160 ; AA2_$deCc1687_CSFSA7XX   ; 1687P15160     ; $fsa7xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$deCc1687P15163 ; AA2_$deCc1687_CSFSA7XX   ; 1687P15163     ; $fsa7xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;

# The licenses below were discontinued by MA and should be available only for the contract migration.
INSERT_UPDATE AppLicense; code[unique = true] ; $catalogVersion[unique = true]; availabilityStatus(code)[default = UNPUBLISHED]
                        ; AA2_$deCc1687P15142 ;
                        ; AA2_$deCc1687P15152 ;
                        ; AA2_$deCc1987P12051 ;
                        ; AA2_$deCc1987P12389 ;
                        ; AA2_$deCc1987P12412 ;

UPDATE AppLicense; code[unique = true] ; userGroups(uid)                                                                                                                                                         ; $catalogVersion[unique = true];
                 ; AA2_$deCc1987P12760 ; IDW000,AT0000                                                                                                                                                           ;
                 ; AA2_$deCc1987P12949 ; IDW000,AT0000                                                                                                                                                           ;
                 ; AA2_$deCc1687P15152 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1687P15150 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1687P15142 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1687P15140 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1687P15060 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1687P15045 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1687P15090 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1687P15102 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1687P15100 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1687P15107 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12840 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0001,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026 ;
                 ; AA2_$deCc1987P12847 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12998 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12784 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12970 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12297 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12990 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12295 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12820 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12824 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12051 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12910 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12917 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12260 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12262 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12278 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12254 ; IDW000                                                                                                                                                                  ;
                 ; AA2_$deCc1987P12257 ; IDW000                                                                                                                                                                  ;
                 ; AA2_$deCc1987P12275 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12400 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026 ;
                 ; AA2_$deCc1987P12936 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026 ;
                 ; AA2_$deCc1987P12412 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026 ;
                 ; AA2_$deCc1987P12500 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026 ;
                 ; AA2_$deCc1987729274 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026 ;
                 ; AA2_$deCc1987P13515 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026 ;
                 ; AA2_$deCc1987P13516 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026 ;
                 ; AA2_$deCc1687P15015 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026 ;
                 ; AA2_$deCc1987P12404 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026 ;
                 ; AA2_$deCc1987P12359 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026 ;
                 ; AA2_$deCc1987P12389 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1987P12385 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026        ;
                 ; AA2_$deCc1687P15137 ; IDW000                                                                                                                                                                  ;
                 ; AA2_$deCc1687P15139 ; IDW000                                                                                                                                                                  ;
# The group assignment for the ADAS ONE is most probably not final. we're missing the prices for now
                 ; AA2_$deCc1987P12515 ; IDW000                                                                                                                                                                  ;
                 ; AA2_$deCc1987P12517 ; IDW000                                                                                                                                                                  ;
                 ; AA2_$deCc1687P15163 ; IDW000                                                                                                                                                                  ;
                 ; AA2_$deCc1687P15170 ; IDW000                                                                                                                                                                  ;
                 ; AA2_$deCc1687P15160 ; IDW000                                                                                                                                                                  ;
                 ; AA2_$deCc1687P15173 ; IDW000                                                                                                                                                                  ;


INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$deCc1987P12760                          ; 2150
                      ; AA2_$deCc1987P12949                          ; 2190
                      ; AA2_$deCc1687P15152                          ; 385
                      ; AA2_$deCc1687P15150                          ; 265
                      ; AA2_$deCc1687P15142                          ; 385
                      ; AA2_$deCc1687P15140                          ; 265
                      ; AA2_$deCc1687P15060                          ; 1
                      ; AA2_$deCc1687P15045                          ; 310
                      ; AA2_$deCc1687P15090                          ; 300
                      ; AA2_$deCc1687P15102                          ; 400
                      ; AA2_$deCc1687P15100                          ; 300
                      ; AA2_$deCc1687P15107                          ; 400
                      ; AA2_$deCc1987P12840                          ; 1380
                      ; AA2_$deCc1987P12847                          ; 1480
                      ; AA2_$deCc1987P12998                          ; 360
                      ; AA2_$deCc1987P12784                          ; 460
                      ; AA2_$deCc1987P12970                          ; 664
                      ; AA2_$deCc1987P12297                          ; 764
                      ; AA2_$deCc1987P12990                          ; 220
                      ; AA2_$deCc1987P12295                          ; 320
                      ; AA2_$deCc1987P12820                          ; 775
                      ; AA2_$deCc1987P12824                          ; 875
                      ; AA2_$deCc1987P12051                          ; 1360
                      ; AA2_$deCc1987P12910                          ; 1985
                      ; AA2_$deCc1987P12917                          ; 2085
                      ; AA2_$deCc1987P12260                          ; 600
                      ; AA2_$deCc1987P12262                          ; 700
                      ; AA2_$deCc1987P12278                          ; 1180
                      ; AA2_$deCc1987P12254                          ; 450
                      ; AA2_$deCc1987P12257                          ; 550
                      ; AA2_$deCc1987P12275                          ; 1280
                      ; AA2_$deCc1987P12400                          ; 1280
                      ; AA2_$deCc1987P12936                          ; 1380
                      ; AA2_$deCc1987P12412                          ; 2890
                      ; AA2_$deCc1987P12500                          ; 680
                      ; AA2_$deCc1987729274                          ; 450
                      ; AA2_$deCc1987P13515                          ; 675
                      ; AA2_$deCc1987P13516                          ; 915
                      ; AA2_$deCc1687P15015                          ; 304
                      ; AA2_$deCc1987P12404                          ; 1070
                      ; AA2_$deCc1987P12359                          ; 1170
                      ; AA2_$deCc1987P12389                          ; 399
                      ; AA2_$deCc1987P12385                          ; 645
                      ; AA2_$deCc1687P15137                          ; 100  ;
                      ; AA2_$deCc1687P15139                          ; 200  ;
                      ; AA2_$deCc1987P12515                          ; 0.01 ;
                      ; AA2_$deCc1987P12517                          ; 0.01 ;
                      ; AA2_$deCc1687P15163                          ; 0.01 ;
                      ; AA2_$deCc1687P15170                          ; 0.01 ;
                      ; AA2_$deCc1687P15160                          ; 0.01 ;
                      ; AA2_$deCc1687P15173                          ; 0.01 ;


INSERT_UPDATE App; code[unique = true]     ; boms(code)                                                                                                           ; $catalogVersion[unique = true]
                 ; AA2_$deCc1987_ALLTRUCKS ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                                  ;
                 ; AA2_$deCc1987_ESIADV    ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe                                       ;
                 ; AA2_$deCc1987_ESIREPCAT ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE                                                                                     ;
                 ; AA2_$deCc1987_ESIREPD   ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE,MAT_$aaCcK3                                                                         ;
                 ; AA2_$deCc1987_ESIREPE   ; MAT_$aaCcA,MAT_$aaCcE,MAT_$aaCcK2                                                                                    ;
                 ; AA2_$deCc1987_ESIDIAG   ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB                                                                  ;
                 ; AA2_$deCc1987_ESIMASTER ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe,MAT_$aaCcM,MAT_$aaCcP,MAT_$aaCcTHLPKW ;
                 ; AA2_$deCc1987_TRKOHW1   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                                  ;
                 ; AA2_$deCc1987_TRKOHW2   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                                  ;
                 ; AA2_$deCc1987_TRKOHW3   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                                  ;
                 ; AA2_$deCc1987_TRKTRUCK  ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                                  ;
                 ; AA2_$deCc1987_TRKUPG    ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                                  ;

INSERT_UPDATE App; code[unique = true]      ; contentModules(code)                                                                                     ; $catalogVersion[unique = true];
                 ; AA2_$deCc1987_ALLTRUCKS  ; CM_$aaCcNEOOrangeATruck, CM_$aaCcETruck
                 ; AA2_$deCc1687_CSBEA750   ; CM_$aaCcCSBEA750SW
                 ; AA2_$deCc1687_CSFSA500   ; CM_$aaCcCSFSA5                                                                                           ;
                 ; AA2_$deCc1687_CSFSA7XX   ; CM_$aaCcCSS,CM_$aaCcCSK                                                                                  ;
                 ; AA2_$deCc1687_DCICRI     ; CM_$aaCcDCICRI                                                                                           ;
                 ; AA2_$deCc1687_DCICRIN    ; CM_$aaCcDCICRIN                                                                                          ;
                 ; AA2_$deCc1987_ESIADV     ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe                                 ;
                 ; AA2_$deCc1987_ESIREPCAT  ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE                                                                            ;
                 ; AA2_$deCc1987_ESIREPD    ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE,CM_$aaCcK3                                                                 ;
                 ; AA2_$deCc1987_ESIREPE    ; CM_$aaCcA,CM_$aaCcE,CM_$aaCcK2                                                                           ;
                 ; AA2_$deCc1987_ESIDIAG    ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB                                                          ;
                 ; AA2_$deCc1987_ESIMASTER  ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe,CM_$aaCcM,CM_$aaCcP,CM_$aaCcTTS ;
                 ; AA2_$deCc1987_TRKOHW1    ; CM_$aaCcETOHW1                                                                                           ;
                 ; AA2_$deCc1987_TRKOHW2    ; CM_$aaCcETOHW2                                                                                           ;
                 ; AA2_$deCc1987_TRKOHW3    ; CM_$aaCcETOHW3                                                                                           ;
                 ; AA2_$deCc1987_TRKTRUCK   ; CM_$aaCcETruck                                                                                           ;
                 ; AA2_$deCc1987_TSTINFOAW  ; CM_$aaCcEW                                                                                               ;
                 ; AA2_$deCc1987_THLPKW     ; CM_$aaCcPKWTHL                                                                                           ;
                 ; AA2_$deCc1987_THLTRK     ; CM_$aaCcPKWTHL                                                                                           ;
                 ; AA2_$deCc1987_THLPKWTRK  ; CM_$aaCcTHLCarTruck                                                                                      ;
                 ; AA2_$deCc1687_TSTINFODAT ; CM_$aaCcTVPMCP                                                                                           ;
                 ; AA2_$deCc1987_TRKUPG     ; CM_$aaCcTRKUPG                                                                                           ;
                 ; AA2_$deCc1987_KTS250SD   ; CM_$aaCcKTS250ECUD                                                                                       ;
                 ; AA2_$deCc1687_CRR950     ; CM_$aaCcCRR950

UPDATE AppLicense; code[unique = true] ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$deCc1987P12051 ; CM_$aaCcSDSDA
                 ; AA2_$deCc1687P15144 ; CM_$aaCcCSBEAPCSW_NOAU
                 ; AA2_$deCc1687P15142 ; CM_$aaCcCSBEAPCSW
                 ; AA2_$deCc1687P15140 ; CM_$aaCcCSBEAPCSW


INSERT_UPDATE App; code[unique = true]      ; $supercategories; $catalogVersion[unique = true];
                 ; AA2_$deCc1987_ALLTRUCKS  ; cat_1010201
                 ; AA2_$deCc1687_CSBEA750   ; cat_301
                 ; AA2_$deCc1687_CSBEAPC    ; cat_301
                 ; AA2_$deCc1687_CSFSA500   ; cat_201
                 ; AA2_$deCc1687_CSFSA7XX   ; cat_201
                 ; AA2_$deCc1687_DCICRI     ; cat_401
                 ; AA2_$deCc1687_DCICRIN    ; cat_401
                 ; AA2_$deCc1987_ESIADV     ; cat_10101
                 ; AA2_$deCc1987_ESIREPCAT  ; cat_1010101
                 ; AA2_$deCc1987_ESIREPD    ; cat_1010101
                 ; AA2_$deCc1987_ESIREPE    ; cat_1010101
                 ; AA2_$deCc1987_ESIDIAG    ; cat_10101
                 ; AA2_$deCc1987_ESIMASTER  ; cat_10101
                 ; AA2_$deCc1987_TRKOHW1    ; cat_10102
                 ; AA2_$deCc1987_TRKOHW2    ; cat_10102
                 ; AA2_$deCc1987_TRKOHW3    ; cat_10102
                 ; AA2_$deCc1987_TRKTRUCK   ; cat_10102
                 ; AA2_$deCc1987_TSTINFOAW  ; cat_40101
                 ; AA2_$deCc1987_THLPKW     ; cat_1010102
                 ; AA2_$deCc1987_THLTRK     ; cat_1010202
                 ; AA2_$deCc1987_THLPKWTRK  ; cat_1010102,cat_1010202
                 ; AA2_$deCc1687_TSTINFODAT ; cat_40101
                 ; AA2_$deCc1987_TRKUPG     ; cat_10102
                 ; AA2_$deCc1987_KTS250SD   ; cat_1010104
                 ; AA2_$deCc1687_CRR950     ; cat_401
                 ; AA2_$deCc1987_ADAS_ONE   ; cat_501         ;

INSERT_UPDATE App; code[unique = true]      ; eulaContainers(code); $catalogVersion[unique = true]
                 ; AA2_$deCc1987_TSTINFOAW  ; AA2_ESItronic
                 ; AA2_$deCc1987_THLPKW     ; AA2_THL
                 ; AA2_$deCc1987_THLPKWTRK  ; AA2_THL
                 ; AA2_$deCc1987_THLTRK     ; AA2_THL
                 ; AA2_$deCc1687_CSBEAPC    ; AA2_BEA,AA2_BEA_ADAT
                 ; AA2_$deCc1687_CSBEA750   ; AA2_BEA,AA2_BEA_ADAT
                 ; AA2_$deCc1687_DCICRI     ; AA2_DCICRI
                 ; AA2_$deCc1687_DCICRIN    ; AA2_DCICRIN
                 ; AA2_$deCc1687_TSTINFODAT ; AA2_ESItronic
                 ; AA2_$deCc1687_CSFSA500   ; AA2_FSA
                 ; AA2_$deCc1687_CSFSA7XX   ; AA2_FSA
                 ; AA2_$deCc1987_KTS250SD   ; AA2_ESItronic
                 ; AA2_$deCc1687_CRR950     ; AA2_ESItronic
