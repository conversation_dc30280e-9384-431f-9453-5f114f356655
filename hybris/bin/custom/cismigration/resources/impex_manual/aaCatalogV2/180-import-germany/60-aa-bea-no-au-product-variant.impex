# This file is used to import an additional OTP BEA product for Germany.
# It is created only for the contract migration, and is disabled by default.

#% impex.setLocale( Locale.ENGLISH );

# numerical code for DE, used as prefix for product code
$deCc = 049
$aaCc = 040

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]

$beaPcNoAuFullBrimName = SystemSoft for BEA PC DE OTP

$enabledIn = DE

INSERT_UPDATE AppLicense; code[unique = true] ; $baseProduct          ; sellerProductId; brimName[lang = en]    ; licenseType(code); runtime(code)          ; bundleInfo(code); specifiedPrice; billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; enabledCountries(isocode)[default = $enabledIn]; availabilityStatus(code)[default = UNPUBLISHED]
                        ; AA2_$deCc1687P15144 ; AA2_$deCc1687_CSBEAPC ; 1687P15144     ; $beaPcNoAuFullBrimName ; "FULL"           ; runtime_full_unlimited ; <ignore>        ; 1             ;

UPDATE AppLicense; code[unique = true] ; userGroups(uid)                                                                                                                                                  ; $catalogVersion[unique = true];
                 ; AA2_$deCc1687P15144 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,WD0000,KA0011,KA0012,KA0013,KA0014,KA0015,KA0016,KA0017,KA0018,KA0019,KA0020,KA0021,KA0022,KA0023,KA0024,KA0025,KA0026 ;


INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$deCc1687P15144                          ; 1


UPDATE AppLicense; code[unique = true] ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$deCc1687P15144 ; CM_$aaCcCSBEAPCSW_NOAU
