#% impex.setLocale( Locale.ENGLISH );

# numerical code for ES, used as prefix for product code
$esCc = 034
$aaCc = 040
$aaPackageName = com.sast.aa.es.

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf

# please make sure, to use right DELEVELOPER COMPANY for Spain in the right environment.
# live
# $companyId = 22c8f257-cb93-4498-a74a-296b2058d779
# demo
# $companyId = 8d12b010-9c67-4c0a-b69e-ca3bcc0468fe
# dev
# $companyId = c963a96d-d232-4d09-ba44-fedf6eba175d
# local
$companyId = b150e4f8-acb6-4f6b-ba88-fcc849268ba2

$compacFsa500Description = CompacSoft[plus] para FSA 500 está equipado con pasos de prueba guiados por el menú y tests de componentes para la comprobación del sistema electrónico y eléctrico de vehículos.
$compacFsa500Description_en = The CompacSoft[plus] for FSA 500 is equipped with preset component tests and can be connected to existing systems, as well as used to gradually expand your workshop test system.
$compacFsa7xxDescription = CompacSoft[plus] para FSA 7xx está equipado con pasos de prueba guiados por el menú con valores de referencia de cada vehículo y tests de componentes para la comprobación del sistema electrónico y eléctrico de vehículos.
$compacFsa7xxDescription_en = With CompacSoft[plus] for FSA 7xx, the comfort for all measuring tasks on the vehicle is further increased by the menu-guided test steps, the optional vehicle-specific set values, as well as the display of the actual values.
$criDescription = El software ESI[tronic] CRI para DCI 200 y DCI 700 proporciona datos actualizados, garantiza procesos sencillos e incluye la comprobación de inyectores piezoeléctricos para sistemas Common Rail.
$criDescription_en = CRI for DCI 700 software provides up-to-date data, ensures smooth processes and includes testing of piezo injectors for common rail systems.
$crinDescription = El software ESI[tronic] CRIN para DCI 200 y DCI 700 proporciona datos actualizados, garantiza procesos sencillos e incluye la comprobación de inyectores de válvulas solenoides para sistemas Common Rail.
$crinDescription_en = CRIN for DCI 700 software provides up-to-date data, ensures smooth processes and includes the testing of solenoid valve injectors for common rail systems.
$packAdvancedDescription = El siguiente nivel de equipamiento profesional para el taller. Además del paquete ESI[tronic] Diagnóstico, también se incluyen manuales e instrucciones de Bosch y otros fabricantes. Connected Repair proporciona un historial de servicio y mantenimiento, así como información sobre reparaciones.
$packAdvancedDescription_en = The next level of professional workshop equipment. In addition to the Diagnostic package, instructions and manuals are included. Connected Repair provides service and maintenance history and repair information.
$packComponentCatDescription = El catálogo recambios incluye las aplicaciones, las funciones y el equipamiento del vehículo, así como los repuestos diésel y eléctricos, incluyendo el archivo y los repuestos eléctricos ESI[tronic]-F.
$packComponentCatDescription_en = The spare parts catalog package includes the applications, functions and automotive equipment as well as the diesel spare parts and electrical spare parts incl. archive and electrical spare part ESI[tronic]-F.
$packComponentRepairDieselDescription = El paquete Reparación de componentes diésel y eléctricos contiene amplia información sobre repuestos y la reparación de componentes diésel y eléctricos. Esto permite identificar correctamente el vehículo y toda la gama de equipamiento Bosch del vehículo. Además, incluye manuales de reparación y telegramas de servicio para componentes.
$packComponentRepairDieselDescription_en = The Repair Diesel package provides information on spare parts and repair of diesel and electrical components. It allows identification of the vehicle, Bosch automotive equipment and includes repair instructions & service information.
$packComponentRepairElectricDescription = Debido al continuo aumento de los modelos de vehículos, los talleres cada vez lo tienen más difícil para mantenerse al día con el constante flujo de actualizaciones de información sobre los sistemas eléctricos para vehículos.El paquete Reparación de componentes eléctricos puede ser de ayuda al suministrar datos de repuestos para sistemas eléctricos de vehículos en un formato claro y sistemático.
$packComponentRepairElectricDescription_en = The increasing number of vehicle models makes it difficult for workshops to have up-to-date information on vehicle electrical systems at their fingertips. The Repair Electrics Package provides support with spare parts data on car electrics in a clear format.
$packDiagnosticDescription = Paquete inicial para diagnóstico, reparaciones y mantenimiento profesionales. Permite realizar diagnósticos con un estándar profesional y ofrece una amplia gama de otras funciones para todos los vehículos que cubre.
$packDiagnosticDescription_en = The entry into professional diagnosis, repair and maintenance. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.
$packMasterDescription = Paquete completo para el diagnóstico profesional de vehículos. Proporciona soporte para todas las tareas de diagnóstico, reparación y mantenimiento, y también ofrece información y funciones sobre diagnóstico, reparaciones, mantenimiento, piezas de repuesto, documentación y gestión de datos.
$packMasterDescription_en = The fully comprehensive package for professional vehicle diagnostics. It provides all the necessary information for diagnosis, repair, maintenance, spare parts, documentation and data management. Technical Support assists you in finding solutions.
$ohw1Description = El paquete ESI[tronic] Truck Agrícola asiste a talleres con un diagnóstico fiable, un mantenimiento integral y reparaciones eficientes de todos los vehículos agrícolas. Incluye, entre otras cosas, funciones de ajuste y parametrización para sistemas hidráulicos.
$ohw1Description_en = The Agricultural Machinery Diagnostic Package provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw2Description = El paquete ESI[tronic] Truck Obra pública asiste a talleres con un diagnóstico fiable, un mantenimiento integral y reparaciones eficientes de todos los vehículos de obra pública, barredoras y motores estacionarios. Incluye, entre otras cosas, funciones de ajuste y parametrización para sistemas hidráulicos.
$ohw2Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw3Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$packTruckDescription = El paquete ESI[tronic] Truck asiste a talleres con un diagnóstico fiable, un mantenimiento integral y reparaciones eficientes de todos los vehículos industriales, ya sean camiones, remolques, autobuses o furgonetas.
$packTruckDescription_en = The Truck package supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.
$infoartWDescription = El ESI[tronic] W contiene información sobre los valores de prueba de diésel para combinaciones de bombas en línea, así como para bombas VE, el procedimiento completo de prueba, desde la determinación de los valores medidos hasta la impresión del informe y la visualización de las etapas de prueba en la secuencia óptima.
$infoartWDescription_en = The ESI[tronic] 2.0 Infoart W contains information about diesel test values for in-line pump combinations as well as for VE pumps, the complete test process test procedure from the determination of the measured values to the printout of the report and the display of the test steps in the optimum sequence.
$thlPkwDescription = ¿Necesita asistencia técnica para el mantenimiento o la reparación de un coche o simplemente una segunda opinión? Póngase en contacto con nuestro equipo de soporte técnico y obtenga soluciones rápidas y probadas.
$thlPkwDescription_en = Do you need technical support to maintain or repair a car, or simply a reliable second opinion? Then contact our support team and get fast and sound solutions.
$thlTruckDescription = ¿Necesita asistencia técnica para el mantenimiento o la reparación de un vehículo industrial o simplemente una segunda opinión? Póngase en contacto con nuestro equipo de soporte técnico y obtenga soluciones rápidas y probadas.
$thlTruckDescription_en = Do you need technical support to maintain or repair a truck, or just a reliable second opinion? Then contact our support team and get fast and sound solutions.
$thlPkwTruckDescription = Con una gran cobertura de marcas, modelos y sistemas, nuestros técnicos  ofrecen a los técnicos del taller una completa asistencia técnica y documentación sobre una amplia variedad de reparaciones, para turismos y vehículos industriales.
$thlPkwTruckDescription_en = With extensive coverage of makes, models and systems, the support team can provide technical assistance and documentation for various light and heavy commercial vehicle repairs.
$infoartTestdataDescription = El ESI[tronic] Testdata (CD) contiene valores de prueba para bombas de alta presión Common Rail de Bosch, inyectores Common Rail y bombas de inyección inyección de distribución VP 29 / 30 / 44.
$infoartTestdataDescription_en = The ESI[tronic] 2.0-Infotype Testdata (CD) contains test values for Bosch Common Rail high pressure pumps, Common Rail injectors and VP 29 / 30 / 44 distributor injection pumps.
$truckUpgradeDescription = El paquete Ampliación Truck está indicado para clientes existentes de paquetes ESI[tronic] para Turismo y que adquieren un KTS Truck. Este paquete asiste a talleres con un diagnóstico fiable, un mantenimiento integral y reparaciones eficientes de todos los vehículos industriales, ya sean camiones, remolques, autobuses o furgonetas.
$truckUpgradeDescription_en = The Truck Upgrade package is dedicated to ESI[tronic] Car users and supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.
$coReDescription = Bosch Connected Repair es un software que conecta los equipos del taller, los datos del vehículo y la información de reparación. Tanto si se trata de solucionar problemas como de almacenar datos e imágenes de acuerdo con el Reglamento General de Protección de Datos, CoRe se ha adaptado a las necesidades de los clientes.
$coReDescription_en = Bosch Connected Repair is software that connects workshop equipment, vehicle and repair data. Whether in the event of malfunctions or the storage of data and images in accordance with the Basic Data Protection Regulation - CoRe has been adapted to the needs of customers.
$kts250SDDescription = La entrada en el diagnóstico, la reparación y el mantenimiento profesionales específicamente para el KTS 250. Permite realizar diagnósticos con un estándar profesional y ofrece una amplia gama de otras funciones para todos los vehículos que cubre.
$kts250SDDescription_en = The entry into professional diagnosis, repair and maintenance specifically for KTS 250. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.

$crr950DieselInjectorsRepairSoftDescription = Software de reparación de inyectores diésel CRR 950
$crr950DieselInjectorsRepairSoftDescription_en = The CRR 950 guides and support the professional repairs of magnetic solenoid common-rail injectors from Bosch

# TODO: UPDATE TRANSLATION
$adasDescription = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.
$adasDescription_en = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.

$compacFsa500Full3YBrimName = CompacSoft[plus] FSA 500 (3 years)
$compacFsa500SubsBrimName = CompacSoft[plus] FSA 500
$compacFsa7xxFull3YBrimName = CompacSoft[plus] FSA 7xx (3 years)
$compacFsa7xxSubsBrimName = CompacSoft[plus] FSA 7xx
$criSubsS1BrimName = Component DCI-CRI
$criSubsM3BrimName = Component DCI-CRI_Multi-users_3
$criFull3YBrimName = Component DCI-CRI (3 year)
$crinSubsS1BrimName = Component DCI-CRIN
$crinSubsM3BrimName = Component DCI-CRIN_Multi-users_3
$crinFull3YBrimName = Component DCI-CRIN (3 year)
$packAdvancedSubsS1BrimName = ESI 2.0 Advanced Unlimited
$packAdvancedSubsM3BrimName = ESI 2.0 Advanced Unlimited Multi
$packAdvancedFull3YS1BrimName = ESI 2.0 Advanced 3 years
$packAdvancedFull3YM3BrimName = ESI 2.0 Advanced 3 years Multi
$packComponentCatSubsS1BrimName = ESI 2.0 ComponentCatalog D+E Unlimited
$packComponentCatSubsM3BrimName = ESI 2.0 ComponentCatalog D+E Unlim Multi
$packComponentCatFull3YS1BrimName = ESI 2.0 ComponentCatalog D+E 3 years
$packComponentCatFull3YM3BrimName = ESI 2.0 ComponentCatalog D+E 3y Multi
$packComponentRepairDieselSubsS1BrimName = ESI 2.0 ComponentRepair D+E Unlimited
$packComponentRepairDieselSubsM3BrimName = ESI 2.0 ComponentRepair D+E Unlim Multi
$packComponentRepairDieselFull3YS1BrimName = ESI 2.0 ComponentRepair D+E 3 years
$packComponentRepairDieselFull3YM3BrimName = ESI 2.0 ComponentRepair D+E 3y Multi
$packComponentRepairElectricSubsS1BrimName = ESI 2.0 ComponentRepair E Unlimited
$packComponentRepairElectricSubsM3BrimName = ESI 2.0 ComponentRepair E Unlim Multi
$packComponentRepairElectricFull3YS1BrimName = ESI 2.0 ComponentRepair E 3 years
$packComponentRepairElectricFull3YM3BrimName = ESI 2.0 ComponentRepair E 3 years Multi
$packDiagnosticSubsS1BrimName = ESI 2.0 Diagnostic Unlimited
$packDiagnosticSubsM3BrimName = ESI 2.0 Diagnostic Unlimited Multi
$packDiagnosticFull3YS1BrimName = ESI 2.0 Diagnostic 3 years
$packDiagnosticFull3YM3BrimName = ESI 2.0 Diagnostic 3 years Multi
$packDiagnosticFullOneTimeBrimName = ESI[tronic] 2.0 SD (OTP one time purchase)
$packMasterSubsS1BrimName = ESI 2.0 Master Unlimited
$packMasterSubsM3BrimName = ESI 2.0 Master Unlimited Multi
$packMasterFull3YS1BrimName = ESI 2.0 Master 3 years
$packMasterFull3YM3BrimName = ESI 2.0 Master 3 years Multi
$ohw1SubsS1BrimName = ESI[tronic] 2.0 Truck OHW I
$ohw1SubsM3BrimName = ESI[tronic] 2.0 Truck OHW I Multi
$ohw1Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW I (3 years)
$ohw1Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW I (3y) Multi
$ohw2SubsS1BrimName = ESI[tronic] 2.0 Truck OHW II
$ohw2SubsM3BrimName = ESI[tronic] 2.0 Truck OHW II Multi
$ohw2Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW II (3 years)
$ohw2Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW II (3y) Multi
$ohw3S1BrimName = ESI[tronic] 2.0 Truck OHW III Unlimited
$ohw3M3BrimName = ESI[tronic] 2.0 Truck OHW III Unlimited Multi
$ohw3Single3YearBrimName = ESI[tronic] Truck OHW III (3y)
$ohw3Multi3YearBrimName = ESI[tronic] Truck OHW III (3y) Multi.
$packTruckSubsS1BrimName = ESI[tronic] 2.0 Truck
$packTruckSubsM3BrimName = ESI[tronic] 2.0 Truck Multi
$packTruckFull3YS1BrimName = ESI[tronic] 2.0 Truck (3 years)
$packTruckFull3YM3BrimName = ESI[tronic] 2.0 Truck (3 years) Multi
$packTruckFullBrimName = ESI[tronic] 2.0 Truck (Einmalkauf)
$infoartWSubsBrimName = ESI[tronic] W Diesel Test Data (WP)
$infoartWFull3YBrimName = ESI[tronic] W (3 Jahre)
$thlPkwSubsBrimName = THL use technical hotline
$thlPkwFull3YBrimName = Technische Hotline LKW (3 Jahre)
$thlTrkSubsBrimName = Technische Hotline ESI[tronic] for Truck
$thlTrkFull3YBrimName = Technische Hotline LKW (3 Jahre)
$thlPkwTrkSubsBrimName = Technische Hotline fuer LKWs und PKWs
$thlPkwTrkFull3YBrimName = Technische Hotline PKW/LKW (3 Jahre)
$infoartTestdataSubsBrimName = Testdata VP-M/CP
$truckUpgradeSubsS1BrimName = ESI[tronic] 2.0 Truck Upgrade
$truckUpgradeSubsM3BrimName = ESI[tronic] 2.0 Truck Upgrade Multi
$truckUpgradeFull3YS1BrimName = ESI[tronic] 2.0 Truck Upgrade (3 years)
$truckUpgradeFull3YM3BrimName = ESI[tronic] 2.0 Truck Upgrade (3y) Multi
$coreSubsS1BrimName = CoRe for ESI 2.0 packages
$kts250SDFullBrimName = KTS 250 SD (OTP one time purchase)
$kts250SDSubsBrimName = KTS 250 SD ECU Diagnosis

$crr950DieselInjectorsRepairSoftwareBrimName1L = CRR 950 Diesel Injectors (1 License)
$crr950DieselInjectorsRepairSoftwareBrimName3L = CRR 950 Diesel Injectors (3 Licenses)
$fsa7xxMulti = CompacSoft[plus] FSA 7xx Multi
$fsa7xx3YearsMulti = CompacSoft[plus] FSA 7xx (3 years) Multi
$fsa5xxMulti = CompacSoft[plus] FSA 500 Multi
$fsa5xx3YearsMulti = CompacSoft[plus] FSA 500 (3 years)Multi
$adasSubsS1BrimName = ADAS ONE - DATA
$adasFull3YS1BrimName = ADAS ONE - DATA (3 years)

$countryRestricted = true
$enabledIn = ES


INSERT_UPDATE App; code[unique = true]      ; packageName                   ; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$esCc1687_CSFSA500   ; $aaPackageName1687_CSFSA500   ;
                 ; AA2_$esCc1687_CSFSA7XX   ; $aaPackageName1687_CSFSA7XX   ;
                 ; AA2_$esCc1687_DCICRI     ; $aaPackageName1687_DCICRI     ;
                 ; AA2_$esCc1687_DCICRIN    ; $aaPackageName1687_DCICRIN    ;
                 ; AA2_$esCc1987_ESIADV     ; $aaPackageName1987_ESIADV     ;
                 ; AA2_$esCc1987_ESIREPCAT  ; $aaPackageName1987_ESIREPCAT  ;
                 ; AA2_$esCc1987_ESIREPD    ; $aaPackageName1987_ESIREPD    ;
                 ; AA2_$esCc1987_ESIREPE    ; $aaPackageName1987_ESIREPE    ;
                 ; AA2_$esCc1987_ESIDIAG    ; $aaPackageName1987_ESIDIAG    ;
                 ; AA2_$esCc1987_ESIMASTER  ; $aaPackageName1987_ESIMASTER  ;
                 ; AA2_$esCc1987_TRKOHW1    ; $aaPackageName1987_TRKOHW1    ;
                 ; AA2_$esCc1987_TRKOHW2    ; $aaPackageName1987_TRKOHW2    ;
                 ; AA2_$esCc1987_TRKTRUCK   ; $aaPackageName1987_TRKTRUCK   ;
                 ; AA2_$esCc1987_TSTINFOAW  ; $aaPackageName1987_TSTINFOAW  ;
                 ; AA2_$esCc1987_THLPKW     ; $aaPackageName1987_THLPKW     ;
                 ; AA2_$esCc1987_THLTRK     ; $aaPackageName1987_THLTRK     ;
                 ; AA2_$esCc1987_THLPKWTRK  ; $aaPackageName1987_THLPKWTRK  ;
                 ; AA2_$esCc1687_TSTINFODAT ; $aaPackageName1687_TSTINFODAT ;
                 ; AA2_$esCc1987_TRKUPG     ; $aaPackageName1987_TRUCKUPG   ;
                 ; AA2_$esCc1987_KTS250SD   ; $aaPackageName1987_KTS250     ;
                 ; AA2_$esCc1687_CRR950     ; $aaPackageName1687_CRR950     ;
                 ; AA2_$esCc1987_TRKOHW3    ; $aaPackageName1987_TRKOHW3    ;
                 ; AA2_$esCc1987_ADAS_ONE   ; $aaPackageName1987_ADAS_ONE   ;

INSERT_UPDATE App; code[unique = true]      ; name[lang = es]                                        ; summary[lang = es]                          ; description[lang = es]                      ; $catalogVersion[unique = true];
                 ; AA2_$esCc1687_CSFSA500   ; CompacSoft[plus] FSA 500                               ; $compacFsa500Description                    ; $compacFsa500Description                    ;
                 ; AA2_$esCc1687_CSFSA7XX   ; CompacSoft[plus] FSA 7xx                               ; $compacFsa7xxDescription                    ; $compacFsa7xxDescription                    ;
                 ; AA2_$esCc1687_DCICRI     ; Componentes DCI-CRI                                    ; $criDescription                             ; $criDescription                             ;
                 ; AA2_$esCc1687_DCICRIN    ; Componentes DCI-CRIN                                   ; $crinDescription                            ; $crinDescription                            ;
                 ; AA2_$esCc1987_ESIADV     ; ESI[tronic] 2.0 Avanzado                               ; $packAdvancedDescription                    ; $packAdvancedDescription                    ;
                 ; AA2_$esCc1987_ESIREPCAT  ; Catálogo de recambios                                  ; $packComponentCatDescription                ; $packComponentCatDescription                ;
                 ; AA2_$esCc1987_ESIREPD    ; Reparación de componentes diésel y eléctricos          ; $packComponentRepairDieselDescription       ; $packComponentRepairDieselDescription       ;
                 ; AA2_$esCc1987_ESIREPE    ; Reparación de componentes eléctricos                   ; $packComponentRepairElectricDescription     ; $packComponentRepairElectricDescription     ;
                 ; AA2_$esCc1987_ESIDIAG    ; ESI[tronic] 2.0 Diagnóstico                            ; $packDiagnosticDescription                  ; $packDiagnosticDescription                  ;
                 ; AA2_$esCc1987_ESIMASTER  ; ESI[tronic] 2.0 Master                                 ; $packMasterDescription                      ; $packMasterDescription                      ;
                 ; AA2_$esCc1987_TRKOHW1    ; ESI[tronic] 2.0 Truck Agrícola                         ; $ohw1Description                            ; $ohw1Description                            ;
                 ; AA2_$esCc1987_TRKOHW2    ; ESI[tronic] 2.0 Truck Obra pública                     ; $ohw2Description                            ; $ohw2Description                            ;
                 ; AA2_$esCc1987_TRKTRUCK   ; ESI[tronic] 2.0 Truck                                  ; $packTruckDescription                       ; $packTruckDescription                       ;
                 ; AA2_$esCc1987_TSTINFOAW  ; ESI[tronic] W                                          ; $infoartWDescription                        ; $infoartWDescription                        ;
                 ; AA2_$esCc1987_THLPKW     ; Soporte técnico para turismos                          ; $thlPkwDescription                          ; $thlPkwDescription                          ;
                 ; AA2_$esCc1987_THLTRK     ; Soporte técnico para vehículos industriales            ; $thlTruckDescription                        ; $thlTruckDescription                        ;
                 ; AA2_$esCc1987_THLPKWTRK  ; Soporte técnico para turismos y vehículos industriales ; $thlPkwTruckDescription                     ; $thlPkwTruckDescription                     ;
                 ; AA2_$esCc1687_TSTINFODAT ; Testdata VP-M/CP                                       ; $infoartTestdataDescription                 ; $infoartTestdataDescription                 ;
                 ; AA2_$esCc1987_TRKUPG     ; ESI[tronic] 2.0 Ampliación Truck                       ; $truckUpgradeDescription                    ; $truckUpgradeDescription                    ;
                 ; AA2_$esCc1987_KTS250SD   ; KTS 250 SD ECU Diagnosis                               ; $kts250SDDescription                        ; $kts250SDDescription                        ;
                 ; AA2_$esCc1687_CRR950     ; CRR 950 Diesel Injectors Repair Software               ; $crr950DieselInjectorsRepairSoftDescription ; $crr950DieselInjectorsRepairSoftDescription ;
                 ; AA2_$esCc1987_TRKOHW3    ; Off Highway III (OHW III)                              ; $ohw3Description_en                         ; $ohw3Description_en                         ;
                 ; AA2_$esCc1987_ADAS_ONE   ; ADAS One Solution                                      ; $adasDescription                            ; $adasDescription                            ;

INSERT_UPDATE App; code[unique = true]      ; name[lang = en]                          ; summary[lang = en]                             ; description[lang = en]                         ; $catalogVersion[unique = true];
                 ; AA2_$esCc1687_CSFSA500   ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description_en                    ; $compacFsa500Description_en                    ;
                 ; AA2_$esCc1687_CSFSA7XX   ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription_en                    ; $compacFsa7xxDescription_en                    ;
                 ; AA2_$esCc1687_DCICRI     ; Component DCI-CRI                        ; $criDescription_en                             ; $criDescription_en                             ;
                 ; AA2_$esCc1687_DCICRIN    ; Component DCI-CRIN                       ; $crinDescription_en                            ; $crinDescription_en                            ;
                 ; AA2_$esCc1987_ESIADV     ; ESI[tronic] 2.0 Advanced                 ; $packAdvancedDescription_en                    ; $packAdvancedDescription_en                    ;
                 ; AA2_$esCc1987_ESIREPCAT  ; ESI[tronic] 2.0 ComponentCat D+E         ; $packComponentCatDescription_en                ; $packComponentCatDescription_en                ;
                 ; AA2_$esCc1987_ESIREPD    ; ESI[tronic] 2.0 ComponentRepair D+E      ; $packComponentRepairDieselDescription_en       ; $packComponentRepairDieselDescription_en       ;
                 ; AA2_$esCc1987_ESIREPE    ; ESI[tronic] 2.0 ComponentRepair E        ; $packComponentRepairElectricDescription_en     ; $packComponentRepairElectricDescription_en     ;
                 ; AA2_$esCc1987_ESIDIAG    ; ESI[tronic] 2.0 Diagnostic               ; $packDiagnosticDescription_en                  ; $packDiagnosticDescription_en                  ;
                 ; AA2_$esCc1987_ESIMASTER  ; ESI[tronic] 2.0 Master                   ; $packMasterDescription_en                      ; $packMasterDescription_en                      ;
                 ; AA2_$esCc1987_TRKOHW1    ; ESI[tronic] 2.0 Truck OHW I              ; $ohw1Description_en                            ; $ohw1Description_en                            ;
                 ; AA2_$esCc1987_TRKOHW2    ; ESI[tronic] 2.0 Truck OHW II             ; $ohw2Description_en                            ; $ohw2Description_en                            ;
                 ; AA2_$esCc1987_TRKTRUCK   ; ESI[tronic] 2.0 Truck                    ; $packTruckDescription_en                       ; $packTruckDescription_en                       ;
                 ; AA2_$esCc1987_TSTINFOAW  ; ESI[tronic] W                            ; $infoartWDescription_en                        ; $infoartWDescription_en                        ;
                 ; AA2_$esCc1987_THLPKW     ; Technical Hotline Cars                   ; $thlPkwDescription_en                          ; $thlPkwDescription_en                          ;
                 ; AA2_$esCc1987_THLTRK     ; Technical Hotline Truck                  ; $thlTruckDescription_en                        ; $thlTruckDescription_en                        ;
                 ; AA2_$esCc1987_THLPKWTRK  ; Technical Hotline Car + Truck            ; $thlPkwTruckDescription_en                     ; $thlPkwTruckDescription_en                     ;
                 ; AA2_$esCc1687_TSTINFODAT ; Testdata VP-M/CP                         ; $infoartTestdataDescription_en                 ; $infoartTestdataDescription_en                 ;
                 ; AA2_$esCc1987_TRKUPG     ; ESI[tronic] 2.0 Truck Upgrade            ; $truckUpgradeDescription_en                    ; $truckUpgradeDescription_en                    ;
                 ; AA2_$esCc1987_KTS250SD   ; KTS 250 SD ECU Diagnosis                 ; $kts250SDDescription_en                        ; $kts250SDDescription_en                        ;
                 ; AA2_$esCc1687_CRR950     ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription_en ; $crr950DieselInjectorsRepairSoftDescription_en ;
                 ; AA2_$esCc1987_TRKOHW3    ; Off Highway III (OHW III)                ; $ohw3Description_en                            ; $ohw3Description_en                            ;
                 ; AA2_$esCc1987_ADAS_ONE   ; ADAS One Solution                        ; $adasDescription_en                            ; $adasDescription_en                            ;

INSERT_UPDATE ProductContainer; code[unique = true]       ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$esCc1687_CSFSA500   ; AA2_$esCc1687_CSFSA500
                              ; pcaa_$esCc1687_CSFSA7XX   ; AA2_$esCc1687_CSFSA7XX
                              ; pcaa_$esCc1687_DCICRI     ; AA2_$esCc1687_DCICRI
                              ; pcaa_$esCc1687_DCICRIN    ; AA2_$esCc1687_DCICRIN
                              ; pcaa_$esCc1987_ESIADV     ; AA2_$esCc1987_ESIADV
                              ; pcaa_$esCc1987_ESIREPCAT  ; AA2_$esCc1987_ESIREPCAT
                              ; pcaa_$esCc1987_ESIREPD    ; AA2_$esCc1987_ESIREPD
                              ; pcaa_$esCc1987_ESIREPE    ; AA2_$esCc1987_ESIREPE
                              ; pcaa_$esCc1987_ESIDIAG    ; AA2_$esCc1987_ESIDIAG
                              ; pcaa_$esCc1987_ESIMASTER  ; AA2_$esCc1987_ESIMASTER
                              ; pcaa_$esCc1987_TRKOHW1    ; AA2_$esCc1987_TRKOHW1
                              ; pcaa_$esCc1987_TRKOHW2    ; AA2_$esCc1987_TRKOHW2
                              ; pcaa_$esCc1987_TRKTRUCK   ; AA2_$esCc1987_TRKTRUCK
                              ; pcaa_$esCc1987_TSTINFOAW  ; AA2_$esCc1987_TSTINFOAW
                              ; pcaa_$esCc1987_THLPKW     ; AA2_$esCc1987_THLPKW
                              ; pcaa_$esCc1987_THLTRK     ; AA2_$esCc1987_THLTRK
                              ; pcaa_$esCc1987_THLPKWTRK  ; AA2_$esCc1987_THLPKWTRK
                              ; pcaa_$esCc1687_TSTINFODAT ; AA2_$esCc1687_TSTINFODAT
                              ; pcaa_$esCc1987_TRKUPG     ; AA2_$esCc1987_TRKUPG
                              ; pcaa_$esCc1987_KTS250SD   ; AA2_$esCc1987_KTS250SD
                              ; pcaa_$esCc1687_CRR950     ; AA2_$esCc1687_CRR950
                              ; pcaa_$esCc1987_TRKOHW3    ; AA2_$esCc1987_TRKOHW3
                              ; pcaa_$esCc1987_ADAS_ONE   ; AA2_$esCc1987_ADAS_ONE

INSERT_UPDATE AppLicense; code[unique = true] ; $baseProduct             ; sellerProductId; brimName[lang = en]                            ; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); specifiedPrice; billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; enabledCountries(isocode)[default = $enabledIn]; availabilityStatus(code)[default = PUBLISHED]
                        ; AA2_$esCc1687P15063 ; AA2_$esCc1687_CSFSA500   ; 1687P15063     ; $compacFsa500Full3YBrimName                    ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 210           ;
                        ; AA2_$esCc1687P15060 ; AA2_$esCc1687_CSFSA500   ; 1687P15060     ; $compacFsa500SubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 80            ;
                        ; AA2_$esCc1687P15048 ; AA2_$esCc1687_CSFSA7XX   ; 1687P15048     ; $compacFsa7xxFull3YBrimName                    ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 755           ;
                        ; AA2_$esCc1687P15045 ; AA2_$esCc1687_CSFSA7XX   ; 1687P15045     ; $compacFsa7xxSubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 295           ;
                        ; AA2_$esCc1687P15090 ; AA2_$esCc1687_DCICRI     ; 1687P15090     ; $criSubsS1BrimName                             ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 440           ;
                        ; AA2_$esCc1687P15102 ; AA2_$esCc1687_DCICRI     ; 1687P15102     ; $criSubsM3BrimName                             ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 580           ;
                        ; AA2_$esCc1687P15100 ; AA2_$esCc1687_DCICRIN    ; 1687P15100     ; $crinSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 440           ;
                        ; AA2_$esCc1687P15107 ; AA2_$esCc1687_DCICRIN    ; 1687P15107     ; $crinSubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 580           ;
                        ; AA2_$esCc1987P12843 ; AA2_$esCc1987_ESIADV     ; 1987P12843     ; $packAdvancedFull3YS1BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3540          ;
                        ; AA2_$esCc1987P12840 ; AA2_$esCc1987_ESIADV     ; 1987P12840     ; $packAdvancedSubsS1BrimName                    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1390          ;
                        ; AA2_$esCc1987P12988 ; AA2_$esCc1987_ESIREPCAT  ; 1987P12988     ; $packComponentCatFull3YS1BrimName              ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 945           ;
                        ; AA2_$esCc1987P12998 ; AA2_$esCc1987_ESIREPCAT  ; 1987P12998     ; $packComponentCatSubsS1BrimName                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 370           ;
                        ; AA2_$esCc1987P12973 ; AA2_$esCc1987_ESIREPD    ; 1987P12973     ; $packComponentRepairDieselFull3YS1BrimName     ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1695          ;
                        ; AA2_$esCc1987P12970 ; AA2_$esCc1987_ESIREPD    ; 1987P12970     ; $packComponentRepairDieselSubsS1BrimName       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 665           ;
                        ; AA2_$esCc1987P12993 ; AA2_$esCc1987_ESIREPE    ; 1987P12993     ; $packComponentRepairElectricFull3YS1BrimName   ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 815           ;
                        ; AA2_$esCc1987P12990 ; AA2_$esCc1987_ESIREPE    ; 1987P12990     ; $packComponentRepairElectricSubsS1BrimName     ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 320           ;
                        ; AA2_$esCc1987P12823 ; AA2_$esCc1987_ESIDIAG    ; 1987P12823     ; $packDiagnosticFull3YS1BrimName                ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 2050          ;
                        ; AA2_$esCc1987P12820 ; AA2_$esCc1987_ESIDIAG    ; 1987P12820     ; $packDiagnosticSubsS1BrimName                  ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 805           ;
                        ; AA2_$esCc1987P12913 ; AA2_$esCc1987_ESIMASTER  ; 1987P12913     ; $packMasterFull3YS1BrimName                    ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 4905          ;
                        ; AA2_$esCc1987P12910 ; AA2_$esCc1987_ESIMASTER  ; 1987P12910     ; $packMasterSubsS1BrimName                      ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1925          ;
                        ; AA2_$esCc1987P12263 ; AA2_$esCc1987_TRKOHW1    ; 1987P12263     ; $ohw1Full3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1265          ;
                        ; AA2_$esCc1987P12260 ; AA2_$esCc1987_TRKOHW1    ; 1987P12260     ; $ohw1SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 520           ;
                        ; AA2_$esCc1987P12262 ; AA2_$esCc1987_TRKOHW1    ; 1987P12262     ; $ohw1SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 640           ;
                        ; AA2_$esCc1987P12265 ; AA2_$esCc1987_TRKOHW1    ; 1987P12265     ; $ohw1Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1625          ;
                        ; AA2_$esCc1987P12280 ; AA2_$esCc1987_TRKOHW2    ; 1987P12280     ; $ohw2Full3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 2290          ;
                        ; AA2_$esCc1987P12278 ; AA2_$esCc1987_TRKOHW2    ; 1987P12278     ; $ohw2SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 930           ;
                        ; AA2_$esCc1987P12275 ; AA2_$esCc1987_TRKOHW2    ; 1987P12275     ; $ohw2SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1050          ;
                        ; AA2_$esCc1987P12276 ; AA2_$esCc1987_TRKOHW2    ; 1987P12276     ; $ohw2Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 2650          ;
                        ; AA2_$esCc1987P12402 ; AA2_$esCc1987_TRKTRUCK   ; 1987P12402     ; $packTruckFull3YS1BrimName                     ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3010          ;
                        ; AA2_$esCc1987P12400 ; AA2_$esCc1987_TRKTRUCK   ; 1987P12400     ; $packTruckSubsS1BrimName                       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1230          ;
                        ; AA2_$esCc1987P12936 ; AA2_$esCc1987_TRKTRUCK   ; 1987P12936     ; $packTruckSubsM3BrimName                       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1350          ;
                        ; AA2_$esCc1987P12937 ; AA2_$esCc1987_TRKTRUCK   ; 1987P12937     ; $packTruckFull3YM3BrimName                     ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 3370          ;
                        ; AA2_$esCc1987P12412 ; AA2_$esCc1987_TRKTRUCK   ; 1987P12412     ; $packTruckFullBrimName                         ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 1785          ;
                        ; AA2_$esCc1987P12503 ; AA2_$esCc1987_TSTINFOAW  ; 1987P12503     ; $infoartWFull3YBrimName                        ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 1610          ;
                        ; AA2_$esCc1987P12500 ; AA2_$esCc1987_TSTINFOAW  ; 1987P12500     ; $infoartWSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; 630           ;
                        ; AA2_$esCc1987P13523 ; AA2_$esCc1987_THLPKW     ; 1987P13523     ; $thlPkwFull3YBrimName                          ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 1260          ;
                        ; AA2_$esCc1987729274 ; AA2_$esCc1987_THLPKW     ; 1987729274     ; $thlPkwSubsBrimName                            ;                                          ; runtime_subs_unlimited ; <ignore>        ; 460           ;
                        ; AA2_$esCc1987P13533 ; AA2_$esCc1987_THLPKWTRK  ; 1987P13533     ; $thlPkwTrkFull3YBrimName                       ;                                          ; runtime_full_3y        ; <ignore>        ; 2550          ;
                        ; AA2_$esCc1987P13528 ; AA2_$esCc1987_THLTRK     ; 1987P13528     ; $thlTrkFull3YBrimName                          ;                                          ; runtime_full_3y        ; <ignore>        ; 1650          ;
                        ; AA2_$esCc1987P13515 ; AA2_$esCc1987_THLTRK     ; 1987P13515     ; $thlTrkSubsBrimName                            ;                                          ; runtime_subs_unlimited ; <ignore>        ; 640           ;
                        ; AA2_$esCc1987P13516 ; AA2_$esCc1987_THLPKWTRK  ; 1987P13516     ; $thlPkwTrkSubsBrimName                         ;                                          ; runtime_subs_unlimited ; <ignore>        ; 940           ;
                        ; AA2_$esCc1687P15015 ; AA2_$esCc1687_TSTINFODAT ; 1687P15015     ; $infoartTestdataSubsBrimName                   ;                                          ; runtime_subs_unlimited ; <ignore>        ; 315           ;
                        ; AA2_$esCc1987P12404 ; AA2_$esCc1987_TRKUPG     ; 1987P12404     ; $truckUpgradeSubsS1BrimName                    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1045          ;
                        ; AA2_$esCc1987P12140 ; AA2_$esCc1987_TRKUPG     ; 1987P12140     ; $truckUpgradeFull3YS1BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 2550          ;
                        ; AA2_$esCc1987P12359 ; AA2_$esCc1987_TRKUPG     ; 1987P12359     ; $truckUpgradeSubsM3BrimName                    ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1165          ;
                        ; AA2_$esCc1987P12364 ; AA2_$esCc1987_TRKUPG     ; 1987P12364     ; $truckUpgradeFull3YM3BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 2910          ;
                        ; AA2_$esCc1987P12051 ; AA2_$esCc1987_ESIDIAG    ; 1987P12051     ; $packDiagnosticFullOneTimeBrimName             ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 780           ;
                        ; AA2_$esCc1987P12389 ; AA2_$esCc1987_KTS250SD   ; 1987P12389     ; $kts250SDFullBrimName                          ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 730           ;
                        ; AA2_$esCc1987P12385 ; AA2_$esCc1987_KTS250SD   ; 1987P12385     ; $kts250SDSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; 710           ;
                        ; AA2_$esCc1687P15137 ; AA2_$esCc1687_CRR950     ; 1687P15137     ; $crr950DieselInjectorsRepairSoftwareBrimName1L ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 100           ;
                        ; AA2_$esCc1687P15139 ; AA2_$esCc1687_CRR950     ; 1687P15139     ; $crr950DieselInjectorsRepairSoftwareBrimName3L ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 200           ;
                        ; AA2_$esCc1987P12254 ; AA2_$esCc1987_TRKOHW3    ; 1987P12254     ; $ohw3S1BrimName                                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 360           ;
                        ; AA2_$esCc1987P12257 ; AA2_$esCc1987_TRKOHW3    ; 1987P12257     ; $ohw3M3BrimName                                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 456           ;
                        ; AA2_$esCc1987P12255 ; AA2_$esCc1987_TRKOHW3    ; 1987P12255     ; $ohw3Single3YearBrimName                       ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 888           ;
                        ; AA2_$esCc1987P12256 ; AA2_$esCc1987_TRKOHW3    ; 1987P12256     ; $ohw3Multi3YearBrimName                        ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1176          ;
                        ; AA2_$esCc1987P12515 ; AA2_$esCc1987_ADAS_ONE   ; 1987P12515     ; $adasSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 0.01          ; $enabledIn
                        ; AA2_$esCc1987P12517 ; AA2_$esCc1987_ADAS_ONE   ; 1987P12517     ; $adasFull3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 0.01          ; $enabledIn
                        ; AA2_$esCc1687P15170 ; AA2_$esCc1687_CSFSA500   ; 1687P15170     ; $fsa5xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$esCc1687P15173 ; AA2_$esCc1687_CSFSA500   ; 1687P15173     ; $fsa5xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;
                        ; AA2_$esCc1687P15160 ; AA2_$esCc1687_CSFSA7XX   ; 1687P15160     ; $fsa7xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$esCc1687P15163 ; AA2_$esCc1687_CSFSA7XX   ; 1687P15163     ; $fsa7xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;

# The licenses below were discontinued by MA and should be available only for the contract migration.
UPDATE AppLicense; code[unique = true] ; $catalogVersion[unique = true]; availabilityStatus(code)[default = UNPUBLISHED]
                 ; AA2_$esCc1987P12051 ;
                 ; AA2_$esCc1987P12389 ;
                 ; AA2_$esCc1987P12412 ;

UPDATE AppLicense; code[unique = true] ; userGroups(uid)                                         ; $catalogVersion[unique = true];
                 ; AA2_$esCc1687P15063 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1687P15060 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1687P15048 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1687P15045 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1687P15090 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1687P15102 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1687P15100 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1687P15107 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12843 ; IDW000,WD0001,KA0004,KA0005,KA0006                      ;
                 ; AA2_$esCc1987P12840 ; IDW000,WD0001,KA0004,KA0005,KA0006                      ;
                 ; AA2_$esCc1987P12988 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12998 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12973 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12970 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12993 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12990 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12823 ; IDW000,WD0001,KA0004,KA0005,KA0006                      ;
                 ; AA2_$esCc1987P12820 ; IDW000,WD0001,KA0002,KA0004,KA0005,KA0006,KA0007        ;
                 ; AA2_$esCc1987P12913 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12910 ; IDW000,WD0001,KA0002                                    ;
                 ; AA2_$esCc1987P12263 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12260 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12262 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12265 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12280 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12278 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12275 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12276 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12402 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12400 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12936 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12937 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12412 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12503 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12500 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P13523 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987729274 ; IDW000,WD0001,KA0005                                    ;
                 ; AA2_$esCc1987P13533 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P13528 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P13515 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P13516 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1687P15015 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12404 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12140 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12359 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12364 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12051 ; IDW000,WD0001,KA0006                                    ;
                 ; AA2_$esCc1987P12389 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12385 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1687P15137 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1687P15139 ; IDW000,WD0001                                           ;
                 ; AA2_$esCc1987P12254 ; IIDW000, WD0001, KA0002, KA0004, KA0005, KA0006, KA0007 ;
                 ; AA2_$esCc1987P12257 ; IIDW000, WD0001, KA0002, KA0004, KA0005, KA0006, KA0007 ;
                 ; AA2_$esCc1987P12255 ; IIDW000, WD0001, KA0002, KA0004, KA0005, KA0006, KA0007 ;
                 ; AA2_$esCc1987P12256 ; IIDW000, WD0001, KA0002, KA0004, KA0005, KA0006, KA0007 ;
# The group assignment for the ADAS ONE is most probably not final. we're missing the prices for now
                 ; AA2_$esCc1987P12515 ; IDW000                                                  ;
                 ; AA2_$esCc1987P12517 ; IDW000                                                  ;
                 ; AA2_$esCc1687P15163 ; IDW000                                                  ;
                 ; AA2_$esCc1687P15170 ; IDW000                                                  ;
                 ; AA2_$esCc1687P15160 ; IDW000                                                  ;
                 ; AA2_$esCc1687P15173 ; IDW000                                                  ;


INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$esCc1687P15063                          ; 210  ;
                      ; AA2_$esCc1687P15060                          ; 80   ;
                      ; AA2_$esCc1687P15048                          ; 755  ;
                      ; AA2_$esCc1687P15045                          ; 295  ;
                      ; AA2_$esCc1687P15090                          ; 440  ;
                      ; AA2_$esCc1687P15102                          ; 580  ;
                      ; AA2_$esCc1687P15100                          ; 440  ;
                      ; AA2_$esCc1687P15107                          ; 580  ;
                      ; AA2_$esCc1987P12843                          ; 3540 ;
                      ; AA2_$esCc1987P12840                          ; 1390 ;
                      ; AA2_$esCc1987P12988                          ; 945  ;
                      ; AA2_$esCc1987P12998                          ; 370  ;
                      ; AA2_$esCc1987P12973                          ; 1695 ;
                      ; AA2_$esCc1987P12970                          ; 665  ;
                      ; AA2_$esCc1987P12993                          ; 815  ;
                      ; AA2_$esCc1987P12990                          ; 320  ;
                      ; AA2_$esCc1987P12823                          ; 2050 ;
                      ; AA2_$esCc1987P12820                          ; 805  ;
                      ; AA2_$esCc1987P12913                          ; 4905 ;
                      ; AA2_$esCc1987P12910                          ; 1925 ;
                      ; AA2_$esCc1987P12263                          ; 1265 ;
                      ; AA2_$esCc1987P12260                          ; 520  ;
                      ; AA2_$esCc1987P12262                          ; 640  ;
                      ; AA2_$esCc1987P12265                          ; 1625 ;
                      ; AA2_$esCc1987P12280                          ; 2290 ;
                      ; AA2_$esCc1987P12278                          ; 930  ;
                      ; AA2_$esCc1987P12275                          ; 1050 ;
                      ; AA2_$esCc1987P12276                          ; 2650 ;
                      ; AA2_$esCc1987P12402                          ; 3010 ;
                      ; AA2_$esCc1987P12400                          ; 1230 ;
                      ; AA2_$esCc1987P12936                          ; 1350 ;
                      ; AA2_$esCc1987P12937                          ; 3370 ;
                      ; AA2_$esCc1987P12412                          ; 1785 ;
                      ; AA2_$esCc1987P12503                          ; 1610 ;
                      ; AA2_$esCc1987P12500                          ; 630  ;
                      ; AA2_$esCc1987P13523                          ; 1260 ;
                      ; AA2_$esCc1987729274                          ; 460  ;
                      ; AA2_$esCc1987P13533                          ; 2550 ;
                      ; AA2_$esCc1987P13528                          ; 1650 ;
                      ; AA2_$esCc1987P13515                          ; 640  ;
                      ; AA2_$esCc1987P13516                          ; 940  ;
                      ; AA2_$esCc1687P15015                          ; 315  ;
                      ; AA2_$esCc1987P12404                          ; 1045 ;
                      ; AA2_$esCc1987P12140                          ; 2550 ;
                      ; AA2_$esCc1987P12359                          ; 1165 ;
                      ; AA2_$esCc1987P12364                          ; 2910 ;
                      ; AA2_$esCc1987P12051                          ; 780  ;
                      ; AA2_$esCc1987P12389                          ; 730  ;
                      ; AA2_$esCc1987P12385                          ; 710  ;
                      ; AA2_$esCc1687P15137                          ; 100  ;
                      ; AA2_$esCc1687P15139                          ; 200  ;
                      ; AA2_$esCc1987P12254                          ; 360  ;
                      ; AA2_$esCc1987P12257                          ; 456  ;
                      ; AA2_$esCc1987P12255                          ; 1110 ;
                      ; AA2_$esCc1987P12256                          ; 1470 ;
                      ; AA2_$esCc1987P12515                          ; 0.01 ;
                      ; AA2_$esCc1987P12517                          ; 0.01 ;
                      ; AA2_$esCc1687P15163                          ; 0.01 ;
                      ; AA2_$esCc1687P15170                          ; 0.01 ;
                      ; AA2_$esCc1687P15160                          ; 0.01 ;
                      ; AA2_$esCc1687P15173                          ; 0.01 ;


INSERT_UPDATE App; code[unique = true]     ; boms(code)                                                                                           ; $catalogVersion[unique = true]
                 ; AA2_$esCc1987_ESIADV    ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe                       ;
                 ; AA2_$esCc1987_ESIREPCAT ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE                                                                     ;
                 ; AA2_$esCc1987_ESIREPD   ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE,MAT_$aaCcK3                                                         ;
                 ; AA2_$esCc1987_ESIREPE   ; MAT_$aaCcA,MAT_$aaCcE,MAT_$aaCcK2                                                                    ;
                 ; AA2_$esCc1987_ESIDIAG   ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB                                                  ;
                 ; AA2_$esCc1987_ESIMASTER ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe,MAT_$aaCcM,MAT_$aaCcP ;
                 ; AA2_$esCc1987_TRKOHW1   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                  ;
                 ; AA2_$esCc1987_TRKOHW2   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                  ;
                 ; AA2_$esCc1987_TRKTRUCK  ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                  ;
                 ; AA2_$esCc1987_TRKUPG    ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                  ;
                 ; AA2_$esCc1987_TRKOHW3   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK                  ;

INSERT_UPDATE App; code[unique = true]      ; contentModules(code)                                                                         ; $catalogVersion[unique = true];
                 ; AA2_$esCc1687_CSFSA500   ; CM_$aaCcCSFSA5                                                                               ;
                 ; AA2_$esCc1687_CSFSA7XX   ; CM_$aaCcCSS,CM_$aaCcCSK                                                                      ;
                 ; AA2_$esCc1687_DCICRI     ; CM_$aaCcDCICRI                                                                               ;
                 ; AA2_$esCc1687_DCICRIN    ; CM_$aaCcDCICRIN                                                                              ;
                 ; AA2_$esCc1987_ESIADV     ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe                     ;
                 ; AA2_$esCc1987_ESIREPCAT  ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE                                                                ;
                 ; AA2_$esCc1987_ESIREPD    ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE,CM_$aaCcK3                                                     ;
                 ; AA2_$esCc1987_ESIREPE    ; CM_$aaCcA,CM_$aaCcE,CM_$aaCcK2                                                               ;
                 ; AA2_$esCc1987_ESIDIAG    ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB                                              ;
                 ; AA2_$esCc1987_ESIMASTER  ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe,CM_$aaCcM,CM_$aaCcP ;
                 ; AA2_$esCc1987_TRKOHW1    ; CM_$aaCcETOHW1                                                                               ;
                 ; AA2_$esCc1987_TRKOHW2    ; CM_$aaCcETOHW2                                                                               ;
                 ; AA2_$esCc1987_TRKTRUCK   ; CM_$aaCcETruck                                                                               ;
                 ; AA2_$esCc1987_TSTINFOAW  ; CM_$aaCcEW                                                                                   ;
                 ; AA2_$esCc1987_THLPKW     ; CM_$aaCcPKWTHL                                                                               ;
                 ; AA2_$esCc1987_THLTRK     ; CM_$aaCcPKWTHL                                                                               ;
                 ; AA2_$esCc1987_THLPKWTRK  ; CM_$aaCcTHLCarTruck                                                                          ;
                 ; AA2_$esCc1687_TSTINFODAT ; CM_$aaCcTVPMCP                                                                               ;
                 ; AA2_$esCc1987_TRKUPG     ; CM_$aaCcTRKUPG                                                                               ;
                 ; AA2_$esCc1987_KTS250SD   ; CM_$aaCcKTS250ECUD                                                                           ;

INSERT_UPDATE App; code[unique = true]   ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$esCc1687_CRR950  ; CM_$aaCcCRR950
                 ; AA2_$esCc1987_TRKOHW3 ; CM_$aaCcETOHW3

UPDATE AppLicense; code[unique = true] ; $catalogVersion[unique = true]; contentModules(code)[default = CM_$aaCcSDSDA];
                 ; AA2_$esCc1987P12051 ;


INSERT_UPDATE App; code[unique = true]      ; $supercategories; $catalogVersion[unique = true];
                 ; AA2_$esCc1687_CSFSA500   ; cat_201
                 ; AA2_$esCc1687_CSFSA7XX   ; cat_201
                 ; AA2_$esCc1687_DCICRI     ; cat_401
                 ; AA2_$esCc1687_DCICRIN    ; cat_401
                 ; AA2_$esCc1987_ESIADV     ; cat_10101
                 ; AA2_$esCc1987_ESIREPCAT  ; cat_1010101
                 ; AA2_$esCc1987_ESIREPD    ; cat_1010101
                 ; AA2_$esCc1987_ESIREPE    ; cat_1010101
                 ; AA2_$esCc1987_ESIDIAG    ; cat_10101
                 ; AA2_$esCc1987_ESIMASTER  ; cat_10101
                 ; AA2_$esCc1987_TRKOHW1    ; cat_10102
                 ; AA2_$esCc1987_TRKOHW2    ; cat_10102
                 ; AA2_$esCc1987_TRKTRUCK   ; cat_10102
                 ; AA2_$esCc1987_TSTINFOAW  ; cat_40101
                 ; AA2_$esCc1987_THLPKW     ; cat_1010102
                 ; AA2_$esCc1987_THLTRK     ; cat_1010202
                 ; AA2_$esCc1987_THLPKWTRK  ; cat_1010102,cat_1010202
                 ; AA2_$esCc1687_TSTINFODAT ; cat_40101
                 ; AA2_$esCc1987_TRKUPG     ; cat_10102
                 ; AA2_$esCc1987_KTS250SD   ; cat_1010104
                 ; AA2_$esCc1687_CRR950     ; cat_401
                 ; AA2_$esCc1987_TRKOHW3    ; cat_10102
                 ; AA2_$esCc1987_ADAS_ONE   ; cat_501         ;

INSERT_UPDATE App; code[unique = true]      ; eulaContainers(code); $catalogVersion[unique = true]
                 ; AA2_$esCc1687_CSFSA500   ; AA2_FSA
                 ; AA2_$esCc1687_CSFSA7XX   ; AA2_FSA
                 ; AA2_$esCc1687_DCICRI     ; AA2_DCICRI
                 ; AA2_$esCc1687_DCICRIN    ; AA2_DCICRIN
                 ; AA2_$esCc1987_TSTINFOAW  ; AA2_ESItronic
                 ; AA2_$esCc1687_TSTINFODAT ; AA2_ESItronic
                 ; AA2_$esCc1987_KTS250SD   ; AA2_ESItronic
                 ; AA2_$esCc1687_CRR950     ; AA2_ESItronic
