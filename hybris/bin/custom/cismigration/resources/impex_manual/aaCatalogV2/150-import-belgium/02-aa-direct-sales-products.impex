#% impex.setLocale( Locale.ENGLISH );

# numerical code for BE, used as prefix for product code
$beCc = 032
$aaCc = 040
$aaPackageName = com.sast.aa.be.

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf

# please make sure, to use right DELEVELOPER COMPANY for Belgium in the right environment.
# live
# $companyId = 2b03d151-5925-44a1-b010-3c555d375f8c
# demo
# $companyId = eb4aef89-91f7-4e05-940a-ef02ff09865e
# dev
#$companyId = 2da0e2ae-770f-4528-adef-8dbc4b328af2
# local
$companyId = 31ea1c14-c221-4764-ab6c-8c0a6c0cdcbf

$packDiagnosticDescription_fr = L'essentiel pour le diagnostic, la réparation et l'entretien professionnels. Il permet un diagnostic électronique efficace et offre un large éventail d'autres fonctions pour tous les véhicules couverts. Fonction SDA incluse.
$packDiagnosticDescription_nl = Instappakket voor een complete diagnose en op ervaring gebaseerde reparaties (bekende fouten databank).
$packDiagnosticDescription_de = Der Einstieg in die professionelle Diagnose, Reparatur und Wartung. Es ermöglicht eine kompetente elektrische Diagnose und bietet eine Vielzahl weiterer Funktionen für alle abgedeckten Fahrzeuge.
$packDiagnosticDescription_en = The entry into professional diagnosis, repair and maintenance. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.
$packAdvancedDescription_fr = Le niveau supérieur pour des diagnostics professionnels. En plus du Pack Diagnostic, des instructions et des manuels sont inclus. Connected Repair fournit l'historique de l'entretien et de la maintenance ainsi que des informations sur les réparations. Fonction SDA incluse.
$packAdvancedDescription_nl = Combinatiepakket van diagnose en technische informatie voor succesvolle reparaties. Idem aan Diagnostic-pakket, met bijkomstig technsiche informatie zoals foutopsporingshandleidingen, inbouwposities, in-en uitbouwinstructies, elektrische schema's, enz.
$packAdvancedDescription_de = Die nächste Stufe der professionellen Werkstattausrüstung. Zusätzlich zum Paket Diagnostic sind Anleitungen und Handbücher enthalten. Connected Repair liefert die Service- und Wartungshistorie sowie Reparaturinformationen.
$packAdvancedDescription_en = The next level of professional workshop equipment. In addition to the Diagnostic package, instructions and manuals are included. Connected Repair provides service and maintenance history and repair information.
$packMasterDescription_fr = Le logiciel complet pour le diagnostic professionnel des véhicules. Il fournit toutes les informations nécessaires au diagnostic, à la réparation, à l'entretien, aux pièces de rechange, à la documentation et à la gestion des données. L'assistance technique vous aide à trouver des solutions. Fonction SDA incluse.
$packMasterDescription_nl = Compleet pakket voor onderhoud en reparatie. Idem aan Advanced-pakket, met bijkomstig onderhoudsinformatie en elektrische schema's van comfortelektronica.
$packMasterDescription_de = Das vollumfängliche Paket für professionelle Fahrzeugdiagnose. Es liefert alle nötigen Informationen zur Diagnose, Reparatur, Wartung, Ersatzteile, Dokumentation und Datenmanagement. Der Technische Support unterstützt Sie bei der Lösungsfindung.
$packMasterDescription_en = The fully comprehensive package for professional vehicle diagnostics. It provides all the necessary information for diagnosis, repair, maintenance, spare parts, documentation and data management. Technical Support assists you in finding solutions.

$packComponentCatDescription_fr = Le catalogue des pièces détachées comprend les applications, les fonctions et l'équipement automobile ainsi que les pièces détachées diesel et les pièces détachées électriques, y compris les archives et la pièce détachée électrique ESI[tronic]-F.
$packComponentCatDescription_nl = Het Component Catalog pakket omvat de complete Bosch-onderdelencataloog, alsook de diesel- en elektro-onderdeellijsten met gedetailleerde explosietekeningen van dieselpompen en -injectoren, starters en alternatoren/dynamo's.
$packComponentCatDescription_de = Das Ersatzteile-Katalog-Paket beinhaltet die Anwendungen, Funktionen und Kfz-Ausrüstung sowie die Diesel Ersatzteile und Elektrik Ersatzteile inkl. Archiv und Elektrik Ersatzteil ESI[tronic]-F.
$packComponentCatDescription_en = The spare parts catalog package includes the applications, functions and automotive equipment as well as the diesel spare parts and electrical spare parts incl. archive and electrical spare part ESI[tronic]-F.
$packComponentRepairDieselDescription_fr = Le pack Réparation Diesel & Electrique fournit des informations sur les pièces détachées et la réparation des composants diesel et électrique. Il permet d'identifier le véhicule, l'équipement automobile Bosch et comprend des instructions de réparation et des informations sur l'entretien.
$packComponentRepairDieselDescription_nl = Compleet pakket voor componentreparatie van diesel- en elektrocomponenten. Omvat de onderdelencataloog, wisselstukken, alsmede de reparatiehandleidingen en service-informatie voor diesel- en elektrocomponenten.
$packComponentRepairDieselDescription_de = Das Repair Diesel-Paket bietet Informationen zu Ersatzteilen und zur Reparatur von Diesel- und Elektrik-Komponenten. Es ermöglicht die Identifikation des Fahrzeugs, der Bosch-Kfz-Ausrüstung und beinhaltet Reparaturanleitungen & Serviceinformationen.
$packComponentRepairDieselDescription_en = The Repair Diesel package provides information on spare parts and repair of diesel and electrical components. It allows identification of the vehicle, Bosch automotive equipment and includes repair instructions & service information.
$packComponentRepairElectricDescription_fr = En raison du nombre croissant de modèles de véhicules, il est difficile pour les ateliers de disposer d'informations actualisées sur les systèmes électriques des véhicules. Le pack Réparation Electrique fournit une assistance avec des données sur les pièces détachées pour les systèmes électriques des voitures dans un format clair.
$packComponentRepairElectricDescription_nl = Elektro reparatiepakket. Omvat de onderdelencataloog, wisselstukken, alsmede de reparatiehandleidingen en service-informatie voor elektrocomponenten (startmotoren en alternatoren/dynamo's).
$packComponentRepairElectricDescription_de = Der steigende Umfang an Fahrzeugmodellen erschwert es Werkstätten, laufend aktuelle Informationen für Aggregate der Fahrzeugelektrik parat zu haben. Das Repair Elektrik Paket unterstützt mit Ersatzteildaten zur Autoelektrik in übersichtlicher Form.
$packComponentRepairElectricDescription_en = The increasing number of vehicle models makes it difficult for workshops to have up-to-date information on vehicle electrical systems at their fingertips. The Repair Electrics Package provides support with spare parts data on car electrics in a clear format.

$packTruckDescription_fr = Le pack Truck permet aux ateliers d'effectuer un diagnostic efficace, un entretien complet et une réparation efficace de tous les véhicules utilitaires légers et poids lourds, remorques, utilitaires et autobus courants.
$packTruckDescription_nl = Diagnose, onderhoudsplannen, elektrische schema's en technische informatie voor vrachtwagens, aanhangwagens, bussen en lichte bedrijfsvoertuigen.
$packTruckDescription_de = Das Paket Truck unterstützt Werkstätten bei der zuverlässigen Diagnose, kompletten Wartung und effizienten Reparatur von allen gängigen leichten und schweren Nutzfahrzeugen, Anhängern, Transportern und Bussen.
$packTruckDescription_en = The Truck package supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.

$ohw1Description_fr = Le pack de diagnostic pour les machines agricoles fournit des informations sur le diagnostic, l'entretien et la réparation des véhicules agricoles. Il comprend, entre autres, des fonctions de réglage et de paramétrage des systèmes hydrauliques.
$ohw1Description_nl = Off-Highway 1 softwarepakket. Diagnose en technische informatie voor landbouwvoertuigen.
$ohw1Description_de = In dem Diagnose Paket Landmaschinen stehen Informationen zur Diagnose, Wartung und Reparatur von landwirtschaftlich genutzten Fahrzeugen zur Verfügung. Enthalten sind unter anderem Einstell- und Parametrierfunktionen an Hydrauliksystemen.
$ohw1Description_en = The Agricultural Machinery Diagnostic Package provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw2Description_fr = Le pack de diagnostic pour les engins de construction et les machines fournit des informations sur le diagnostic, l'entretien et la réparation des véhicules agricoles. Il comprend notamment des fonctions de réglage et de paramétrage des systèmes hydrauliques.
$ohw2Description_nl = Off-Highway 2 softwarepakket. Diagnose en technische informatie voor grondverzetmachines, stationaire motoren, veegmachines, enz.
$ohw2Description_de = In dem Diagnose Paket Baumaschinen und Motoren stehen Informationen zur Diagnose, Wartung und Reparatur von landwirtschaftlich genutzten Fahrzeugen zur Verfügung. Enthalten sind unter anderem Einstell- und Parametrierfunktionen an Hydrauliksystemen.
$ohw2Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.

$thlPkwDescription_fr = Vous avez besoin d'une assistance technique pour l'entretien ou la réparation d'une voiture, ou simplement d'un deuxième avis fiable ? Contactez notre équipe d'assistance et obtenez des solutions rapides et fiables.
$thlPkwDescription_nl = Heb je technische ondersteuning nodig voor het onderhoud of de reparatie van een auto, of gewoon een betrouwbare tweede opinie? Neem dan contact op met ons supportteam en krijg snelle en adequate ondersteuning.
$thlPkwDescription_de = Sie benötigen technische Unterstützung beim Warten oder Reparieren eines Pkw oder einfach nur eine zuverlässige zweite Meinung? Dann wenden Sie sich an unser Support-Team und erhalten Sie schnelle und fundierte Lösungen.
$thlPkwDescription_en = Do you need technical support to maintain or repair a car, or simply a reliable second opinion? Then contact our support team and get fast and sound solutions.

$alltrucksDescription_fr = Le logiciel Alltrucks contient des informations importantes sur les véhicules utilitaires, telles que la série du modèle, les performances, l'identification du moteur et la configuration des essieux. Inclut ESI[tronic] Truck, NEO | orange de Knorr-Bremse et ZF-TESTMAN de ZF.
$alltrucksDescription_nl = Unieke combinatie van Bosch ESI[tronic] Truck, Knorr-Bremse Neo Orange en ZF-Testman software. Voorbehouden aan Alltruck-garagebedrijven.
$alltrucksDescription_de = Die Alltrucks Diagnose-Software beinhaltet wichtige Informationen zu den Nutzfahrzeugen wie Modellreihe, Leistung, Motorkennzeichnung sowie Achskonfiguration. Beinhaltet ESI[tronic] Truck, NEO | orange von Knorr-Bremse.
$alltrucksDescription_en = The Alltrucks software contains important information about commercial vehicles such as model series, performance, engine identification as well as axle configuration. Includes ESI[tronic] Truck, NEO | orange from Knorr-Bremse.
$compacFsa500Description_fr = Le CompacSoft[plus] pour FSA 500 est équipé de tests de composants prédéfinis et peut être connecté à des systèmes existants, ou être utilisé pour étendre progressivement votre système de test en atelier.
$compacFsa500Description_nl = Software voor de FSA 500 draadloze meetmodule, inclusief: ong. 5 motorteststappen, ong. 30 voorgedefinieerde componententests, URI incl. temperatuur- en drukmetingen, primaire en secundaire ontstekingsoscilloscoop, universele 4-kanaalsoscilloscoop incl. opslagfunctie in referentiesignalendatabank, signaalgenerator.
$compacFsa500Description_de = Die CompacSoft[plus] für FSA 500 ist mit voreingestellten Komponententests ausgestattet und kann an vorhandene Systeme angeschlossen, sowie für den schrittweisen Ausbau Ihres Werkstatt-Testsystems genutzt werden.
$compacFsa500Description_en = The CompacSoft[plus] for FSA 500 is equipped with preset component tests and can be connected to existing systems, as well as used to gradually expand your workshop test system.
$compacFsa7xxDescription_fr = Avec CompacSoft[plus] pour FSA 7xx, le confort pour toutes les tâches de mesure sur le véhicule est encore accru par les étapes de test guidées par le menu, les valeurs de consigne spécifiques au véhicule en option, ainsi que l'affichage des valeurs réelles.
$compacFsa7xxDescription_nl = Software voor de FSA 7xx meetapparatuur, inclusief: ong. 10 motorteststappen incl. referentiewaarden, ong. 50 voorgedefinieerde componententests, URI incl. temperatuur- en drukmetingen, primaire en secundaire ontstekingsoscilloscoop, universele 2-kanaalsoscilloscoop incl. opslagfunctie in referentiesignalendatabank, signaalgenerator.
$compacFsa7xxDescription_de = Mit CompacSoft[plus] für FSA 7xx wird der Komfort für alle Messaufgaben am Fahrzeug durch die menügeführten Prüfschritte, den optionalen fahrzeugspezifischen Sollwerten, sowie der Anzeige der Ist-Werte noch weiter erhöht.
$compacFsa7xxDescription_en = With CompacSoft[plus] for FSA 7xx, the comfort for all measuring tasks on the vehicle is further increased by the menu-guided test steps, the optional vehicle-specific set values, as well as the display of the actual values.

$criDescription_fr = Le logiciel ESI[tronic] CRI pour DCI 700 fournit des données actualisées, garantit le bon déroulement des processus et inclut les essais d'injecteurs piézo pour les systèmes Common Rail Bosch VL.
$criDescription_nl = Software voor de DCI 700 dieselinjectortestbank, incl. online updates voor commonrailinjectoren (piëzo en magneetventiel) van personenwagens
$criDescription_de = CRI-Lizenz stellt den Anwender während der Vertragslaufzeit die DCI-Software und CRI-Datenbank zur Verfügung für den Betrieb eines DCI 200 oder DCI 700. Die Datenbank beinhaltet die aktuelle zur Verfügung Bosch-CRI-Injektoren für Pkws und kleine Transportern. Updates werden während der Vertragslaufzeit über das Internet zur Verfügung gestellt.
$criDescription_en = CRI for DCI 700 software provides up-to-date data, ensures smooth processes and includes testing of piezo injectors for common rail systems.
$crinDescription_fr = Le logiciel ESI[tronic] CRIN pour DCI 700 fournit des données actualisées, garantit le bon déroulement des processus et permet de tester les injecteurs à électrovanne pour les systèmes Common Rail Bosch PL.
$crinDescription_nl = Software voor de DCI 700 dieselinjectortestbank, incl. online updates voor commonrailinjectoren van bedrijfsvoertuigen
$crinDescription_de = CRIN-Lizenz stellt den Anwender während der Vertragslaufzeit die DCI-Software und CRIN-Datenbank zur Verfügung für den Betrieb eines DCI 200 oder DCI 700. Die Datenbank beinhaltet die aktuelle zur Verfügung Bosch-CRIN-Injektoren für Nutzfahrzeuge (Inklusiv CRIN 4.2 Injektoren). Updates werden während der Vertragslaufzeit über das Internet zur Verfügung gestellt.
$crinDescription_en = CRIN for DCI 700 software provides up-to-date data, ensures smooth processes and includes the testing of solenoid valve injectors for common rail systems.

# TODO: UPDATE TRANSLATION
$adasDescription_fr = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.
$adasDescription_nl = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.
$adasDescription_de = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.
$adasDescription_en = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.


$coReDescription_fr = Bosch Connected Repair est un logiciel qui relie les équipements de l'atelier, le véhicule et les données de réparation. Que ce soit en cas de dysfonctionnement ou pour le stockage de données et d'images, CoRe OnLine a été adapté aux besoins des utilisateurs, et respecte le règlement de base sur la protection des données.
$coReDescription_nl = Bosch Connected Repair is software die werkplaatsapparatuur, voertuig- en reparatiegegevens met elkaar verbindt. Of het nu gaat om storingen of de opslag van gegevens en beelden in overeenstemming met de basisverordening gegevensbescherming - CoRe is aangepast aan de behoeften van klanten.
$coReDescription_de = Bosch Connected Repair ist eine Software, die Werkstattausrüstung, Fahrzeug- und Reparaturdaten miteinander verbindet. Ob bei Störungen oder bei der Speicherung von Daten und Bildern gemäß der Datenschutzgrundverordnung - CoRe wurde an die Bedürfnisse der Kunden angepasst.
$coReDescription_en = Bosch Connected Repair is software that connects workshop equipment, vehicle and repair data. Whether in the event of malfunctions or the storage of data and images in accordance with the Basic Data Protection Regulation - CoRe has been adapted to the needs of customers.

$kts250SDDescription_fr = L'essentiel pour le diagnostic professionnel, la réparation et l'entretien spécifiquement pour le KTS 250. Cette licence permet un diagnostic électrique efficace et offre un large éventail d'autres fonctions pour tous les véhicules couverts. Fonction SDA incluse.
$kts250SDDescription_nl = Complete diagnosesoftware voor KTS 250, incl. SDA en online software updates.
$kts250SDDescription_de = Der Einstieg in die professionelle Diagnose, Reparatur und Wartung speziell für KTS 250. Es ermöglicht eine kompetente, vielseitige Diagnosetechnik und bietet zahlreiche weitere Funktionen für alle abgedeckten Fahrzeuge.
$kts250SDDescription_en = The entry into professional diagnosis, repair and maintenance specifically for KTS 250. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.

$infoartWDescription_fr = L'ESI[tronic] 2.0 W contient des informations sur les valeurs de test diesel pour les combinaisons de pompes en ligne ainsi que pour les pompes VE, la procédure de test complet du processus de contrôle : de la détermination des valeurs de mesure à l'impression du rapport et à l'affichage des étapes de test dans l'ordre du protocole.
$infoartWDescription_nl = Diesel afstelgegevens en testwaarden voor lijnpompen en VE-pompen.
$infoartWDescription_de = Die ESI[tronic] 2.0-Infoart W beinhaltet Informationen zu Diesel Prüfwerten für Reihenpumpen-Kombinationen sowie für VE Pumpen, den kompletten Prüfvorgang von der Messwerteermittlung bis zum Protokollausdruck und die Anzeige der Prüfschritte in optimaler Reihenfolge.
$infoartWDescription_en = The ESI[tronic] 2.0 Infoart W contains information about diesel test values for in-line pump combinations as well as for VE pumps, the complete test process test procedure from the determination of the measured values to the printout of the report and the display of the test steps in the optimum sequence.

$infoartTestdataDescription_fr = Testdata d'ESI[tronic] 2.0 contient les valeurs de test pour les pompes haute pression Common Rail Bosch, les injecteurs Common Rail et les pompes d'injection VP 29 / 30 / 44.
$infoartTestdataDescription_nl = De EPS-Testdata bevat testwaarden voor Bosch Common Rail hogedrukpompen, Common Rail injectoren en VP 29 / 30 / 44 verdelerinspuitpompen. Online download en updates via DDM. Voor gebruik op Bosch EPS-dieseltestbanken.
$infoartTestdataDescription_de = Die ESI[tronic] 2.0-Infoart Testdata (CD) enthält Prüfwerte für Bosch Common Rail Hochdruckpumpen, Common Rail Injektoren und VP 29 / 30 / 44 Verteilereinspritzpumpen.
$infoartTestdataDescription_en = The ESI[tronic] 2.0-Infotype Testdata (CD) contains test values for Bosch Common Rail high pressure pumps, Common Rail injectors and VP 29 / 30 / 44 distributor injection pumps.
$truckUpgradeDescription_fr = La 2ème ligence ESI[tronic] 2.0 Truck est dédié aux utilisateurs d'ESI[tronic] VL et permet aux ateliers d'effectuer un diagnostic efficace, une maintenance complète et une réparation efficace de tous les véhicules utilitaires légers et lourds, remorques, utilitaires et autobus.
$truckUpgradeDescription_nl = ESI[tronic] Truck softwarepakket aan voordeeltarief voor bestaande ESI[tronic] Advanced of Master klanten. Diagnose, onderhoudsplannen, elektrische schema's en technische informatie voor vrachtwagens, aanhangwagens, bussen en lichte bedrijfsvoertuigen.
$truckUpgradeDescription_de = Das Paket Truck unterstützt Bestandskunden der ESI[tronic] Car Produkte bei der zuverlässigen Diagnose, kompletten Wartung und effizienten Reparatur von allen gängigen leichten und schweren Nutzfahrzeugen, Anhängern, Transportern und Bussen.
$truckUpgradeDescription_en = The Truck Upgrade package is dedicated to ESI[tronic] Car users and supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.

$alltrucksSubsS1BrimName = Alltrucks Diagnose-Paket
$alltrucksSubsM3BrimName = ESI[tronic] 2.0 All Trucks Unlim Multi
$compacFsa500Full3YBrimName = CompacSoft[plus] FSA 500 (3 years)
$compacFsa500SubsBrimName = CompacSoft[plus] FSA 500
$compacFsa7xxFull3YBrimName = CompacSoft[plus] FSA 7xx (3 years)
$compacFsa7xxSubsBrimName = CompacSoft[plus] FSA 7xx
$criSubsS1BrimName = Component DCI-CRI
$criSubsM3BrimName = Component DCI-CRI_Multi-users_3
$criFull3YBrimName = Component DCI-CRI (3 year)
$crinSubsS1BrimName = Component DCI-CRIN
$crinSubsM3BrimName = Component DCI-CRIN_Multi-users_3
$crinFull3YBrimName = Component DCI-CRIN (3 year)
$packAdvancedSubsS1BrimName = ESI 2.0 Advanced Unlimited
$packAdvancedSubsM3BrimName = ESI 2.0 Advanced Unlimited Multi
$packAdvancedFull3YS1BrimName = ESI 2.0 Advanced 3 years
$packAdvancedFull3YM3BrimName = ESI 2.0 Advanced 3 years Multi
$packComponentCatSubsS1BrimName = ESI 2.0 ComponentCatalog D+E Unlimited
$packComponentCatSubsM3BrimName = ESI 2.0 ComponentCatalog D+E Unlim Multi
$packComponentCatFull3YS1BrimName = ESI 2.0 ComponentCatalog D+E 3 years
$packComponentCatFull3YM3BrimName = ESI 2.0 ComponentCatalog D+E 3y Multi
$packComponentRepairDieselSubsS1BrimName = ESI 2.0 ComponentRepair D+E Unlimited
$packComponentRepairDieselSubsM3BrimName = ESI 2.0 ComponentRepair D+E Unlim Multi
$packComponentRepairDieselFull3YS1BrimName = ESI 2.0 ComponentRepair D+E 3 years
$packComponentRepairDieselFull3YM3BrimName = ESI 2.0 ComponentRepair D+E 3y Multi
$packComponentRepairElectricSubsS1BrimName = ESI 2.0 ComponentRepair E Unlimited
$packComponentRepairElectricSubsM3BrimName = ESI 2.0 ComponentRepair E Unlim Multi
$packComponentRepairElectricFull3YS1BrimName = ESI 2.0 ComponentRepair E 3 years
$packComponentRepairElectricFull3YM3BrimName = ESI 2.0 ComponentRepair E 3 years Multi
$packDiagnosticSubsS1BrimName = ESI 2.0 Diagnostic Unlimited
$packDiagnosticSubsM3BrimName = ESI 2.0 Diagnostic Unlimited Multi
$packDiagnosticFull3YS1BrimName = ESI 2.0 Diagnostic 3 years
$packDiagnosticFull3YM3BrimName = ESI 2.0 Diagnostic 3 years Multi
$packMasterSubsS1BrimName = ESI 2.0 Master Unlimited
$packMasterSubsM3BrimName = ESI 2.0 Master Unlimited Multi
$packMasterFull3YS1BrimName = ESI 2.0 Master 3 years
$packMasterFull3YM3BrimName = ESI 2.0 Master 3 years Multi

$ohw1SubsS1BrimName = ESI[tronic] 2.0 Truck OHW I
$ohw1SubsM3BrimName = ESI[tronic] 2.0 Truck OHW I Multi
$ohw1Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW I (3 years)
$ohw1Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW I (3y) Multi

$ohw2SubsS1BrimName = ESI[tronic] 2.0 Truck OHW II
$ohw2SubsM3BrimName = ESI[tronic] 2.0 Truck OHW II Multi
$ohw2Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW II (3 years)
$ohw2Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW II (3y) Multi

$packTruckSubsS1BrimName = ESI[tronic] 2.0 Truck
$packTruckSubsM3BrimName = ESI[tronic] 2.0 Truck Multi
$packTruckFull3YS1BrimName = ESI[tronic] 2.0 Truck (3 years)
$packTruckFull3YM3BrimName = ESI[tronic] 2.0 Truck (3 years) Multi
$packTruckFullBrimName = ESI[tronic] 2.0 Truck (Einmalkauf)
$infoartWSubsBrimName = ESI[tronic] W Diesel Test Data (WP)
$infoartWFull3YBrimName = ESI[tronic] W (3 Jahre)
$thlPkwSubsBrimName = THL use technical hotline
$thlPkwFull3YBrimName = Technische Hotline LKW (3 Jahre)
$thlTrkSubsBrimName = Technische Hotline ESI[tronic] for Truck
$thlPkwTrkSubsBrimName = Technische Hotline fuer LKWs und PKWs
$infoartTestdataSubsBrimName = Testdata VP-M/CP
$truckUpgradeSubsS1BrimName = ESI[tronic] 2.0 Truck Upgrade
$truckUpgradeSubsM3BrimName = ESI[tronic] 2.0 Truck Upgrade Multi
$truckUpgradeFull3YS1BrimName = ESI[tronic] 2.0 Truck Upgrade (3 years)
$truckUpgradeFull3YM3BrimName = ESI[tronic] 2.0 Truck Upgrade (3y) Multi

$coreSubsS1BrimName = CoRe for ESI 2.0 packages

$kts250SDFullBrimName = KTS 250 SD (OTP one time purchase)
$kts250SDSubsBrimName = KTS 250 SD ECU Diagnosis
$fsa7xxMulti = CompacSoft[plus] FSA 7xx Multi
$fsa7xx3YearsMulti = CompacSoft[plus] FSA 7xx (3 years) Multi
$fsa5xxMulti = CompacSoft[plus] FSA 500 Multi
$fsa5xx3YearsMulti = CompacSoft[plus] FSA 500 (3 years)Multi
$adasSubsS1BrimName = ADAS ONE - DATA
$adasFull3YS1BrimName = ADAS ONE - DATA (3 years)

$countryRestricted = true
# @formatter:off
$enabledIn = BE
# @formatter:on


INSERT_UPDATE App; code[unique = true]       ; packageName                    ; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$beCc1987_ALLTRUCKS   ; $aaPackageName1987_ALLTRUCKS   ;
                 ; AA2_$beCc1687_CSFSA500    ; $aaPackageName1687_CSFSA500    ;
                 ; AA2_$beCc1687_CSFSA7XX    ; $aaPackageName1687_CSFSA7XX    ;
                 ; AA2_$beCc1687_DCICRI      ; $aaPackageName1687_DCICRI      ;
                 ; AA2_$beCc1687_DCICRIN     ; $aaPackageName1687_DCICRIN     ;
                 ; AA2_$beCc1987_ESIADV      ; $aaPackageName1987_ESIADV      ;
                 ; AA2_$beCc1987_ESIREPCAT   ; $aaPackageName1987_ESIREPCAT   ;
                 ; AA2_$beCc1987_ESIREPD     ; $aaPackageName1987_ESIREPD     ;
                 ; AA2_$beCc1987_ESIREPE     ; $aaPackageName1987_ESIREPE     ;
                 ; AA2_$beCc1987_ESIDIAG     ; $aaPackageName1987_ESIDIAG     ;
                 ; AA2_$beCc1987_ESIMASTER   ; $aaPackageName1987_ESIMASTER   ;
                 ; AA2_$beCc1987_TRKOHW1     ; $aaPackageName1987_TRKOHW1     ;
                 ; AA2_$beCc1987_TRKOHW2     ; $aaPackageName1987_TRKOHW2     ;
                 ; AA2_$beCc1987_TRKTRUCK    ; $aaPackageName1987_TRKTRUCK    ;
                 ; AA2_$beCc1987_TSTINFOAW   ; $aaPackageName1987_TSTINFOAW   ;
                 ; AA2_$beCc1687_TSTINFODAT  ; $aaPackageName1687_TSTINFODAT  ;
                 ; AA2_$beCc1987_TRKUPG      ; $aaPackageName1987_TRUCKUPG    ;
                 ; AA2_$beCc1687_CORE_ESIPKG ; $aaPackageName1687_CORE_ESIPKG ;
                 ; AA2_$beCc1987_KTS250SD    ; $aaPackageName1987_KTS250      ;
                 ; AA2_$beCc1987_ADAS_ONE    ; $aaPackageName1987_ADAS_ONE    ;

INSERT_UPDATE App; code[unique = true]       ; name[lang = fr]                          ; summary[lang = fr]                         ; description[lang = fr]; $catalogVersion[unique = true];
                 ; AA2_$beCc1987_ALLTRUCKS   ; ESI[tronic] Alltrucks                    ; $alltrucksDescription_fr                   ; $alltrucksDescription_fr
                 ; AA2_$beCc1687_CSFSA500    ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description_fr                ; $compacFsa500Description_fr
                 ; AA2_$beCc1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription_fr                ; $compacFsa7xxDescription_fr
                 ; AA2_$beCc1687_DCICRI      ; Composant DCI-CRI                        ; $criDescription_fr                         ; $criDescription_fr
                 ; AA2_$beCc1687_DCICRIN     ; Composant DCI-CRIN                       ; $crinDescription_fr                        ; $crinDescription_fr
                 ; AA2_$beCc1987_ESIADV      ; ESI[tronic] Advanced                     ; $packAdvancedDescription_fr                ; $packAdvancedDescription_fr
                 ; AA2_$beCc1987_ESIREPCAT   ; ESI[tronic] Catalogue                    ; $packComponentCatDescription_fr            ; $packComponentCatDescription_fr
                 ; AA2_$beCc1987_ESIREPD     ; ESI[tronic] Repair D+E                   ; $packComponentRepairDieselDescription_fr   ; $packComponentRepairDieselDescription_fr
                 ; AA2_$beCc1987_ESIREPE     ; ESI[tronic] Repair E                     ; $packComponentRepairElectricDescription_fr ; $packComponentRepairElectricDescription_fr
                 ; AA2_$beCc1987_ESIDIAG     ; ESI[tronic] Diagnostic                   ; $packDiagnosticDescription_fr              ; $packDiagnosticDescription_fr
                 ; AA2_$beCc1987_ESIMASTER   ; ESI[tronic] Master                       ; $packMasterDescription_fr                  ; $packMasterDescription_fr
                 ; AA2_$beCc1987_TRKOHW1     ; ESI[tronic] Off Highway I (OHW I)        ; $ohw1Description_fr                        ; $ohw1Description_fr
                 ; AA2_$beCc1987_TRKOHW2     ; ESI[tronic] Off Highway II (OHW II)      ; $ohw2Description_fr                        ; $ohw2Description_fr
                 ; AA2_$beCc1987_TRKTRUCK    ; ESI[tronic] Truck                        ; $packTruckDescription_fr                   ; $packTruckDescription_fr
                 ; AA2_$beCc1987_TSTINFOAW   ; ESI[tronic] W                            ; $infoartWDescription_fr                    ; $infoartWDescription_fr
                 ; AA2_$beCc1687_TSTINFODAT  ; Testdata VP-M/CP                         ; $infoartTestdataDescription_fr             ; $infoartTestdataDescription_fr
                 ; AA2_$beCc1987_TRKUPG      ; ESI[tronic] Truck Upgrade                ; $truckUpgradeDescription_fr                ; $truckUpgradeDescription_fr
                 ; AA2_$beCc1687_CORE_ESIPKG ; Connected Repair (CoRe Serveur + Online) ; $coReDescription_fr                        ; $coReDescription_fr
                 ; AA2_$beCc1987_KTS250SD    ; KTS 250 SD                               ; $kts250SDDescription_fr                    ; $kts250SDDescription_fr
                 ; AA2_$beCc1987_ADAS_ONE    ; ADAS One Solution                        ; $adasDescription_fr                        ; $adasDescription_fr   ;


INSERT_UPDATE App; code[unique = true]       ; name[lang = nl]                         ; summary[lang = nl]                         ; description[lang = nl]; $catalogVersion[unique = true];
                 ; AA2_$beCc1987_ALLTRUCKS   ; Alltrucks softwarepakket                ; $alltrucksDescription_nl                   ; $alltrucksDescription_nl
                 ; AA2_$beCc1687_CSFSA500    ; CompacSoft[plus] FSA 500                ; $compacFsa500Description_nl                ; $compacFsa500Description_nl
                 ; AA2_$beCc1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx                ; $compacFsa7xxDescription_nl                ; $compacFsa7xxDescription_en
                 ; AA2_$beCc1687_DCICRI      ; Component DCI-CRI                       ; $criDescription_nl                         ; $criDescription_nl
                 ; AA2_$beCc1687_DCICRIN     ; Component DCI-CRIN                      ; $crinDescription_nl                        ; $crinDescription_nl
                 ; AA2_$beCc1987_ESIADV      ; ESI[tronic] 2.0 Advanced                ; $packAdvancedDescription_nl                ; $packAdvancedDescription_nl
                 ; AA2_$beCc1987_ESIREPCAT   ; ESI[tronic] 2.0 ComponentCat D+E        ; $packComponentCatDescription_nl            ; $packComponentCatDescription_nl
                 ; AA2_$beCc1987_ESIREPD     ; ESI[tronic] 2.0 ComponentRepair D+E     ; $packComponentRepairDieselDescription_nl   ; $packComponentRepairDieselDescription_nl
                 ; AA2_$beCc1987_ESIREPE     ; ESI[tronic] 2.0 ComponentRepair E       ; $packComponentRepairElectricDescription_nl ; $packComponentRepairElectricDescription_nl
                 ; AA2_$beCc1987_ESIDIAG     ; ESI[tronic] 2.0 Diagnostic              ; $packDiagnosticDescription_nl              ; $packDiagnosticDescription_nl
                 ; AA2_$beCc1987_ESIMASTER   ; ESI[tronic] 2.0 Master                  ; $packMasterDescription_nl                  ; $packMasterDescription_nl
                 ; AA2_$beCc1987_TRKOHW1     ; ESI[tronic] 2.0 Truck OHW I             ; $ohw1Description_nl                        ; $ohw1Description_nl
                 ; AA2_$beCc1987_TRKOHW2     ; ESI[tronic] 2.0 Truck OHW II            ; $ohw2Description_nl                        ; $ohw2Description_nl
                 ; AA2_$beCc1987_TRKTRUCK    ; ESI[tronic] 2.0 Truck                   ; $packTruckDescription_nl                   ; $packTruckDescription_nl
                 ; AA2_$beCc1987_TSTINFOAW   ; ESI[tronic] W                           ; $infoartWDescription_nl                    ; $infoartWDescription_nl
                 ; AA2_$beCc1687_TSTINFODAT  ; Testdata VP-M/CP                        ; $infoartTestdataDescription_nl             ; $infoartTestdataDescription_nl
                 ; AA2_$beCc1987_TRKUPG      ; ESI[tronic] 2.0 Truck Upgrade           ; $truckUpgradeDescription_nl                ; $truckUpgradeDescription_nl
                 ; AA2_$beCc1687_CORE_ESIPKG ; Connected Repair (CoRe server + online) ; $coReDescription_nl                        ; $coReDescription_nl
                 ; AA2_$beCc1987_KTS250SD    ; KTS 250 SD                              ; $kts250SDDescription_nl                    ; $kts250SDDescription_nl
                 ; AA2_$beCc1987_ADAS_ONE    ; ADAS One Solution                       ; $adasDescription_nl                        ; $adasDescription_nl   ;


INSERT_UPDATE App; code[unique = true]       ; name[lang = de]               ; summary[lang = de]                         ; description[lang = de]; $catalogVersion[unique = true];
                 ; AA2_$beCc1987_ALLTRUCKS   ; Alltrucks                     ; $alltrucksDescription_de                   ; $alltrucksDescription_de
                 ; AA2_$beCc1687_CSFSA500    ; CompacSoft[plus] FSA 500      ; $compacFsa500Description_de                ; $compacFsa500Description_de
                 ; AA2_$beCc1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx      ; $compacFsa7xxDescription_de                ; $compacFsa7xxDescription_de
                 ; AA2_$beCc1687_DCICRI      ; CRI Bosch                     ; $criDescription_de                         ; $criDescription_de
                 ; AA2_$beCc1687_DCICRIN     ; CRIN Bosch                    ; $crinDescription_de                        ; $crinDescription_de
                 ; AA2_$beCc1987_ESIADV      ; Paket Advanced                ; $packAdvancedDescription_de                ; $packAdvancedDescription_de
                 ; AA2_$beCc1987_ESIREPCAT   ; Paket Ersatzteile-Kat.        ; $packComponentCatDescription_de            ; $packComponentCatDescription_de
                 ; AA2_$beCc1987_ESIREPD     ; Paket Repair Diesel           ; $packComponentRepairDieselDescription_de   ; $packComponentRepairDieselDescription_de
                 ; AA2_$beCc1987_ESIREPE     ; Paket Repair Elektrik         ; $packComponentRepairElectricDescription_de ; $packComponentRepairElectricDescription_de
                 ; AA2_$beCc1987_ESIDIAG     ; Paket Diagnostic              ; $packDiagnosticDescription_de              ; $packDiagnosticDescription_de
                 ; AA2_$beCc1987_ESIMASTER   ; Paket Master                  ; $packMasterDescription_de                  ; $packMasterDescription_de
                 ; AA2_$beCc1987_TRKOHW1     ; Off Highway I (OHW I)         ; $ohw1Description_de                        ; $ohw1Description_de
                 ; AA2_$beCc1987_TRKOHW2     ; Off Highway II (OHW II)       ; $ohw2Description_de                        ; $ohw2Description_de
                 ; AA2_$beCc1987_TRKTRUCK    ; Paket Truck                   ; $packTruckDescription_de                   ; $packTruckDescription_de
                 ; AA2_$beCc1987_TSTINFOAW   ; Infoart W                     ; $infoartWDescription_de                    ; $infoartWDescription_de
                 ; AA2_$beCc1687_TSTINFODAT  ; Infoart Testdata              ; $infoartTestdataDescription_de             ; $infoartTestdataDescription_de
                 ; AA2_$beCc1987_TRKUPG      ; ESI[tronic] 2.0 Truck Upgrade ; $truckUpgradeDescription_de                ; $truckUpgradeDescription_de
                 ; AA2_$beCc1687_CORE_ESIPKG ; CoRe Server + Online          ; $coReDescription_de                        ; $coReDescription_de
                 ; AA2_$beCc1987_KTS250SD    ; KTS 250 SD ECU Diagnosis      ; $kts250SDDescription_de                    ; $kts250SDDescription_de
                 ; AA2_$beCc1987_ADAS_ONE    ; ADAS One Solution             ; $adasDescription_de                        ; $adasDescription_de   ;


INSERT_UPDATE App; code[unique = true]       ; name[lang = en]                     ; summary[lang = en]                         ; description[lang = en]; $catalogVersion[unique = true];
                 ; AA2_$beCc1987_ALLTRUCKS   ; Alltrucks Diagnosis                 ; $alltrucksDescription_en                   ; $alltrucksDescription_en
                 ; AA2_$beCc1687_CSFSA500    ; CompacSoft[plus] FSA 500            ; $compacFsa500Description_en                ; $compacFsa500Description_en
                 ; AA2_$beCc1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx            ; $compacFsa7xxDescription_en                ; $compacFsa7xxDescription_en
                 ; AA2_$beCc1687_DCICRI      ; Component DCI-CRI                   ; $criDescription_en                         ; $criDescription_en
                 ; AA2_$beCc1687_DCICRIN     ; Component DCI-CRIN                  ; $crinDescription_en                        ; $crinDescription_en
                 ; AA2_$beCc1987_ESIADV      ; ESI[tronic] 2.0 Advanced            ; $packAdvancedDescription_en                ; $packAdvancedDescription_en
                 ; AA2_$beCc1987_ESIREPCAT   ; ESI[tronic] 2.0 ComponentCat D+E    ; $packComponentCatDescription_en            ; $packComponentCatDescription_en
                 ; AA2_$beCc1987_ESIREPD     ; ESI[tronic] 2.0 ComponentRepair D+E ; $packComponentRepairDieselDescription_en   ; $packComponentRepairDieselDescription_en
                 ; AA2_$beCc1987_ESIREPE     ; ESI[tronic] 2.0 ComponentRepair E   ; $packComponentRepairElectricDescription_en ; $packComponentRepairElectricDescription_en
                 ; AA2_$beCc1987_ESIDIAG     ; ESI[tronic] 2.0 Diagnostic          ; $packDiagnosticDescription_en              ; $packDiagnosticDescription_en
                 ; AA2_$beCc1987_ESIMASTER   ; ESI[tronic] 2.0 Master              ; $packMasterDescription_en                  ; $packMasterDescription_en
                 ; AA2_$beCc1987_TRKOHW1     ; ESI[tronic] 2.0 Truck OHW I         ; $ohw1Description_en                        ; $ohw1Description_en
                 ; AA2_$beCc1987_TRKOHW2     ; ESI[tronic] 2.0 Truck OHW II        ; $ohw2Description_en                        ; $ohw2Description_en
                 ; AA2_$beCc1987_TRKTRUCK    ; ESI[tronic] 2.0 Truck               ; $packTruckDescription_en                   ; $packTruckDescription_en
                 ; AA2_$beCc1987_TSTINFOAW   ; ESI[tronic] W                       ; $infoartWDescription_en                    ; $infoartWDescription_en
                 ; AA2_$beCc1687_TSTINFODAT  ; Testdata VP-M/CP                    ; $infoartTestdataDescription_en             ; $infoartTestdataDescription_en
                 ; AA2_$beCc1987_TRKUPG      ; ESI[tronic] 2.0 Truck Upgrade       ; $truckUpgradeDescription_en                ; $truckUpgradeDescription_en
                 ; AA2_$beCc1687_CORE_ESIPKG ; CoRe Server + Online                ; $coReDescription_en                        ; $coReDescription_en
                 ; AA2_$beCc1987_KTS250SD    ; KTS 250 SD ECU Diagnosis            ; $kts250SDDescription_en                    ; $kts250SDDescription_en
                 ; AA2_$beCc1987_ADAS_ONE    ; ADAS One Solution                   ; $adasDescription_en                        ; $adasDescription_en   ;

INSERT_UPDATE ProductContainer; code[unique = true]        ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$beCc1987_ALLTRUCKS   ; AA2_$beCc1987_ALLTRUCKS
                              ; pcaa_$beCc1687_CSFSA500    ; AA2_$beCc1687_CSFSA500
                              ; pcaa_$beCc1687_CSFSA7XX    ; AA2_$beCc1687_CSFSA7XX
                              ; pcaa_$beCc1687_DCICRI      ; AA2_$beCc1687_DCICRI
                              ; pcaa_$beCc1687_DCICRIN     ; AA2_$beCc1687_DCICRIN
                              ; pcaa_$beCc1987_ESIADV      ; AA2_$beCc1987_ESIADV
                              ; pcaa_$beCc1987_ESIREPCAT   ; AA2_$beCc1987_ESIREPCAT
                              ; pcaa_$beCc1987_ESIREPD     ; AA2_$beCc1987_ESIREPD
                              ; pcaa_$beCc1987_ESIREPE     ; AA2_$beCc1987_ESIREPE
                              ; pcaa_$beCc1987_ESIDIAG     ; AA2_$beCc1987_ESIDIAG
                              ; pcaa_$beCc1987_ESIMASTER   ; AA2_$beCc1987_ESIMASTER
                              ; pcaa_$beCc1987_TRKOHW1     ; AA2_$beCc1987_TRKOHW1
                              ; pcaa_$beCc1987_TRKOHW2     ; AA2_$beCc1987_TRKOHW2
                              ; pcaa_$beCc1987_TRKTRUCK    ; AA2_$beCc1987_TRKTRUCK
                              ; pcaa_$beCc1987_TSTINFOAW   ; AA2_$beCc1987_TSTINFOAW
                              ; pcaa_$beCc1687_TSTINFODAT  ; AA2_$beCc1687_TSTINFODAT
                              ; pcaa_$beCc1987_TRKUPG      ; AA2_$beCc1987_TRKUPG
                              ; pcaa_$beCc1687_CORE_ESIPKG ; AA2_$beCc1687_CORE_ESIPKG
                              ; pcaa_$beCc1987_KTS250SD    ; AA2_$beCc1987_KTS250SD
                              ; pcaa_$beCc1987_ADAS_ONE    ; AA2_$beCc1987_ADAS_ONE


INSERT_UPDATE AppLicense; code[unique = true] ; $baseProduct              ; sellerProductId; brimName[lang = en]                        ; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); specifiedPrice; enabledCountries(isocode)[default = $enabledIn]; billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; availabilityStatus(code)[default = PUBLISHED]
                        ; AA2_$beCc1987P12760 ; AA2_$beCc1987_ALLTRUCKS   ; 1987P12760     ; $alltrucksSubsS1BrimName                   ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 2150.00       ; $enabledIn
                        ; AA2_$beCc1987P12949 ; AA2_$beCc1987_ALLTRUCKS   ; 1987P12949     ; $alltrucksSubsM3BrimName                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 2300.00       ; $enabledIn
                        ; AA2_$beCc1687P15060 ; AA2_$beCc1687_CSFSA500    ; 1687P15060     ; $compacFsa500SubsBrimName                  ;                                          ; runtime_subs_unlimited ; <ignore>        ; 100.00        ; $enabledIn
                        ; AA2_$beCc1687P15045 ; AA2_$beCc1687_CSFSA7XX    ; 1687P15045     ; $compacFsa7xxSubsBrimName                  ;                                          ; runtime_subs_unlimited ; <ignore>        ; 315.00        ; $enabledIn
                        ; AA2_$beCc1687P15090 ; AA2_$beCc1687_DCICRI      ; 1687P15090     ; $criSubsS1BrimName                         ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 325.00        ; $enabledIn
                        ; AA2_$beCc1687P15102 ; AA2_$beCc1687_DCICRI      ; 1687P15102     ; $criSubsM3BrimName                         ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 425.00        ; $enabledIn
                        ; AA2_$beCc1687P15100 ; AA2_$beCc1687_DCICRIN     ; 1687P15100     ; $crinSubsS1BrimName                        ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 325.00        ; $enabledIn
                        ; AA2_$beCc1687P15107 ; AA2_$beCc1687_DCICRIN     ; 1687P15107     ; $crinSubsM3BrimName                        ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 425.00        ; $enabledIn
                        ; AA2_$beCc1987P12840 ; AA2_$beCc1987_ESIADV      ; 1987P12840     ; $packAdvancedSubsS1BrimName                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1450.00       ; $enabledIn
                        ; AA2_$beCc1987P12847 ; AA2_$beCc1987_ESIADV      ; 1987P12847     ; $packAdvancedSubsM3BrimName                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1600.00       ; $enabledIn
                        ; AA2_$beCc1987P12998 ; AA2_$beCc1987_ESIREPCAT   ; 1987P12998     ; $packComponentCatSubsS1BrimName            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 330.00        ; $enabledIn
                        ; AA2_$beCc1987P12784 ; AA2_$beCc1987_ESIREPCAT   ; 1987P12784     ; $packComponentCatSubsM3BrimName            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 430.00        ; $enabledIn
                        ; AA2_$beCc1987P12970 ; AA2_$beCc1987_ESIREPD     ; 1987P12970     ; $packComponentRepairDieselSubsS1BrimName   ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 700.00        ; $enabledIn
                        ; AA2_$beCc1987P12297 ; AA2_$beCc1987_ESIREPD     ; 1987P12297     ; $packComponentRepairDieselSubsM3BrimName   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 800.00        ; $enabledIn
                        ; AA2_$beCc1987P12990 ; AA2_$beCc1987_ESIREPE     ; 1987P12990     ; $packComponentRepairElectricSubsS1BrimName ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 300.00        ; $enabledIn
                        ; AA2_$beCc1987P12295 ; AA2_$beCc1987_ESIREPE     ; 1987P12295     ; $packComponentRepairElectricSubsM3BrimName ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 400.00        ; $enabledIn
                        ; AA2_$beCc1987P12820 ; AA2_$beCc1987_ESIDIAG     ; 1987P12820     ; $packDiagnosticSubsS1BrimName              ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 840.00        ; $enabledIn
                        ; AA2_$beCc1987P12824 ; AA2_$beCc1987_ESIDIAG     ; 1987P12824     ; $packDiagnosticSubsM3BrimName              ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 990.00        ; $enabledIn
                        ; AA2_$beCc1987P12910 ; AA2_$beCc1987_ESIMASTER   ; 1987P12910     ; $packMasterSubsS1BrimName                  ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1950.00       ; $enabledIn
                        ; AA2_$beCc1987P12917 ; AA2_$beCc1987_ESIMASTER   ; 1987P12917     ; $packMasterSubsM3BrimName                  ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 2100.00       ; $enabledIn
                        ; AA2_$beCc1987P12260 ; AA2_$beCc1987_TRKOHW1     ; 1987P12260     ; $ohw1SubsS1BrimName                        ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 630.00        ; $enabledIn
                        ; AA2_$beCc1987P12262 ; AA2_$beCc1987_TRKOHW1     ; 1987P12262     ; $ohw1SubsM3BrimName                        ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 780.00        ; $enabledIn
                        ; AA2_$beCc1987P12278 ; AA2_$beCc1987_TRKOHW2     ; 1987P12278     ; $ohw2SubsS1BrimName                        ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1200.00       ; $enabledIn
                        ; AA2_$beCc1987P12275 ; AA2_$beCc1987_TRKOHW2     ; 1987P12275     ; $ohw2SubsM3BrimName                        ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1350.00       ; $enabledIn
                        ; AA2_$beCc1987P12400 ; AA2_$beCc1987_TRKTRUCK    ; 1987P12400     ; $packTruckSubsS1BrimName                   ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1350.00       ; $enabledIn
                        ; AA2_$beCc1987P12936 ; AA2_$beCc1987_TRKTRUCK    ; 1987P12936     ; $packTruckSubsM3BrimName                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1500.00       ; $enabledIn
                        ; AA2_$beCc1987P12500 ; AA2_$beCc1987_TSTINFOAW   ; 1987P12500     ; $infoartWSubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 665.00        ; $enabledIn
                        ; AA2_$beCc1687P15015 ; AA2_$beCc1687_TSTINFODAT  ; 1687P15015     ; $infoartTestdataSubsBrimName               ;                                          ; runtime_subs_unlimited ; <ignore>        ; 315.00        ; $enabledIn
                        ; AA2_$beCc1987P12404 ; AA2_$beCc1987_TRKUPG      ; 1987P12404     ; $truckUpgradeSubsS1BrimName                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1150.00       ; $enabledIn
                        ; AA2_$beCc1987P12359 ; AA2_$beCc1987_TRKUPG      ; 1987P12359     ; $truckUpgradeSubsM3BrimName                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1300.00       ; $enabledIn
                        ; AA2_$beCc1687P15130 ; AA2_$beCc1687_CORE_ESIPKG ; 1687P15130     ; $coreSubsS1BrimName                        ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1.00          ; $enabledIn
                        ; AA2_$beCc1987P12389 ; AA2_$beCc1987_KTS250SD    ; 1987P12389     ; $kts250SDFullBrimName                      ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 650.00        ; $enabledIn
                        ; AA2_$beCc1987P12385 ; AA2_$beCc1987_KTS250SD    ; 1987P12385     ; $kts250SDSubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 590.00        ; $enabledIn
                        ; AA2_$beCc1987P12515 ; AA2_$beCc1987_ADAS_ONE    ; 1987P12515     ; $adasSubsS1BrimName                        ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 0.01          ; $enabledIn
                        ; AA2_$beCc1987P12517 ; AA2_$beCc1987_ADAS_ONE    ; 1987P12517     ; $adasFull3YS1BrimName                      ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 0.01          ; $enabledIn
                        ; AA2_$beCc1687P15170 ; AA2_$beCc1687_CSFSA500    ; 1687P15170     ; $fsa5xxMulti                               ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000        ; 0.01                                           ;
                        ; AA2_$beCc1687P15173 ; AA2_$beCc1687_CSFSA500    ; 1687P15173     ; $fsa5xx3YearsMulti                         ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; IDW000        ; 0.01                                           ;
                        ; AA2_$beCc1687P15160 ; AA2_$beCc1687_CSFSA7XX    ; 1687P15160     ; $fsa7xxMulti                               ;                                          ; runtime_subs_unlimited ; BI_M_3          ; IDW000        ; 0.01                                           ;
                        ; AA2_$beCc1687P15163 ; AA2_$beCc1687_CSFSA7XX    ; 1687P15163     ; $fsa7xx3YearsMulti                         ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; IDW000        ; 0.01                                           ;

# The licenses below were discontinued by MA and should be available only for the contract migration.
UPDATE AppLicense; code[unique = true] ; $catalogVersion[unique = true]; availabilityStatus(code)[default = UNPUBLISHED]
                 ; AA2_$beCc1987P12389 ;

UPDATE AppLicense; code[unique = true] ; userGroups(uid)                                                       ; $catalogVersion[unique = true]
                 ; AA2_$beCc1987P12760 ; IDW000,AT0000
                 ; AA2_$beCc1987P12949 ; IDW000,AT0000
                 ; AA2_$beCc1687P15060 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000,KA0002 ;
                 ; AA2_$beCc1687P15045 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000,KA0002 ;
                 ; AA2_$beCc1687P15090 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1687P15102 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1687P15100 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1687P15107 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1987P12840 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000,KA0002 ;
                 ; AA2_$beCc1987P12847 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000,KA0002 ;
                 ; AA2_$beCc1987P12998 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1987P12784 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1987P12970 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1987P12297 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1987P12990 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1987P12295 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1987P12820 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000,KA0002 ;
                 ; AA2_$beCc1987P12824 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000,KA0002 ;
                 ; AA2_$beCc1987P12910 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000,KA0002 ;
                 ; AA2_$beCc1987P12917 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000,KA0002 ;
                 ; AA2_$beCc1987P12260 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1987P12262 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1987P12278 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1987P12275 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1987P12400 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1987P12936 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1987P12500 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1687P15015 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1987P12404 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1987P12359 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000        ;
                 ; AA2_$beCc1687P15130 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000,KA0002 ;
                 ; AA2_$beCc1987P12389 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000,KA0002 ;
                 ; AA2_$beCc1987P12385 ; IDW000,BCS000,BDS000,BDC000,BM0000,AC0000,AT0000,WD0000,AUT000,KA0002 ;
# The group assignment for the ADAS ONE is most probably not final. we're missing the prices for now
                 ; AA2_$beCc1987P12515 ; IDW000                                                                ;
                 ; AA2_$beCc1987P12517 ; IDW000                                                                ;
                 ; AA2_$beCc1687P15163 ; IDW000                                                                ;
                 ; AA2_$beCc1687P15170 ; IDW000                                                                ;
                 ; AA2_$beCc1687P15160 ; IDW000                                                                ;
                 ; AA2_$beCc1687P15173 ; IDW000                                                                ;

INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$beCc1987P12760                          ; 2150.00
                      ; AA2_$beCc1987P12949                          ; 2300.00
                      ; AA2_$beCc1687P15060                          ; 100.00
                      ; AA2_$beCc1687P15045                          ; 315.00
                      ; AA2_$beCc1687P15090                          ; 325.00
                      ; AA2_$beCc1687P15102                          ; 425.00
                      ; AA2_$beCc1687P15100                          ; 325.00
                      ; AA2_$beCc1687P15107                          ; 425.00
                      ; AA2_$beCc1987P12840                          ; 1450.00
                      ; AA2_$beCc1987P12847                          ; 1600.00
                      ; AA2_$beCc1987P12998                          ; 330.00
                      ; AA2_$beCc1987P12784                          ; 430.00
                      ; AA2_$beCc1987P12970                          ; 700.00
                      ; AA2_$beCc1987P12297                          ; 800.00
                      ; AA2_$beCc1987P12990                          ; 300.00
                      ; AA2_$beCc1987P12295                          ; 400.00
                      ; AA2_$beCc1987P12820                          ; 840.00
                      ; AA2_$beCc1987P12824                          ; 990.00
                      ; AA2_$beCc1987P12910                          ; 1950.00
                      ; AA2_$beCc1987P12917                          ; 2100.00
                      ; AA2_$beCc1987P12260                          ; 630.00
                      ; AA2_$beCc1987P12262                          ; 780.00
                      ; AA2_$beCc1987P12278                          ; 1200.00
                      ; AA2_$beCc1987P12275                          ; 1350.00
                      ; AA2_$beCc1987P12400                          ; 1350.00
                      ; AA2_$beCc1987P12936                          ; 1500.00
                      ; AA2_$beCc1987P12500                          ; 665.00
                      ; AA2_$beCc1687P15015                          ; 315.00
                      ; AA2_$beCc1987P12404                          ; 1150.00
                      ; AA2_$beCc1987P12359                          ; 1300.00
                      ; AA2_$beCc1687P15130                          ; 1.00
                      ; AA2_$beCc1987P12389                          ; 650.00
                      ; AA2_$beCc1987P12385                          ; 590.00
                      ; AA2_$beCc1987P12515                          ; 0.01 ;
                      ; AA2_$beCc1987P12517                          ; 0.01 ;
                      ; AA2_$beCc1687P15163                          ; 0.01 ;
                      ; AA2_$beCc1687P15170                          ; 0.01 ;
                      ; AA2_$beCc1687P15160                          ; 0.01 ;
                      ; AA2_$beCc1687P15173                          ; 0.01 ;


INSERT_UPDATE App; code[unique = true]     ; boms(code); $catalogVersion[unique = true]
                 ; AA2_$beCc1987_ALLTRUCKS ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$beCc1987_ESIADV    ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe
                 ; AA2_$beCc1987_ESIREPCAT ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE
                 ; AA2_$beCc1987_ESIREPD   ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE,MAT_$aaCcK3
                 ; AA2_$beCc1987_ESIREPE   ; MAT_$aaCcA,MAT_$aaCcE,MAT_$aaCcK2
                 ; AA2_$beCc1987_ESIDIAG   ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB
                 ; AA2_$beCc1987_ESIMASTER ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe,MAT_$aaCcM,MAT_$aaCcP
                 ; AA2_$beCc1987_TRKOHW1   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$beCc1987_TRKOHW2   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$beCc1987_TRKTRUCK  ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$beCc1987_TRKUPG    ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK

INSERT_UPDATE App; code[unique = true]       ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$beCc1987_ALLTRUCKS   ; CM_$aaCcNEOOrangeATruck, CM_$aaCcETruck
                 ; AA2_$beCc1687_CSFSA500    ; CM_$aaCcCSFSA5
                 ; AA2_$beCc1687_CSFSA7XX    ; CM_$aaCcCSS,CM_$aaCcCSK
                 ; AA2_$beCc1687_DCICRI      ; CM_$aaCcDCICRI
                 ; AA2_$beCc1687_DCICRIN     ; CM_$aaCcDCICRIN
                 ; AA2_$beCc1987_ESIADV      ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe
                 ; AA2_$beCc1987_ESIREPCAT   ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE
                 ; AA2_$beCc1987_ESIREPD     ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE,CM_$aaCcK3
                 ; AA2_$beCc1987_ESIREPE     ; CM_$aaCcA,CM_$aaCcE,CM_$aaCcK2
                 ; AA2_$beCc1987_ESIDIAG     ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB
                 ; AA2_$beCc1987_ESIMASTER   ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe,CM_$aaCcM,CM_$aaCcP
                 ; AA2_$beCc1987_TRKOHW1     ; CM_$aaCcETOHW1
                 ; AA2_$beCc1987_TRKOHW2     ; CM_$aaCcETOHW2
                 ; AA2_$beCc1987_TRKTRUCK    ; CM_$aaCcETruck
                 ; AA2_$beCc1987_TSTINFOAW   ; CM_$aaCcEW
                 ; AA2_$beCc1687_TSTINFODAT  ; CM_$aaCcTVPMCP
                 ; AA2_$beCc1987_TRKUPG      ; CM_$aaCcTRKUPG
                 ; AA2_$beCc1687_CORE_ESIPKG ; CM_$aaCcCoReESIPKG
                 ; AA2_$beCc1987_KTS250SD    ; CM_$aaCcKTS250ECUD

INSERT_UPDATE App; code[unique = true]       ; $supercategories; $catalogVersion[unique = true];
                 ; AA2_$beCc1987_ALLTRUCKS   ; cat_1010201
                 ; AA2_$beCc1687_CSFSA500    ; cat_201
                 ; AA2_$beCc1687_CSFSA7XX    ; cat_201
                 ; AA2_$beCc1687_DCICRI      ; cat_401
                 ; AA2_$beCc1687_DCICRIN     ; cat_401
                 ; AA2_$beCc1987_ESIADV      ; cat_10101
                 ; AA2_$beCc1987_ESIREPCAT   ; cat_1010101
                 ; AA2_$beCc1987_ESIREPD     ; cat_1010101
                 ; AA2_$beCc1987_ESIREPE     ; cat_1010101
                 ; AA2_$beCc1987_ESIDIAG     ; cat_10101
                 ; AA2_$beCc1987_ESIMASTER   ; cat_10101
                 ; AA2_$beCc1987_TRKOHW1     ; cat_10102
                 ; AA2_$beCc1987_TRKOHW2     ; cat_10102
                 ; AA2_$beCc1987_TRKTRUCK    ; cat_10102
                 ; AA2_$beCc1987_TSTINFOAW   ; cat_40101
                 ; AA2_$beCc1687_TSTINFODAT  ; cat_40101
                 ; AA2_$beCc1987_TRKUPG      ; cat_10102
                 ; AA2_$beCc1687_CORE_ESIPKG ; cat_1010103
                 ; AA2_$beCc1987_KTS250SD    ; cat_1010104
                 ; AA2_$beCc1987_ADAS_ONE    ; cat_501         ;


INSERT_UPDATE App; code[unique = true]       ; eulaContainers(code); $catalogVersion[unique = true]
                 ; AA2_$beCc1687_DCICRI      ; AA2_DCICRI
                 ; AA2_$beCc1687_DCICRIN     ; AA2_DCICRIN
                 ; AA2_$beCc1687_TSTINFODAT  ; AA2_ESItronic
                 ; AA2_$beCc1987_TSTINFOAW   ; AA2_ESItronic
                 ; AA2_$beCc1687_CORE_ESIPKG ; AA2_CoRe
                 ; AA2_$beCc1687_CSFSA500    ; AA2_FSA
                 ; AA2_$beCc1687_CSFSA7XX    ; AA2_FSA
                 ; AA2_$beCc1987_KTS250SD    ; AA2_ESItronic
