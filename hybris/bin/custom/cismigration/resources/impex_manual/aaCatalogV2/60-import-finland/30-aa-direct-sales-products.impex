#% impex.setLocale( Locale.ENGLISH );

# numerical isocode for FI, used as prefix for product code
$fiCc = 358
$aaCc = 040
$aaPackageName = com.sast.aa.fi.

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf

#local
$companyId = d13faef4-769e-43e3-94e7-fae2c2009f10
#DEV
#$companyId = 09014d91-7f1d-40b6-b4a2-646756334bcf
#DEMO
#$companyId = a8d470e0-ee94-41fd-b233-3eb88f9ea7e4

$packDiagnosticDescription = Ohjelmistopaketti ammattimaiseen diagnoostiikkaan, korjaukseen ja huoltoihin. Sen avulla voit suorittaa ohjausyksiköiden diagnostiikan ja kattavan valikoiman muita toimintoja.
$packDiagnosticDescription_sv = Ingång till professionell diagnostik, reparation och underhåll. Den möjliggör kompetent elektronisk diagnos och erbjuder ett brett utbud av andra funktioner för alla täckta fordon.
$packDiagnosticDescription_en = The entry into professional diagnosis, repair and maintenance. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.
$packAdvancedDescription = Laajennettu ohjelmistopaketti korjaamoille. Diagnostiikkapaketin lisäksi mukana on korjausohjeet ja käsikirjat. Connected Repair tarjoaa mahdollisuuden tallentaa laitedokumentit ja huoltotiedot eri työpisteille.
$packAdvancedDescription_sv = Nästa nivå av professionell verkstadsutrustning. Utöver Diagnostikpaketet medföljer instruktioner och reparationsmanualer. Connected Repair tillhandahåller service- och underhållshistorik samt reparationsinformation.
$packAdvancedDescription_en = The next level of professional workshop equipment. In addition to the Diagnostic package, instructions and manuals are included. Connected Repair provides service and maintenance history and repair information.
$packMasterDescription = Laajin ohjelmistopaketti ajoneuvon diagnostiikkaan. Se tarjoaa kaiken tarvittavan ohjainlaitediagnostiikkaa, korjausta, huoltoa, varaosia, dokumentointia ja tiedonhallintaa varten. Tekninen tuki auttaa ratkaisujen löytämisessä.
$packMasterDescription_sv = Det heltäckande paketet för professionell fordonsdiagnostik. Det innehåller all information som behövs för diagnos, reparation, underhåll, reservdelar, dokumentation och datahantering. Teknisk support hjälper dig att hitta lösningar.
$packMasterDescription_en = The fully comprehensive package for professional vehicle diagnostics. It provides all the necessary information for diagnosis, repair, maintenance, spare parts, documentation and data management. Technical Support assists you in finding solutions.

$packComponentCatDescription = Varaosaluettelo-paketti sisältää ajoneuvotiedot, toiminnot ja varaosatiedot sekä diesel- ja sähkövaraosat, mukaan lukien arkisto ja sähkövaraosat ESI[tronic]-F.
$packComponentCatDescription_sv = Reservdelskatalogpaketet innehåller applikationer, funktioner och fordonsutrustning samt dieselreservdelar och elektriska reservdelar inkl. arkiv och elektrisk reservdel ESI[tronic]-F.
$packComponentCatDescription_en = The spare parts catalog package includes the applications, functions and automotive equipment as well as the diesel spare parts and electrical spare parts incl. archive and electrical spare part ESI[tronic]-F.
$packComponentRepairDieselDescription = Diesel korjausohjeet -paketti tarjoaa tietoa varaosista ja diesel- ja sähkökomponenttien korjausohjeet. Sen avulla voidaan tunnistaa ajoneuvo, Boschin autovarusteet ja se sisältää korjausohjeita, sekä huoltotietoja.
$packComponentRepairDieselDescription_sv = Paketet Repair Diesel innehåller information om reservdelar och reparation av diesel- och elkomponenter. Det gör det möjligt att identifiera fordonet, Bosch fordonsutrustning och innehåller reparationsinstruktioner och serviceinformation.
$packComponentRepairDieselDescription_en = The Repair Diesel package provides information on spare parts and repair of diesel and electrical components. It allows identification of the vehicle, Bosch automotive equipment and includes repair instructions & service information.
$packComponentRepairElectricDescription = Ajoneuvomallien määrän lisääntyessä korjaamoiden on vaikea löytää ajantasaiset tiedot ajoneuvojen sähköjärjestelmistä käyttöönsä. Sähkökomponenttien korjausohjeet-paketti tarjoaa tukea autojen sähköjärjestelmiä koskevilla varaosatiedoilla selkeässä muodossa.
$packComponentRepairElectricDescription_sv = Det ökande antalet fordonsmodeller gör det svårt för verkstäderna att ha tillgång till uppdaterad information om fordonens elsystem. Paketet Repair Electrics ger stöd med reservdelsdata om bilens elsystem i ett tydligt format.
$packComponentRepairElectricDescription_en = The increasing number of vehicle models makes it difficult for workshops to have up-to-date information on vehicle electrical systems at their fingertips. The Repair Electrics Package provides support with spare parts data on car electrics in a clear format.

$packTruckDescription = Truck-paketti tukee korjaamoja kaikkien kevyiden- ja raskaiden hyötyajoneuvojen, perävaunujen, pakettiautojen ja linja-autojen diagnostiikassa, täydellisessä huollossa ja tehokkaassa korjauksessa.
$packTruckDescription_sv = Truck-paketet hjälper verkstäder med tillförlitlig diagnos, komplett underhåll och effektiv reparation av alla vanliga lätta och tunga kommersiella fordon, släpvagnar, skåpbilar och bussar.
$packTruckDescription_en = The Truck package supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.

$ohw1Description = Maatalouskoneiden diagnostiikkapaketissa on tietoa maatalousajoneuvojen diagnosoinnista, huollosta ja korjauksesta. Mukana on muun muassa hydraulijärjestelmien säätö- ja parametrointitoimintoja.
$ohw1Description_sv = Diagnospaketet för jordbruksmaskiner ger information om diagnos, underhåll och reparation av jordbruksfordon. Här ingår bl.a. funktioner för justering och parametrering av hydraulsystem.
$ohw1Description_en = The Agricultural Machinery Diagnostic Package provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw2Description = Maanrakennuskoneiden ja -moottoreiden diagnoosipaketti sisältää tietoa maatalousajoneuvojen diagnosoinnista, huollosta ja korjauksesta. Mukana on muun muassa hydraulijärjestelmien säätö- ja parametrointitoimintoja.
$ohw2Description_sv = Diagnospaketet för entreprenadmaskiner och motorer ger information om diagnos, underhåll och reparation av jordbruksfordon. Här ingår bl.a. funktioner för justering och parametrering av hydraulsystem.
$ohw2Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.

$thlPkwDescription = Tarvitsetko teknistä tukea auton huoltoon tai korjaukseen vai vain luotettavan toisen mielipiteen? Ota yhteyttä tukitiimiimme, niin saat nopeat ratkaisut ongelmiisi.
$thlPkwDescription_sv = Behöver du teknisk support för att underhålla eller reparera en bil, eller bara en pålitlig andra åsikt? Kontakta då vårt supportteam och få snabba och bra lösningar.
$thlPkwDescription_en = Do you need technical support to maintain or repair a car, or simply a reliable second opinion? Then contact our support team and get fast and sound solutions.
$thlPkwTruckDescription = Autoteknistä neuvontaa ja dokumentaatiota erilaisiin kevyiden ja raskaiden hyötyajoneuvojen korjauksiin.
$thlPkwTruckDescription_sv = Med omfattande täckning av märken, modeller och system kan supportteamet ge teknisk hjälp och dokumentation för olika reparationer av lätta och tunga kommersiella fordon.
$thlPkwTruckDescription_en = With extensive coverage of makes, models and systems, the support team can provide technical assistance and documentation for various light and heavy commercial vehicle repairs.
$thlTruckDescription = Tarvitsetko teknistä tukea hyötyajoneuvojen huoltoon tai korjaukseen vai vain luotettavan toisen mielipiteen? Ota yhteyttä tukitiimiimme, niin saat nopeat ratkaisut ongelmiisi.
$thlTruckDescription_sv = Behöver du teknisk support för att underhålla eller reparera en lastbil, eller bara en tillförlitlig andra åsikt? Kontakta då vårt supportteam och få snabba och bra lösningar.
$thlTruckDescription_en = Do you need technical support to maintain or repair a truck, or just a reliable second opinion? Then contact our support team and get fast and sound solutions.

$compacFsa500Description = CompacSoft[plus] FSA 500:lle on varustettu ESI[tronic] esiasetetuilla komponenttitesteillä, ja se voidaan liittää olemassa oleviin järjestelmiin tai sitä voidaan käyttää korjaamon testausjärjestelmän asteittaiseen laajentamiseen.
$compacFsa500Description_sv = CompacSoft[plus] för FSA 500 är utrustad med förinställda komponenttester och kan anslutas till befintliga system, samt användas för att gradvis bygga ut ditt verkstadstestsystem.
$compacFsa500Description_en = The CompacSoft[plus] for FSA 500 is equipped with preset component tests and can be connected to existing systems, as well as used to gradually expand your workshop test system.
$compacFsa7xxDescription = CompacSoft[plus] FSA 7xx:n avulla kaikkien ajoneuvon mittaustehtävien mukavuutta lisäävät entisestään valikko-ohjatut testivaiheet, valinnaiset ajoneuvokohtaiset asetusarvot sekä todellisten arvojen näyttö.
$compacFsa7xxDescription_sv = Med CompacSoft[plus] för FSA 7xx ökar komforten för alla mätuppgifter på fordonet ytterligare genom de menystyrda teststegen, de fordonsspecifika inställningsvärdena (tillval) samt visningen av de faktiska värdena.
$compacFsa7xxDescription_en = With CompacSoft[plus] for FSA 7xx, the comfort for all measuring tasks on the vehicle is further increased by the menu-guided test steps, the optional vehicle-specific set values, as well as the display of the actual values.
$criDescription = CRI DCI 700 -ohjelmisto tarjoaa ajantasaiset tiedot common rail -järjestelmien pietsosuuttimien testaukseen ja varmistaa sujuvat prosessit.
$criDescription_sv = Programvaran ESI[tronic] CRI för DCI 700 tillhandahåller aktuella data, säkerställer smidiga processer och inkluderar testning av piezoinsprutare för common rail-system.
$criDescription_en = The ESI[tronic] CRI for DCI 700 software provides up-to-date data, ensures smooth processes and includes testing of piezo injectors for common rail systems.
$crinDescription = CRIN DCI 700 -ohjelmisto tarjoaa ajantasaiset tiedot common Rail -järjestelmien magneettiventtiilisuuttimien testaukseen ja varmistaa sujuvat prosessit.
$crinDescription_sv = Programvaran ESI[tronic] CRIN för DCI 700 tillhandahåller aktuella data, säkerställer smidiga processer och inkluderar testning av solenoidventilinsprutare för common rail-system.
$crinDescription_en = The ESI[tronic] CRIN for DCI 700 software provides up-to-date data, ensures smooth processes and includes the testing of solenoid valve injectors for common rail systems.

# TODO: UPDATE TRANSLATION
$adasDescription = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.
$adasDescription_sv = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.
$adasDescription_en = Fully integrated into the ESI[tronic] or as stand-alone application: The ADAS One Solution provides up-to-date data and user guidance to ensures precise calibration and adjustment of driver assistance systems. Significantly shortened and optimized workflows despite the increasing number of sensor technologies and calibration routines.

$coReName = Core - Connected Repair
$coReName_sv = CoRe Server + Online
$coReName_en = CoRe Server + Online
$coReDescription = Bosch CoRe on ohjelmisto, joka yhdistää korjaamon laitteet, ajoneuvon ja korjaustiedot. Voit luoda työasemakohtaisia työkortteja ja kerää tietoja useista Bosch-laitteista yhdistääksesi ne yhdeksi asiakasraportiksi.
$coReDescription_sv = Bosch Connected Repair är en programvara som kopplar samman verkstadsutrustning, fordons- och reparationsdata. Oavsett om det gäller fel eller att spara data och bilder i enlighet med den allmänna dataskyddsförordningen - CoRe har anpassats för att möta kundernas behov.
$coReDescription_en = Bosch Connected Repair is software that connects workshop equipment, vehicle and repair data. Whether in the event of malfunctions or the storage of data and images in accordance with the Basic Data Protection Regulation - CoRe has been adapted to the needs of customers.

$kts250SDDescription = Pääsy diagnostiikkaan, sekä korjaus- ja huolto toimintoihin erityisesti KTS 250:lle. Se mahdollistaa tehokkaat diagnostiikkatyöt ja tarjoaa laajan valikoiman muita toimintoja ajoneuvoille.
$kts250SDDescription_sv = Inträde till professionell diagnos, reparation och underhåll specifikt för KTS 250. Det möjliggör kompetent elektrisk diagnos och erbjuder ett brett utbud av andra funktioner för alla täckta fordon.
$kts250SDDescription_en = The entry into professional diagnosis, repair and maintenance specifically for KTS 250. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.

$infoartWDescription = ESI[tronic] 2.0 W sisältää tiedot testiarvoista diesel rivipumppuyhdistelmille, sekä VE-pumpuille. Se mahdollistaa koko testiprosessin mittausarvojen määrittämisestä raportin tulostamiseen ja testivaiheiden näyttämiseen optimaalisessa järjestyksessä.
$infoartWDescription_sv = ESI[tronic] 2.0 Infoart W innehåller information om dieseltestvärden för in-line pumpkombinationer och för VE-pumpar, den kompletta testprocessen från fastställandet av mätvärden till utskrift av rapporten och visning av teststegen i optimal sekvens.
$infoartWDescription_en = The ESI[tronic] 2.0 Infoart W contains information about diesel test values for in-line pump combinations as well as for VE pumps, the complete test process test procedure from the determination of the measured values to the printout of the report and the display of the test steps in the optimum sequence.

$infoartTestdataDescription = ESI[tronic] 2.0-Infotype Testdata (CD) sisältää testiarvot Boschin Common Rail -korkeapainepumpuille, Common Rail -ruiskutussuuttimille ja VP 29 / 30 / 44 -jakajaruiskutuspumpuille.
$infoartTestdataDescription_sv = ESI[tronic] 2.0-Infotype Testdata (CD) innehåller testvärden för Bosch Common Rail högtryckspumpar, Common Rail insprutare och VP 29 / 30 / 44 fördelningsinsprutningspumpar.
$infoartTestdataDescription_en = The ESI[tronic] 2.0-Infotype Testdata (CD) contains test values for Bosch Common Rail high pressure pumps, Common Rail injectors and VP 29 / 30 / 44 distributor injection pumps.
$truckUpgradeDescription = Truck laajennus -paketti on tarkoitettu henkilöautopuolen ESI[tronic] käyttäjille, ja se tukee korjaamoja kaikkien kevyiden- ja raskaiden hyötyajoneuvojen, perävaunujen, pakettiautojen ja linja-autojen diagnostiikassa, täydellisessä huollossa ja tehokkaassa korjauksessa.
$truckUpgradeDescription_sv = Truck Upgrade-paketet är avsett för användare av ESI[tronic] Car och hjälper verkstäder med tillförlitlig diagnos, komplett underhåll och effektiv reparation av alla vanliga lätta och tunga kommersiella fordon, släpvagnar, skåpbilar och bussar.
$truckUpgradeDescription_en = The Truck Upgrade package is dedicated to ESI[tronic] Car users and supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.

$crr950DieselInjectorsRepairSoftDescription = CRR 950 -dieselsuuttimien korjausohjelmisto
$crr950DieselInjectorsRepairSoftDescription_en = The CRR 950 guides and support the professional repairs of magnetic solenoid common-rail injectors from Bosch
$crr950DieselInjectorsRepairSoftDescription_sv = CRR 950 Programvara för reparation av dieselinjektorer

$compacFsa500Full3YBrimName = CompacSoft[plus] FSA 500 (3 years)
$compacFsa500SubsBrimName = CompacSoft[plus] FSA 500
$compacFsa7xxFull3YBrimName = CompacSoft[plus] FSA 7xx (3 years)
$compacFsa7xxSubsBrimName = CompacSoft[plus] FSA 7xx
$coreS1BrimName = CoRe for ESI 2.0 packages

$criSubsBrimName = Component DCI-CRI
$criFull3YBrimName = Component DCI-CRI (3 year)
$crinSubsBrimName = Component DCI-CRIN
$criSubsM3BrimName = Component DCI-CRI_Multi-users_3
$crinFull3YBrimName = Component DCI-CRIN (3 year)

$packAdvancedSubsS1BrimName = ESI 2.0 Advanced Unlimited
$packAdvancedSubsM3BrimName = ESI 2.0 Advanced Unlimited  Multi
$packAdvancedFull3YS1BrimName = ESI 2.0 Advanced 3 years
$packAdvancedFull3YM3BrimName = ESI 2.0 Advanced 3 years Multi
$packComponentCatSubsS1BrimName = ESI 2.0 ComponentCatalog D+E Unlimited
$packComponentCatSubsM3BrimName = ESI 2.0 ComponentCatalog D+E Unlim Multi
$packComponentCatFull3YS1BrimName = ESI 2.0 ComponentCatalog D+E 3 years
$packComponentCatFull3YM3BrimName = ESI 2.0 ComponentCatalog D+E 3y Multi
$packComponentRepairDieselSubsS1BrimName = ESI 2.0 ComponentRepair D+E Unlimited
$packComponentRepairDieselSubsM3BrimName = ESI 2.0 ComponentRepair D+E Unlim Multi
$packComponentRepairDieselFull3YS1BrimName = ESI 2.0 ComponentRepair D+E 3 years
$packComponentRepairDieselFull3YM3BrimName = ESI 2.0 ComponentRepair D+E 3y Multi
$packComponentRepairElectricSubsS1BrimName = ESI 2.0 ComponentRepair E Unlimited
$packComponentRepairElectricSubsM3BrimName = ESI 2.0 ComponentRepair E Unlim Multi
$packComponentRepairElectricFull3YS1BrimName = ESI 2.0 ComponentRepair E 3 years
$packComponentRepairElectricFull3YM3BrimName = ESI 2.0 ComponentRepair E 3 years Multi
$packDiagnosticSubsS1BrimName = ESI 2.0 Diagnostic Unlimited
$packDiagnosticSubsM3BrimName = ESI 2.0 Diagnostic Unlimited Multi
$packDiagnosticFull3YS1BrimName = ESI 2.0 Diagnostic 3 years
$packDiagnosticFull3YM3BrimName = ESI 2.0 Diagnostic 3 years Multi
$packMasterSubsS1BrimName = ESI 2.0 Master Unlimited
$packMasterSubsM3BrimName = ESI 2.0 Master Unlimited Multi
$packMasterFull3YS1BrimName = ESI 2.0 Master 3 years
$packMasterFull3YM3BrimName = ESI 2.0 Master 3 years Multi

$ohw1SubsS1BrimName = ESI[tronic] 2.0 Truck OHW I
$ohw1SubsM3BrimName = ESI[tronic] 2.0 Truck OHW I Multi
$ohw1Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW I (3 years)
$ohw1Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW I (3y) Multi

$ohw2SubsS1BrimName = ESI[tronic] 2.0 Truck OHW II
$ohw2SubsM3BrimName = ESI[tronic] 2.0 Truck OHW II Multi
$ohw2Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW II (3 years)
$ohw2Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW II (3y) Multi

$packTruckSubsS1BrimName = ESI[tronic] 2.0 Truck
$packTruckSubsM3BrimName = ESI[tronic] 2.0 Truck Multi
$packTruckFull3YS1BrimName = ESI[tronic] 2.0 Truck (3 years)
$packTruckFull3YM3BrimName = ESI[tronic] 2.0 Truck (3 years) Multi
$packTruckFullBrimName = ESI[tronic] 2.0 Truck (Einmalkauf)
$infoartWSubsBrimName = ESI[tronic] W Diesel Test Data (WP)
$infoartWFull3YBrimName = ESI[tronic] W (3 Jahre)
$thlPkwSubsBrimName = THL use technical hotline
$thlPkwFull3YBrimName = Technische Hotline LKW (3 Jahre)
$thlTrkSubsBrimName = Technische Hotline ESI[tronic] for Truck
$thlPkwTrkSubsBrimName = Technische Hotline fuer LKWs und PKWs
$infoartTestdataSubsBrimName = Testdata VP-M/CP
$truckUpgradeSubsS1BrimName = ESI[tronic] 2.0 Truck Upgrade
$truckUpgradeSubsM3BrimName = ESI[tronic] 2.0 Truck Upgrade Multi
$truckUpgradeFull3YS1BrimName = ESI[tronic] 2.0 Truck Upgrade (3 years)
$truckUpgradeFull3YM3BrimName = ESI[tronic] 2.0 Truck Upgrade (3y) Multi
$kts250SDFullBrimName = KTS 250 SD (OTP one time purchase)
$kts250SDSubsBrimName = KTS 250 SD ECU Diagnosis

$crr950DieselInjectorsRepairSoftwareBrimName1L = CRR 950 Diesel Injectors (1 License)
$crr950DieselInjectorsRepairSoftwareBrimName3L = CRR 950 Diesel Injectors (3 Licenses)

$ohw3Description = In dem Diagnose Paket Baumaschinen und Motoren stehen Informationen zur Diagnose, Wartung und Reparatur von landwirtschaftlich genutzten Fahrzeugen zur Verfügung. Enthalten sind unter anderem Einstell- und Parametrierfunktionen an Hydrauliksystemen.
$ohw3Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.

$ohw3S1BrimName = ESI[tronic] Truck OHW III Unlimited
$ohw3M3BrimName = ESI[tronic] Truck OHW III Unlimited Multi
$ohw3Full3YM3BrimName = ESI[tronic] Truck OHW III (3y) Multi
$fsa7xxMulti = CompacSoft[plus] FSA 7xx Multi
$fsa7xx3YearsMulti = CompacSoft[plus] FSA 7xx (3 years) Multi
$fsa5xxMulti = CompacSoft[plus] FSA 500 Multi
$fsa5xx3YearsMulti = CompacSoft[plus] FSA 500 (3 years)Multi
$adasSubsS1BrimName = ADAS ONE - DATA
$adasFull3YS1BrimName = ADAS ONE - DATA (3 years)

$countryRestricted = true
# @formatter:off
$enabledIn = FI
# @formatter:on

INSERT_UPDATE App; code[unique = true]       ; packageName                    ; name[lang = fi]                          ; summary[lang = fi]                          ; description[lang = fi]                      ; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$fiCc1687_CSFSA500    ; $aaPackageName1687_CSFSA500    ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description                    ; $compacFsa500Description                    ;
                 ; AA2_$fiCc1687_CSFSA7XX    ; $aaPackageName1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription                    ; $compacFsa7xxDescription                    ;
                 ; AA2_$fiCc1687_DCICRI      ; $aaPackageName1687_DCICRI      ; Testiarvot DCI-CRI                       ; $criDescription                             ; $criDescription                             ;
                 ; AA2_$fiCc1687_DCICRIN     ; $aaPackageName1687_DCICRIN     ; Testiarvot DCI-CRIN                      ; $crinDescription                            ; $crinDescription                            ;
                 ; AA2_$fiCc1987_ESIADV      ; $aaPackageName1987_ESIADV      ; ESI[tronic] 2.0 Advanced                 ; $packAdvancedDescription                    ; $packAdvancedDescription                    ;
                 ; AA2_$fiCc1987_ESIREPCAT   ; $aaPackageName1987_ESIREPCAT   ; ESI[tronic] 2.0 Luettelo D+E             ; $packComponentCatDescription                ; $packComponentCatDescription                ;
                 ; AA2_$fiCc1987_ESIREPD     ; $aaPackageName1987_ESIREPD     ; ESI[tronic] 2.0 Komponenttikorjaus D+E   ; $packComponentRepairDieselDescription       ; $packComponentRepairDieselDescription       ;
                 ; AA2_$fiCc1987_ESIREPE     ; $aaPackageName1987_ESIREPE     ; ESI[tronic] 2.0 Komponenttikorjaus E     ; $packComponentRepairElectricDescription     ; $packComponentRepairElectricDescription     ;
                 ; AA2_$fiCc1987_ESIDIAG     ; $aaPackageName1987_ESIDIAG     ; ESI[tronic] 2.0 Diagnostiikka            ; $packDiagnosticDescription                  ; $packDiagnosticDescription                  ;
                 ; AA2_$fiCc1987_ESIMASTER   ; $aaPackageName1987_ESIMASTER   ; ESI[tronic] 2.0 Master                   ; $packMasterDescription                      ; $packMasterDescription                      ;
                 ; AA2_$fiCc1987_TRKOHW1     ; $aaPackageName1987_TRKOHW1     ; ESI[tronic] 2.0 OHW I                    ; $ohw1Description                            ; $ohw1Description                            ;
                 ; AA2_$fiCc1987_TRKOHW2     ; $aaPackageName1987_TRKOHW2     ; ESI[tronic] 2.0 OHW II                   ; $ohw2Description                            ; $ohw2Description                            ;
                 ; AA2_$fiCc1987_TRKTRUCK    ; $aaPackageName1987_TRKTRUCK    ; ESI[tronic] 2.0 Truck                    ; $packTruckDescription                       ; $packTruckDescription                       ;
                 ; AA2_$fiCc1987_TSTINFOAW   ; $aaPackageName1987_TSTINFOAW   ; ESI[tronic] W                            ; $infoartWDescription                        ; $infoartWDescription                        ;
                 ; AA2_$fiCc1687_TSTINFODAT  ; $aaPackageName1687_TSTINFODAT  ; Testiarvot VP-M/CP                       ; $infoartTestdataDescription                 ; $infoartTestdataDescription                 ;
                 ; AA2_$fiCc1987_THLPKW      ; $aaPackageName1687_THLPKW      ; Autotekninen neuvonta, henkilöautot      ; $thlPkwDescription                          ; $thlPkwDescription                          ;
                 ; AA2_$fiCc1987_TRKUPG      ; $aaPackageName1987_TRUCKUPG    ; ESI[tronic] 2.0 Truck laajennus          ; $truckUpgradeDescription                    ; $truckUpgradeDescription                    ;
                 ; AA2_$fiCc1687_CORE_ESIPKG ; $aaPackageName1687_CORE_ESIPKG ; $coReName                                ; $coReDescription                            ; $coReDescription                            ;
                 ; AA2_$fiCc1987_KTS250SD    ; $aaPackageName1987_KTS250      ; KTS 250 SD ECU -diagnostiikka            ; $kts250SDDescription                        ; $kts250SDDescription                        ;
                 ; AA2_$fiCc1687_CRR950      ; $aaPackageName1687_CRR950      ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription ; $crr950DieselInjectorsRepairSoftDescription ;
                 ; AA2_$fiCc1987_TRKOHW3     ; $aaPackageName1987_TRKOHW3     ; Off Highway III (OHW III)                ; $ohw3Description                            ; $ohw3Description                            ;
                 ; AA2_$fiCc1987_ADAS_ONE    ; $aaPackageName1987_ADAS_ONE    ; ADAS One Solution                        ; $adasDescription                            ; $adasDescription                            ;

# English translations
INSERT_UPDATE App; code[unique = true]       ; name[lang = en]                          ; summary[lang = en]                             ; description[lang = en]                     ; $catalogVersion[unique = true];
                 ; AA2_$fiCc1687_CSFSA500    ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description_en                    ; $compacFsa500Description_en                ;
                 ; AA2_$fiCc1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription_en                    ; $compacFsa7xxDescription_en                ;
                 ; AA2_$fiCc1687_DCICRI      ; Component DCI-CRI                        ; $criDescription_en                             ; $criDescription_en                         ;
                 ; AA2_$fiCc1687_DCICRIN     ; Component DCI-CRIN                       ; $crinDescription_en                            ; $crinDescription_en                        ;
                 ; AA2_$fiCc1987_ESIADV      ; ESI[tronic] 2.0 Advanced                 ; $packAdvancedDescription_en                    ; $packAdvancedDescription_en                ;
                 ; AA2_$fiCc1987_ESIREPCAT   ; ESI[tronic] 2.0 ComponentCat D+E         ; $packComponentCatDescription_en                ; $packComponentCatDescription_en            ;
                 ; AA2_$fiCc1987_ESIREPD     ; ESI[tronic] 2.0 ComponentRepair D+E      ; $packComponentRepairDieselDescription_en       ; $packComponentRepairDieselDescription_en   ;
                 ; AA2_$fiCc1987_ESIREPE     ; ESI[tronic] 2.0 ComponentRepair E        ; $packComponentRepairElectricDescription_en     ; $packComponentRepairElectricDescription_en ;
                 ; AA2_$fiCc1987_ESIDIAG     ; ESI[tronic] 2.0 Diagnostic               ; $packDiagnosticDescription_en                  ; $packDiagnosticDescription_en              ;
                 ; AA2_$fiCc1987_ESIMASTER   ; ESI[tronic] 2.0 Master                   ; $packMasterDescription_en                      ; $packMasterDescription_en                  ;
                 ; AA2_$fiCc1987_TRKOHW1     ; ESI[tronic] 2.0 Truck OHW I              ; $ohw1Description_en                            ; $ohw1Description_en                        ;
                 ; AA2_$fiCc1987_TRKOHW2     ; ESI[tronic] 2.0 Truck OHW II             ; $ohw2Description_en                            ; $ohw2Description_en                        ;
                 ; AA2_$fiCc1987_TRKTRUCK    ; ESI[tronic] 2.0 Truck                    ; $packTruckDescription_en                       ; $packTruckDescription_en                   ;
                 ; AA2_$fiCc1987_TSTINFOAW   ; ESI[tronic] W                            ; $infoartWDescription_en                        ; $infoartWDescription_en                    ;
                 ; AA2_$fiCc1687_TSTINFODAT  ; Testdata VP-M/CP                         ; $infoartTestdataDescription_en                 ; $infoartTestdataDescription_en             ;
                 ; AA2_$fiCc1987_THLPKW      ; Technical Hotline Car                    ; $thlPkwDescription_en                          ; $thlPkwDescription_en                      ;
                 ; AA2_$fiCc1987_TRKUPG      ; ESI[tronic] 2.0 Truck Upgrade            ; $truckUpgradeDescription_en                    ; $truckUpgradeDescription_en                ;
                 ; AA2_$fiCc1687_CORE_ESIPKG ; $coReName                                ; $coReDescription_en                            ; $coReDescription_en                        ;
                 ; AA2_$fiCc1987_KTS250SD    ; KTS 250 SD ECU Diagnosis                 ; $kts250SDDescription_en                        ; $kts250SDDescription_en                    ;
                 ; AA2_$fiCc1687_CRR950      ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription_en ; $crr950DieselInjectorsRepairSoftDescription_en
                 ; AA2_$fiCc1987_TRKOHW3     ; ESI[tronic] Truck OHW III                ; $ohw3Description_en                            ; $ohw3Description_en                        ;
                 ; AA2_$fiCc1987_ADAS_ONE    ; ADAS One Solution                        ; $adasDescription_en                            ; $adasDescription_en                        ;

# Swedish translations
INSERT_UPDATE App; code[unique = true]       ; name[lang = sv]                          ; summary[lang = sv]                             ; description[lang = sv]                         ; $catalogVersion[unique = true];
                 ; AA2_$fiCc1687_CSFSA500    ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description_sv                    ; $compacFsa500Description_sv                    ;
                 ; AA2_$fiCc1687_CSFSA7XX    ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription_sv                    ; $compacFsa7xxDescription_sv                    ;
                 ; AA2_$fiCc1687_DCICRI      ; Komponent DCI-CRI                        ; $criDescription_sv                             ; $criDescription_sv                             ;
                 ; AA2_$fiCc1687_DCICRIN     ; Komponent DCI-CRIN                       ; $crinDescription_sv                            ; $crinDescription_sv                            ;
                 ; AA2_$fiCc1987_ESIADV      ; ESI[tronic] 2.0 Advanced                 ; $packAdvancedDescription_sv                    ; $packAdvancedDescription_sv                    ;
                 ; AA2_$fiCc1987_ESIREPCAT   ; ESI[tronic] 2.0 ComponentCat D+E         ; $packComponentCatDescription_sv                ; $packComponentCatDescription_sv                ;
                 ; AA2_$fiCc1987_ESIREPD     ; ESI[tronic] 2.0 ComponentRepair D+E      ; $packComponentRepairDieselDescription_sv       ; $packComponentRepairDieselDescription_sv       ;
                 ; AA2_$fiCc1987_ESIREPE     ; ESI[tronic] 2.0 ComponentRepair E        ; $packComponentRepairElectricDescription_sv     ; $packComponentRepairElectricDescription_sv     ;
                 ; AA2_$fiCc1987_ESIDIAG     ; ESI[tronic] 2.0 Diagnostic               ; $packDiagnosticDescription_sv                  ; $packDiagnosticDescription_sv                  ;
                 ; AA2_$fiCc1987_ESIMASTER   ; ESI[tronic] 2.0 Master                   ; $packMasterDescription_sv                      ; $packMasterDescription_sv                      ;
                 ; AA2_$fiCc1987_TRKOHW1     ; ESI[tronic] 2.0 Truck OHW I              ; $ohw1Description_sv                            ; $ohw1Description_sv                            ;
                 ; AA2_$fiCc1987_TRKOHW2     ; ESI[tronic] 2.0 Truck OHW II             ; $ohw2Description_sv                            ; $ohw2Description_sv                            ;
                 ; AA2_$fiCc1987_TRKTRUCK    ; ESI[tronic] 2.0 Truck                    ; $packTruckDescription_sv                       ; $packTruckDescription_sv                       ;
                 ; AA2_$fiCc1987_TSTINFOAW   ; ESI[tronic] W                            ; $infoartWDescription_sv                        ; $infoartWDescription_sv                        ;
                 ; AA2_$fiCc1687_TSTINFODAT  ; Testdata VP-M/CP                         ; $infoartTestdataDescription_sv                 ; $infoartTestdataDescription_sv                 ;
                 ; AA2_$fiCc1987_THLPKW      ; Teknisk hotline Bil                      ; $thlPkwDescription_sv                          ; $thlPkwDescription_sv                          ;
                 ; AA2_$fiCc1987_TRKUPG      ; ESI[tronic] 2.0 Truck Upgrade            ; $truckUpgradeDescription_sv                    ; $truckUpgradeDescription_sv                    ;
                 ; AA2_$fiCc1687_CORE_ESIPKG ; $coReName                                ; $coReDescription_sv                            ; $coReDescription_sv                            ;
                 ; AA2_$fiCc1987_KTS250SD    ; KTS 250 SD ECU -diagnos                  ; $kts250SDDescription_sv                        ; $kts250SDDescription_sv                        ;
                 ; AA2_$fiCc1687_CRR950      ; CRR 950 Diesel Injectors Repair Software ; $crr950DieselInjectorsRepairSoftDescription_sv ; $crr950DieselInjectorsRepairSoftDescription_sv ;
                 ; AA2_$fiCc1987_TRKOHW3     ; Off Highway III (OHW III)                ; $ohw3Description                               ; $ohw3Description                               ;
                 ; AA2_$fiCc1987_ADAS_ONE    ; ADAS One Solution                        ; $adasDescription_sv                            ; $adasDescription_sv                            ;

INSERT_UPDATE ProductContainer; code[unique = true]        ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$fiCc1687_CSFSA500    ; AA2_$fiCc1687_CSFSA500
                              ; pcaa_$fiCc1687_CSFSA7XX    ; AA2_$fiCc1687_CSFSA7XX
                              ; pcaa_$fiCc1687_DCICRI      ; AA2_$fiCc1687_DCICRI
                              ; pcaa_$fiCc1687_DCICRIN     ; AA2_$fiCc1687_DCICRIN
                              ; pcaa_$fiCc1987_ESIADV      ; AA2_$fiCc1987_ESIADV
                              ; pcaa_$fiCc1987_ESIREPCAT   ; AA2_$fiCc1987_ESIREPCAT
                              ; pcaa_$fiCc1987_ESIREPD     ; AA2_$fiCc1987_ESIREPD
                              ; pcaa_$fiCc1987_ESIREPE     ; AA2_$fiCc1987_ESIREPE
                              ; pcaa_$fiCc1987_ESIDIAG     ; AA2_$fiCc1987_ESIDIAG
                              ; pcaa_$fiCc1987_ESIMASTER   ; AA2_$fiCc1987_ESIMASTER

                              ; pcaa_$fiCc1987_TRKOHW1     ; AA2_$fiCc1987_TRKOHW1
                              ; pcaa_$fiCc1987_TRKOHW2     ; AA2_$fiCc1987_TRKOHW2

                              ; pcaa_$fiCc1987_TRKTRUCK    ; AA2_$fiCc1987_TRKTRUCK
                              ; pcaa_$fiCc1987_TSTINFOAW   ; AA2_$fiCc1987_TSTINFOAW
                              ; pcaa_$fiCc1687_TSTINFODAT  ; AA2_$fiCc1687_TSTINFODAT
                              ; pcaa_$fiCc1987_TRKUPG      ; AA2_$fiCc1987_TRKUPG
                              ; pcaa_$fiCc1987_THLPKW      ; AA2_$fiCc1987_THLPKW
                              ; pcaa_$fiCc1687_CORE_ESIPKG ; AA2_$fiCc1687_CORE_ESIPKG
                              ; pcaa_$fiCc1987_KTS250SD    ; AA2_$fiCc1987_KTS250SD
                              ; pcaa_$fiCc1687_CRR950      ; AA2_$fiCc1687_CRR950
                              ; pcaa_$fiCc1987_TRKOHW3     ; AA2_$fiCc1987_TRKOHW3
                              ; pcaa_$fiCc1987_ADAS_ONE    ; AA2_$fiCc1987_ADAS_ONE


INSERT_UPDATE AppLicense; code[unique = true] ; $baseProduct              ; sellerProductId; brimName[lang = en]                            ; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); specifiedPrice; enabledCountries(isocode); billingSystemStatus(code)[default = NEW]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; availabilityStatus(code)[default = PUBLISHED]
                        ; AA2_$fiCc1687P15060 ; AA2_$fiCc1687_CSFSA500    ; 1687P15060     ; $compacFsa500SubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 110.00        ; $enabledIn
                        ; AA2_$fiCc1687P15063 ; AA2_$fiCc1687_CSFSA500    ; 1687P15063     ; $compacFsa500Full3YBrimName                    ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 290.00        ; $enabledIn
                        ; AA2_$fiCc1687P15048 ; AA2_$fiCc1687_CSFSA7XX    ; 1687P15048     ; $compacFsa7xxFull3YBrimName                    ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 897.60        ; $enabledIn
                        ; AA2_$fiCc1687P15045 ; AA2_$fiCc1687_CSFSA7XX    ; 1687P15045     ; $compacFsa7xxSubsBrimName                      ;                                          ; runtime_subs_unlimited ; <ignore>        ; 340.00        ; $enabledIn
                        ; AA2_$fiCc1687P15093 ; AA2_$fiCc1687_DCICRI      ; 1687P15093     ; $criFull3YBrimName                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 911.00        ; $enabledIn
                        ; AA2_$fiCc1687P15090 ; AA2_$fiCc1687_DCICRI      ; 1687P15090     ; $criSubsBrimName                               ;                                          ; runtime_subs_unlimited ; <ignore>        ; 345.00        ; $enabledIn
                        ; AA2_$fiCc1687P15102 ; AA2_$fiCc1687_DCICRI      ; 1687P15102     ; $criSubsM3BrimName                             ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 415.00        ; $enabledIn
                        ; AA2_$fiCc1687P15103 ; AA2_$fiCc1687_DCICRIN     ; 1687P15103     ; $crinFull3YBrimName                            ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 911.00        ; $enabledIn
                        ; AA2_$fiCc1687P15100 ; AA2_$fiCc1687_DCICRIN     ; 1687P15100     ; $crinSubsBrimName                              ;                                          ; runtime_subs_unlimited ; <ignore>        ; 345.00        ; $enabledIn
                        ; AA2_$fiCc1687P15107 ; AA2_$fiCc1687_DCICRIN     ; 1687P15107     ; $criSubsM3BrimName                             ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 415.00        ; $enabledIn
                        ; AA2_$fiCc1987P12843 ; AA2_$fiCc1987_ESIADV      ; 1987P12843     ; $packAdvancedFull3YS1BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3920.00       ; $enabledIn
                        ; AA2_$fiCc1987P12840 ; AA2_$fiCc1987_ESIADV      ; 1987P12840     ; $packAdvancedSubsS1BrimName                    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1485.00       ; $enabledIn
                        ; AA2_$fiCc1987P12846 ; AA2_$fiCc1987_ESIADV      ; 1987P12846     ; $packAdvancedFull3YM3BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 4264.00       ; $enabledIn
                        ; AA2_$fiCc1987P12847 ; AA2_$fiCc1987_ESIADV      ; 1987P12847     ; $packAdvancedSubsM3BrimName                    ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1615.00       ; $enabledIn
                        ; AA2_$fiCc1987P12988 ; AA2_$fiCc1987_ESIREPCAT   ; 1987P12988     ; $packComponentCatFull3YS1BrimName              ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 845.00        ; $enabledIn
                        ; AA2_$fiCc1987P12998 ; AA2_$fiCc1987_ESIREPCAT   ; 1987P12998     ; $packComponentCatSubsS1BrimName                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 320.00        ; $enabledIn
                        ; AA2_$fiCc1987P12783 ; AA2_$fiCc1987_ESIREPCAT   ; 1987P12783     ; $packComponentCatFull3YM3BrimName              ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1030.00       ; $enabledIn
                        ; AA2_$fiCc1987P12784 ; AA2_$fiCc1987_ESIREPCAT   ; 1987P12784     ; $packComponentCatSubsM3BrimName                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 390.00        ; $enabledIn
                        ; AA2_$fiCc1987P12973 ; AA2_$fiCc1987_ESIREPD     ; 1987P12973     ; $packComponentRepairDieselFull3YS1BrimName     ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1769.00       ; $enabledIn
                        ; AA2_$fiCc1987P12970 ; AA2_$fiCc1987_ESIREPD     ; 1987P12970     ; $packComponentRepairDieselSubsS1BrimName       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 670.00        ; $enabledIn
                        ; AA2_$fiCc1987P12296 ; AA2_$fiCc1987_ESIREPD     ; 1987P12296     ; $packComponentRepairDieselFull3YM3BrimName     ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1954.90       ; $enabledIn
                        ; AA2_$fiCc1987P12297 ; AA2_$fiCc1987_ESIREPD     ; 1987P12297     ; $packComponentRepairDieselSubsM3BrimName       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 740.00        ; $enabledIn
                        ; AA2_$fiCc1987P12993 ; AA2_$fiCc1987_ESIREPE     ; 1987P12993     ; $packComponentRepairElectricFull3YS1BrimName   ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 766.00        ; $enabledIn
                        ; AA2_$fiCc1987P12990 ; AA2_$fiCc1987_ESIREPE     ; 1987P12990     ; $packComponentRepairElectricSubsS1BrimName     ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 290.00        ; $enabledIn
                        ; AA2_$fiCc1987P12294 ; AA2_$fiCc1987_ESIREPE     ; 1987P12294     ; $packComponentRepairElectricFull3YM3BrimName   ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 950.00        ; $enabledIn
                        ; AA2_$fiCc1987P12295 ; AA2_$fiCc1987_ESIREPE     ; 1987P12295     ; $packComponentRepairElectricSubsM3BrimName     ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 360.00        ; $enabledIn
                        ; AA2_$fiCc1987P12823 ; AA2_$fiCc1987_ESIDIAG     ; 1987P12823     ; $packDiagnosticFull3YS1BrimName                ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 2125.00       ; $enabledIn
                        ; AA2_$fiCc1987P12820 ; AA2_$fiCc1987_ESIDIAG     ; 1987P12820     ; $packDiagnosticSubsS1BrimName                  ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 805.00        ; $enabledIn
                        ; AA2_$fiCc1987P12822 ; AA2_$fiCc1987_ESIDIAG     ; 1987P12822     ; $packDiagnosticFull3YM3BrimName                ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 2486.00       ; $enabledIn
                        ; AA2_$fiCc1987P12824 ; AA2_$fiCc1987_ESIDIAG     ; 1987P12824     ; $packDiagnosticSubsM3BrimName                  ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 935.00        ; $enabledIn
                        ; AA2_$fiCc1987P12913 ; AA2_$fiCc1987_ESIMASTER   ; 1987P12913     ; $packMasterFull3YS1BrimName                    ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 5518.00       ; $enabledIn
                        ; AA2_$fiCc1987P12910 ; AA2_$fiCc1987_ESIMASTER   ; 1987P12910     ; $packMasterSubsS1BrimName                      ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 2090.00       ; $enabledIn
                        ; AA2_$fiCc1987P12916 ; AA2_$fiCc1987_ESIMASTER   ; 1987P12916     ; $packMasterFull3YM3BrimName                    ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 5861.00       ; $enabledIn
                        ; AA2_$fiCc1987P12917 ; AA2_$fiCc1987_ESIMASTER   ; 1987P12917     ; $packMasterSubsM3BrimName                      ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 2220.00       ; $enabledIn
                        ; AA2_$fiCc1987P12263 ; AA2_$fiCc1987_TRKOHW1     ; 1987P12263     ; $ohw1Full3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1769.00       ; $enabledIn
                        ; AA2_$fiCc1987P12260 ; AA2_$fiCc1987_TRKOHW1     ; 1987P12260     ; $ohw1SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 670.00        ; $enabledIn
                        ; AA2_$fiCc1987P12262 ; AA2_$fiCc1987_TRKOHW1     ; 1987P12262     ; $ohw1SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 800.00        ; $enabledIn
                        ; AA2_$fiCc1987P12265 ; AA2_$fiCc1987_TRKOHW1     ; 1987P12265     ; $ohw1Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 2112.00       ; $enabledIn
                        ; AA2_$fiCc1987P12280 ; AA2_$fiCc1987_TRKOHW2     ; 1987P12280     ; $ohw2Full3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3155.00       ; $enabledIn
                        ; AA2_$fiCc1987P12278 ; AA2_$fiCc1987_TRKOHW2     ; 1987P12278     ; $ohw2SubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1195.00       ; $enabledIn
                        ; AA2_$fiCc1987P12275 ; AA2_$fiCc1987_TRKOHW2     ; 1987P12275     ; $ohw2SubsM3BrimName                            ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1325.00       ; $enabledIn
                        ; AA2_$fiCc1987P12276 ; AA2_$fiCc1987_TRKOHW2     ; 1987P12276     ; $ohw2Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 3498.00       ; $enabledIn
                        ; AA2_$fiCc1987P12402 ; AA2_$fiCc1987_TRKTRUCK    ; 1987P12402     ; $packTruckFull3YS1BrimName                     ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3696.00       ; $enabledIn
                        ; AA2_$fiCc1987P12400 ; AA2_$fiCc1987_TRKTRUCK    ; 1987P12400     ; $packTruckSubsS1BrimName                       ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1400.00       ; $enabledIn
                        ; AA2_$fiCc1987P12936 ; AA2_$fiCc1987_TRKTRUCK    ; 1987P12936     ; $packTruckSubsM3BrimName                       ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1530.00       ; $enabledIn
                        ; AA2_$fiCc1987P12937 ; AA2_$fiCc1987_TRKTRUCK    ; 1987P12937     ; $packTruckFull3YM3BrimName                     ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 4039.00       ; $enabledIn
                        ; AA2_$fiCc1987P12503 ; AA2_$fiCc1987_TSTINFOAW   ; 1987P12503     ; $infoartWFull3YBrimName                        ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 1848.00       ; $enabledIn
                        ; AA2_$fiCc1987P12500 ; AA2_$fiCc1987_TSTINFOAW   ; 1987P12500     ; $infoartWSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; 700.00        ; $enabledIn

                        ; AA2_$fiCc1987P13523 ; AA2_$fiCc1987_THLPKW      ; 1987P13523     ; $thlPkwFull3YBrimName                          ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 1689.60       ; $enabledIn
                        ; AA2_$fiCc1987729274 ; AA2_$fiCc1987_THLPKW      ; 1987729274     ; $thlPkwSubsBrimName                            ;                                          ; runtime_subs_unlimited ; <ignore>        ; 640.00        ; $enabledIn

                        ; AA2_$fiCc1687P15015 ; AA2_$fiCc1687_TSTINFODAT  ; 1687P15015     ; $infoartTestdataSubsBrimName                   ;                                          ; runtime_subs_unlimited ; <ignore>        ; 340.00        ; $enabledIn
                        ; AA2_$fiCc1987P12404 ; AA2_$fiCc1987_TRKUPG      ; 1987P12404     ; $truckUpgradeSubsS1BrimName                    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1200.00       ; $enabledIn
                        ; AA2_$fiCc1987P12140 ; AA2_$fiCc1987_TRKUPG      ; 1987P12140     ; $truckUpgradeFull3YS1BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 3168.00       ; $enabledIn
                        ; AA2_$fiCc1987P12359 ; AA2_$fiCc1987_TRKUPG      ; 1987P12359     ; $truckUpgradeSubsM3BrimName                    ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1330.00       ; $enabledIn
                        ; AA2_$fiCc1987P12364 ; AA2_$fiCc1987_TRKUPG      ; 1987P12364     ; $truckUpgradeFull3YM3BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 3511.00       ; $enabledIn
                        ; AA2_$fiCc1687P15130 ; AA2_$fiCc1687_CORE_ESIPKG ; 1687P15130     ; $coreS1BrimName                                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1.20          ; $enabledIn
                        ; AA2_$fiCc1987P12389 ; AA2_$fiCc1987_KTS250SD    ; 1987P12389     ; $kts250SDFullBrimName                          ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 800.00        ; $enabledIn
                        ; AA2_$fiCc1987P12385 ; AA2_$fiCc1987_KTS250SD    ; 1987P12385     ; $kts250SDSubsBrimName                          ;                                          ; runtime_subs_unlimited ; <ignore>        ; 715.00        ; $enabledIn
                        ; AA2_$fiCc1687P15137 ; AA2_$fiCc1687_CRR950      ; 1687P15137     ; $crr950DieselInjectorsRepairSoftwareBrimName1L ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 100           ; $enabledIn
                        ; AA2_$fiCc1687P15139 ; AA2_$fiCc1687_CRR950      ; 1687P15139     ; $crr950DieselInjectorsRepairSoftwareBrimName3L ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 200           ; $enabledIn
                        ; AA2_$fiCc1987P12254 ; AA2_$fiCc1987_TRKOHW3     ; 1987P12254     ; $ohw3S1BrimName                                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 380           ;
                        ; AA2_$fiCc1987P12257 ; AA2_$fiCc1987_TRKOHW3     ; 1987P12257     ; $ohw3M3BrimName                                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 484           ;
                        ; AA2_$fiCc1987P12256 ; AA2_$fiCc1987_TRKOHW3     ; 1987P12256     ; $ohw3Full3YM3BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1597          ;
                        ; AA2_$fiCc1987P12515 ; AA2_$fiCc1987_ADAS_ONE    ; 1987P12515     ; $adasSubsS1BrimName                            ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 0.01          ; $enabledIn
                        ; AA2_$fiCc1987P12517 ; AA2_$fiCc1987_ADAS_ONE    ; 1987P12517     ; $adasFull3YS1BrimName                          ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 0.01          ; $enabledIn
                        ; AA2_$fiCc1687P15170 ; AA2_$fiCc1687_CSFSA500    ; 1687P15170     ; $fsa5xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$fiCc1687P15173 ; AA2_$fiCc1687_CSFSA500    ; 1687P15173     ; $fsa5xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;
                        ; AA2_$fiCc1687P15160 ; AA2_$fiCc1687_CSFSA7XX    ; 1687P15160     ; $fsa7xxMulti                                   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$fiCc1687P15163 ; AA2_$fiCc1687_CSFSA7XX    ; 1687P15163     ; $fsa7xx3YearsMulti                             ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;

# The licenses below were discontinued by MA and should be available only for the contract migration.
UPDATE AppLicense; code[unique = true] ; $catalogVersion[unique = true]; availabilityStatus(code)[default = UNPUBLISHED]
                 ; AA2_$fiCc1987P12389 ;

UPDATE AppLicense; code[unique = true] ; userGroups(uid); $catalogVersion[unique = true]
                 ; AA2_$fiCc1687P15060 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1687P15063 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1687P15048 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1687P15045 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1687P15093 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1687P15090 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1687P15102 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1687P15103 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1687P15100 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1687P15107 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12843 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12840 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12846 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12847 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12988 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12998 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12783 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12784 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12973 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12970 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12296 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12297 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12993 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12990 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12294 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12295 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12823 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12820 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12822 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12824 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12913 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12910 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12916 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12917 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12263 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12260 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12262 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12265 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12280 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12278 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12275 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12276 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12402 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12400 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12936 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12937 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12503 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12500 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P13523 ;                ;
                 ; AA2_$fiCc1987729274 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1687P15015 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12404 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12140 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12359 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12364 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1687P15130 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12389 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12385 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1687P15137 ; IDW000         ;
                 ; AA2_$fiCc1687P15139 ; IDW000         ;
                 ; AA2_$fiCc1987P12254 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12257 ; IDW000,WD0001  ;
                 ; AA2_$fiCc1987P12256 ; IDW000,WD0001  ;
# The group assignment for the ADAS ONE is most probably not final. we're missing the prices for now
                 ; AA2_$fiCc1987P12515 ; IDW000         ;
                 ; AA2_$fiCc1987P12517 ; IDW000         ;
                 ; AA2_$fiCc1687P15163 ; IDW000         ;
                 ; AA2_$fiCc1687P15170 ; IDW000         ;
                 ; AA2_$fiCc1687P15160 ; IDW000         ;
                 ; AA2_$fiCc1687P15173 ; IDW000         ;


INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$fiCc1687P15060                          ; 110.00
                      ; AA2_$fiCc1687P15063                          ; 290.00
                      ; AA2_$fiCc1687P15048                          ; 897.60
                      ; AA2_$fiCc1687P15045                          ; 340.00
                      ; AA2_$fiCc1687P15093                          ; 911.00
                      ; AA2_$fiCc1687P15090                          ; 345.00
                      ; AA2_$fiCc1687P15102                          ; 415.00
                      ; AA2_$fiCc1687P15103                          ; 911.00
                      ; AA2_$fiCc1687P15100                          ; 345.00
                      ; AA2_$fiCc1687P15107                          ; 415.00
                      ; AA2_$fiCc1987P12843                          ; 3920.00
                      ; AA2_$fiCc1987P12840                          ; 1485.00
                      ; AA2_$fiCc1987P12846                          ; 4264.00
                      ; AA2_$fiCc1987P12847                          ; 1615.00
                      ; AA2_$fiCc1987P12988                          ; 845.00
                      ; AA2_$fiCc1987P12998                          ; 320.00
                      ; AA2_$fiCc1987P12783                          ; 1030.00
                      ; AA2_$fiCc1987P12784                          ; 390.00
                      ; AA2_$fiCc1987P12973                          ; 1769.00
                      ; AA2_$fiCc1987P12970                          ; 670.00
                      ; AA2_$fiCc1987P12296                          ; 1954.90
                      ; AA2_$fiCc1987P12297                          ; 740.00
                      ; AA2_$fiCc1987P12993                          ; 766.00
                      ; AA2_$fiCc1987P12990                          ; 290.00
                      ; AA2_$fiCc1987P12294                          ; 950.00
                      ; AA2_$fiCc1987P12295                          ; 360.00
                      ; AA2_$fiCc1987P12823                          ; 2125.00
                      ; AA2_$fiCc1987P12820                          ; 805.00
                      ; AA2_$fiCc1987P12822                          ; 2486.00
                      ; AA2_$fiCc1987P12824                          ; 935.00
                      ; AA2_$fiCc1987P12913                          ; 5518.00
                      ; AA2_$fiCc1987P12910                          ; 2090.00
                      ; AA2_$fiCc1987P12916                          ; 5861.00
                      ; AA2_$fiCc1987P12917                          ; 2220.00
                      ; AA2_$fiCc1987P12263                          ; 1769.00
                      ; AA2_$fiCc1987P12260                          ; 670.00
                      ; AA2_$fiCc1987P12262                          ; 800.00
                      ; AA2_$fiCc1987P12265                          ; 2112.00
                      ; AA2_$fiCc1987P12280                          ; 3155.00
                      ; AA2_$fiCc1987P12278                          ; 1195.00
                      ; AA2_$fiCc1987P12275                          ; 1325.00
                      ; AA2_$fiCc1987P12276                          ; 3498.00
                      ; AA2_$fiCc1987P12402                          ; 3696.00
                      ; AA2_$fiCc1987P12400                          ; 1400.00
                      ; AA2_$fiCc1987P12936                          ; 1530.00
                      ; AA2_$fiCc1987P12937                          ; 4039.00
                      ; AA2_$fiCc1987P12503                          ; 1848.00
                      ; AA2_$fiCc1987P12500                          ; 700.00
                      ; AA2_$fiCc1987P13523                          ; 1689.60
                      ; AA2_$fiCc1987729274                          ; 640.00
                      ; AA2_$fiCc1687P15015                          ; 340.00
                      ; AA2_$fiCc1987P12404                          ; 1200.00
                      ; AA2_$fiCc1987P12140                          ; 3168.00
                      ; AA2_$fiCc1987P12359                          ; 1330.00
                      ; AA2_$fiCc1987P12364                          ; 3511.00
                      ; AA2_$fiCc1687P15130                          ; 1.20
                      ; AA2_$fiCc1987P12389                          ; 800.00
                      ; AA2_$fiCc1987P12385                          ; 715.00
                      ; AA2_$fiCc1687P15137                          ; 100  ;
                      ; AA2_$fiCc1687P15139                          ; 200  ;
                      ; AA2_$fiCc1987P12254                          ; 380
                      ; AA2_$fiCc1987P12257                          ; 484
                      ; AA2_$fiCc1987P12256                          ; 1597
                      ; AA2_$fiCc1987P12515                          ; 0.01 ;
                      ; AA2_$fiCc1987P12517                          ; 0.01 ;
                      ; AA2_$fiCc1687P15163                          ; 0.01 ;
                      ; AA2_$fiCc1687P15170                          ; 0.01 ;
                      ; AA2_$fiCc1687P15160                          ; 0.01 ;
                      ; AA2_$fiCc1687P15173                          ; 0.01 ;


INSERT_UPDATE App; code[unique = true]     ; boms(code); $catalogVersion[unique = true]
                 ; AA2_$fiCc1987_ESIADV    ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe
                 ; AA2_$fiCc1987_ESIREPCAT ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE
                 ; AA2_$fiCc1987_ESIREPD   ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE,MAT_$aaCcK3
                 ; AA2_$fiCc1987_ESIREPE   ; MAT_$aaCcA,MAT_$aaCcE,MAT_$aaCcK2
                 ; AA2_$fiCc1987_ESIDIAG   ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB
                 ; AA2_$fiCc1987_ESIMASTER ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe,MAT_$aaCcM,MAT_$aaCcP
                 ; AA2_$fiCc1987_TRKOHW1   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$fiCc1987_TRKOHW2   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$fiCc1987_TRKTRUCK  ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$fiCc1987_TRKUPG    ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$fiCc1987_TRKOHW3   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK


INSERT_UPDATE App; code[unique = true]       ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$fiCc1687_CSFSA500    ; CM_$aaCcCSFSA5
                 ; AA2_$fiCc1687_CSFSA7XX    ; CM_$aaCcCSS,CM_$aaCcCSK
                 ; AA2_$fiCc1687_DCICRI      ; CM_$aaCcDCICRI
                 ; AA2_$fiCc1687_DCICRIN     ; CM_$aaCcDCICRIN
                 ; AA2_$fiCc1987_ESIADV      ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe
                 ; AA2_$fiCc1987_ESIREPCAT   ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE
                 ; AA2_$fiCc1987_ESIREPD     ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE,CM_$aaCcK3
                 ; AA2_$fiCc1987_ESIREPE     ; CM_$aaCcA,CM_$aaCcE,CM_$aaCcK2
                 ; AA2_$fiCc1987_ESIDIAG     ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB
                 ; AA2_$fiCc1987_ESIMASTER   ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe,CM_$aaCcM,CM_$aaCcP
                 ; AA2_$fiCc1987_TRKOHW1     ; CM_$aaCcETOHW1
                 ; AA2_$fiCc1987_TRKOHW2     ; CM_$aaCcETOHW2
                 ; AA2_$fiCc1987_TRKTRUCK    ; CM_$aaCcETruck
                 ; AA2_$fiCc1987_TSTINFOAW   ; CM_$aaCcEW
                 ; AA2_$fiCc1687_TSTINFODAT  ; CM_$aaCcTVPMCP
                 ; AA2_$fiCc1987_TRKUPG      ; CM_$aaCcTRKUPG
                 ; AA2_$fiCc1987_THLPKW      ; CM_$aaCcPKWTHL
                 ; AA2_$fiCc1687_CORE_ESIPKG ; CM_$aaCcCoReESIPKG
                 ; AA2_$fiCc1987_KTS250SD    ; CM_$aaCcKTS250ECUD
                 ; AA2_$fiCc1987_TRKOHW3     ; CM_$aaCcETOHW3

INSERT_UPDATE App; code[unique = true]  ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$fiCc1687_CRR950 ; CM_$aaCcCRR950

INSERT_UPDATE App; code[unique = true]       ; $supercategories; $catalogVersion[unique = true];
                 ; AA2_$fiCc1687_CSFSA500    ; cat_201
                 ; AA2_$fiCc1687_CSFSA7XX    ; cat_201
                 ; AA2_$fiCc1687_DCICRI      ; cat_401
                 ; AA2_$fiCc1687_DCICRIN     ; cat_401
                 ; AA2_$fiCc1987_ESIADV      ; cat_10101
                 ; AA2_$fiCc1987_ESIREPCAT   ; cat_1010101
                 ; AA2_$fiCc1987_ESIREPD     ; cat_1010101
                 ; AA2_$fiCc1987_ESIREPE     ; cat_1010101
                 ; AA2_$fiCc1987_ESIDIAG     ; cat_10101
                 ; AA2_$fiCc1987_ESIMASTER   ; cat_10101
                 ; AA2_$fiCc1987_TRKOHW1     ; cat_10102
                 ; AA2_$fiCc1987_TRKOHW2     ; cat_10102
                 ; AA2_$fiCc1987_TRKTRUCK    ; cat_10102
                 ; AA2_$fiCc1987_TSTINFOAW   ; cat_40101
                 ; AA2_$fiCc1687_TSTINFODAT  ; cat_40101
                 ; AA2_$fiCc1987_TRKUPG      ; cat_10102
                 ; AA2_$fiCc1987_THLPKW      ; cat_1010102
                 ; AA2_$fiCc1687_CORE_ESIPKG ; cat_1010103
                 ; AA2_$fiCc1987_KTS250SD    ; cat_1010104
                 ; AA2_$fiCc1687_CRR950      ; cat_401
                 ; AA2_$fiCc1987_TRKOHW3     ; cat_10102
                 ; AA2_$fiCc1987_ADAS_ONE    ; cat_501         ;


INSERT_UPDATE App; code[unique = true]       ; eulaContainers(code); $catalogVersion[unique = true]
                 ; AA2_$fiCc1687_DCICRI      ; AA2_DCICRI
                 ; AA2_$fiCc1687_DCICRIN     ; AA2_DCICRIN
                 ; AA2_$fiCc1687_TSTINFODAT  ; AA2_ESItronic
                 ; AA2_$fiCc1687_CSFSA500    ; AA2_FSA
                 ; AA2_$fiCc1687_CSFSA7XX    ; AA2_FSA
                 ; AA2_$fiCc1687_CORE_ESIPKG ; AA2_CoRe
                 ; AA2_$fiCc1987_KTS250SD    ; AA2_ESItronic
                 ; AA2_$fiCc1687_CRR950      ; AA2_ESItronic
