package com.sast.cis.macmt.service.validator;

import com.sast.cis.aa.core.model.MigrationContractModel;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.BundleInfoModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.AppLicenseService;
import com.sast.cis.core.service.ProductCatalogIdentifierService;
import com.sast.cis.macmt.service.dto.MigrationContractErrorDto;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.catalog.model.CatalogModel;
import de.hybris.platform.core.model.c2l.CountryModel;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

import static com.sast.cis.macmt.exception.ValidationErrorCode.BUNDLE_SIZE_EXCEEDED;
import static com.sast.cis.macmt.exception.ValidationErrorCode.CONTRACTS_WITHIN_BUNDLE_HAVE_DIFFERENT_DISTRIBUTORS;
import static com.sast.cis.macmt.exception.ValidationErrorCode.NOT_A_BUNDLE_PRODUCT;
import static com.sast.cis.macmt.exception.ValidationErrorCode.SHARED_BUNDLE_IDENTIFIER;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class MigrationContractBundleValidatorUnitTest {

    private static final String CATALOG_ID = "CATALOG_ID";
    private static final String MATERIAL_1 = "MATERIAL_1";
    private static final String UMP_CONTRACT_ID_1 = "UMP_CONTRACT_ID_1";
    private static final String UNIQUE_BUNDLE_IDENTIFIER = "UNIQUE_BUNDLE_IDENTIFIER";
    private static final String CMT_ID_1 = "CMT_ID_1";
    private static final String CMT_ID_2 = "CMT_ID_2";
    private static final String MATERIAL_2 = "MATERIAL_2";
    private static final String UMP_CONTRACT_ID_2 = "UMP_CONTRACT_ID_2";
    private final String DISTRIBUTOR = "DISTRIBUTOR_1";

    @Mock
    private AppLicenseService appLicenseService;

    @Mock
    private ProductCatalogIdentifierService productCatalogIdentifierService;

    @InjectMocks
    private MigrationContractBundleValidator bundleValidator;

    @Mock
    private IoTCompanyModel iotCompany;

    @Mock
    private AppLicenseModel appLicense;

    @Mock
    private BundleInfoModel bundleInfo;

    @Mock
    private MigrationContractModel migrationContract;

    @Mock
    private MigrationContractModel anotherMigrationContract;

    @Mock
    private CatalogModel catalog;
    @Mock
    private CountryModel country;

    private Set<MigrationContractModel> migrationContracts;

    @Before
    public void setUp() {
        when(country.getIsocode()).thenReturn("AT");
        when(iotCompany.getCountry()).thenReturn(country);
        when(productCatalogIdentifierService.getSessionProductCatalog()).thenReturn(catalog);
        when(catalog.getId()).thenReturn(CATALOG_ID);
        when(appLicenseService.getAppLicenseForSellerProductIdAndCountry(any(), any(), any(), any()))
            .thenReturn(Optional.of(appLicense));
        when(appLicense.getBundleInfo()).thenReturn(bundleInfo);
        when(bundleInfo.getSize()).thenReturn(3);

        mockContract(migrationContract, UMP_CONTRACT_ID_1, CMT_ID_1);
        mockContract(anotherMigrationContract, UMP_CONTRACT_ID_2, CMT_ID_2);

        migrationContracts = new HashSet<>();
        migrationContracts.add(migrationContract);
        migrationContracts.add(anotherMigrationContract);
    }

    private void mockContract(MigrationContractModel migrationContract, String upmContractId, String cmtId) {
        when(migrationContract.getMaterialId()).thenReturn(MATERIAL_1);
        when(migrationContract.getUmpContractId()).thenReturn(upmContractId);
        when(migrationContract.getUniqueBundleIdentifier()).thenReturn(UNIQUE_BUNDLE_IDENTIFIER);
        when(migrationContract.getCmtId()).thenReturn(cmtId);
        when(migrationContract.getDistributor()).thenReturn(DISTRIBUTOR);
    }

    @Test
    public void testValidateBundleSizeWithSharedBundleIdentifier() {
        when(anotherMigrationContract.getMaterialId()).thenReturn(MATERIAL_2);
        Set<MigrationContractErrorDto> result = bundleValidator.validate(iotCompany, migrationContracts);

        assertThat(result).hasSize(2);
        result.forEach(errorDto -> assertThat(errorDto.errors()).contains(SHARED_BUNDLE_IDENTIFIER.name()));
    }

    @Test
    public void testValidateBundleSizeExceedsLimit() {

        when(bundleInfo.getSize()).thenReturn(1);

        Set<MigrationContractErrorDto> result = bundleValidator.validate(iotCompany, migrationContracts);

        assertThat(result).hasSize(2);
        result.forEach(errorDto -> assertThat(errorDto.errors()).contains(BUNDLE_SIZE_EXCEEDED.name()));
    }

    @Test
    public void testValidateBundleSizeValid() {

        when(bundleInfo.getSize()).thenReturn(3);

        Set<MigrationContractErrorDto> result = bundleValidator.validate(iotCompany, migrationContracts);

        assertThat(result).isEmpty();
    }

    @Test
    public void validate_asBundleWithNoBundleInfo_notABundleErrorReported() {
        when(appLicense.getBundleInfo()).thenReturn(null);

        Set<MigrationContractErrorDto> result = bundleValidator.validate(iotCompany, migrationContracts);

        assertThat(result).hasSize(2);
        result.forEach(errorDto -> assertThat(errorDto.errors()).contains(NOT_A_BUNDLE_PRODUCT.name()));
    }

    @Test
    public void validate_bundleProductWithoutIdentifier_errorReported() {
        // Setup a bundle product
        when(bundleInfo.getSize()).thenReturn(2);

        // Contract with no bundle identifier
        when(migrationContract.getUniqueBundleIdentifier()).thenReturn(null);
        when(anotherMigrationContract.getUniqueBundleIdentifier()).thenReturn(null);

        Set<MigrationContractErrorDto> result = bundleValidator.validate(iotCompany, migrationContracts);

        assertThat(result).hasSize(2);
        result.forEach(errorDto -> assertThat(errorDto.errors()).contains("EMPTY_UNIQUE_BUNDLE_IDENTIFIER"));
    }

    @Test
    public void validate_nonBundleProductWithoutIdentifier_noError() {
        // Setup a non-bundle product (size = 1)
        when(bundleInfo.getSize()).thenReturn(1);

        // Contract with no bundle identifier
        when(migrationContract.getUniqueBundleIdentifier()).thenReturn(null);
        when(anotherMigrationContract.getUniqueBundleIdentifier()).thenReturn(null);

        Set<MigrationContractErrorDto> result = bundleValidator.validate(iotCompany, migrationContracts);

        assertThat(result).isEmpty();
    }

    @Test
    public void givenContractsWithinBundleHaveDifferentDistributors_whenValidate_thenReturnError() {
        when(anotherMigrationContract.getDistributor()).thenReturn("OTHER_DISTRIBUTOR");
        Set<MigrationContractErrorDto> result = bundleValidator.validate(iotCompany, migrationContracts);

        assertThat(result).hasSize(2);
        result.forEach(errorDto -> assertThat(errorDto.errors()).contains(CONTRACTS_WITHIN_BUNDLE_HAVE_DIFFERENT_DISTRIBUTORS.name()));
    }

    @Test
    public void givenBundleContainsContractsWithAndWithoutDistributor_whenValidate_thenReturnError() {
        when(anotherMigrationContract.getDistributor()).thenReturn(null);
        Set<MigrationContractErrorDto> result = bundleValidator.validate(iotCompany, migrationContracts);

        assertThat(result).hasSize(2);
        result.forEach(errorDto -> assertThat(errorDto.errors()).contains(CONTRACTS_WITHIN_BUNDLE_HAVE_DIFFERENT_DISTRIBUTORS.name()));
    }
}
