package com.sast.cis.macmt.service

import com.amazonaws.services.sqs.AmazonSQSClient
import com.amazonaws.services.sqs.model.Message
import com.amazonaws.services.sqs.model.PurgeQueueRequest
import com.amazonaws.services.sqs.model.ReceiveMessageRequest
import com.amazonaws.services.sqs.model.ReceiveMessageResult
import com.sast.cis.aa.core.enums.MigrationMode
import com.sast.cis.aa.core.model.ContractMigrationProcessModel
import com.sast.cis.core.constants.Currency
import com.sast.cis.core.model.AppLicenseModel
import com.sast.cis.core.model.AppModel
import com.sast.cis.core.model.IoTCompanyModel
import com.sast.cis.core.service.ObjectMapperService
import com.sast.cis.core.service.company.IotCompanyService
import com.sast.cis.core.service.customer.developer.DeveloperService
import com.sast.cis.macmt.model.ContractMigrationRequestModel
import com.sast.cis.macmt.publisher.MigrationResponsePublisher
import com.sast.cis.macmt.service.dto.ContractMigrationNotificationType
import com.sast.cis.macmt.service.dto.ContractMigrationProcessExecutionResultDto
import com.sast.cis.test.utils.SampleDataCreator
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.catalog.CatalogVersionService
import de.hybris.platform.core.model.c2l.CountryModel
import de.hybris.platform.core.model.user.UserGroupModel
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.config.ConfigurationService
import de.hybris.platform.servicelayer.i18n.CommonI18NService
import de.hybris.platform.servicelayer.model.ModelService
import de.hybris.platform.servicelayer.search.FlexibleSearchService
import de.hybris.platform.servicelayer.user.UserService
import generated.com.sast.cis.core.model.BundleInfoBuilder
import generated.com.sast.cis.macmt.model.ContractMigrationRequestBuilder
import groovy.json.JsonOutput

import javax.annotation.Resource

import static com.sast.cis.aa.core.enums.MigrationMode.EXECUTE
import static com.sast.cis.aa.core.enums.MigrationMode.SIMULATE
import static com.sast.cis.core.constants.DynamicBusinessProcessesDefinitions.AA_ORDER_MIGRATION_PROCESS
import static com.sast.cis.core.constants.DynamicBusinessProcessesDefinitions.AA_ORDER_MIGRATION_SIMULATION_PROCESS
import static com.sast.cis.core.dao.CatalogVersion.ONLINE
import static com.sast.cis.core.enums.CompanyApprovalStatus.APPROVED_COMMERCIAL
import static com.sast.cis.macmt.constants.MacmtintegrationConstants.PROPERTY_CMT_EVENTS_RESPONSE_QUEUE_NAME
import static com.sast.cis.macmt.exception.ValidationErrorCode.*
import static com.sast.cis.macmt.service.dto.ProcessExecutionStep.MIGRATION_COMPLETION
import static com.sast.cis.macmt.service.dto.ProcessExecutionStep.STORE_VALIDATION
import static com.sast.cis.test.utils.TestDataConstants.*
import static de.hybris.platform.catalog.enums.ArticleApprovalStatus.APPROVED
import static org.assertj.core.api.Assertions.assertThat

@IntegrationTest
class ContractMigrationRequestProcessingServiceISpec extends ServicelayerTransactionalSpockSpecification {

    @Resource
    private ContractMigrationRequestProcessingService contractMigrationRequestProcessingService

    @Resource
    private MigrationResponsePublisher migrationResponsePublisher

    @Resource
    private FlexibleSearchService flexibleSearchService

    @Resource
    private AmazonSQSClient amazonSQS

    @Resource
    private ConfigurationService configurationService

    @Resource
    private ObjectMapperService objectMapperService

    @Resource
    private CommonI18NService commonI18NService

    @Resource
    private UserService userService

    @Resource
    private DeveloperService developerService

    @Resource
    private IotCompanyService iotCompanyService

    @Resource
    private CatalogVersionService catalogVersionService

    @Resource
    private ModelService modelService

    private final SampleDataCreator sampleDataCreator = new SampleDataCreator()

    private ContractMigrationRequestModel contractMigrationRequest
    private AppModel esiApp
    private AppLicenseModel esiLicense
    private IoTCompanyModel buyerCompany

    private UserGroupModel wd0Group
    private UserGroupModel idwGroup

    private CountryModel at

    private String queueUrl

    private final String requestCode = "REQ_1"
    private final String nonExistingCustomerId = "nonExistingCustomerId"
    private final String customerId = "********"
    private final String migratedCustomerId = "********"
    private final String managedAccountCustomerId = "********"
    private final Long processExecutionId = 1L
    private final String creationDate = "2024-06-28T08:58:57.000"

    private final Long migrationContractId = 100L
    private final String licenseType = "AZCU"
    private final String materialId = "1987P12821999"
    private final String upmContractId = "*********"
    private final String uniqueBundleIdentifier = "********-01"
    private final String startDate = "2024-06-28"
    private final String endDate = "2025-06-28"
    private final String wholesaler = "********"

    def setup() {
        queueUrl = amazonSQS.getQueueUrl(configurationService.getConfiguration().getString(PROPERTY_CMT_EVENTS_RESPONSE_QUEUE_NAME)).getQueueUrl()

        def catalogVersion = catalogVersionService.getCatalogVersion(AA_PRODUCT_CATALOG, ONLINE.getVersionName())
        catalogVersionService.setSessionCatalogVersions(Collections.singletonList(catalogVersion))

        def developer = developerService.getDeveloperByInternalUserId(AA_AUSTRIA1_COMPANY_DEVELOPER_UID)
        wd0Group = userService.getUserGroupForUID("WD0001")
        idwGroup = userService.getUserGroupForUID("IDW000")

        at = commonI18NService.getCountry("AT")

        esiApp = sampleDataCreator.createApp("AA_ESI", "aa.macmt.ContractMigrationRequestProcessingServiceISpec",
                developer, AA_PRODUCT_CATALOG, ONLINE, APPROVED)
        esiLicense = sampleDataCreator.createSubscriptionAppLicense(esiApp)
        esiLicense.setSellerProductId(materialId.substring(0, 10))
        esiLicense.setUserGroups(Set.of(wd0Group, idwGroup))
        esiLicense.setEnabledCountries(Set.of(at))

        esiLicense.setBundleInfo(BundleInfoBuilder.generate().withCode("bundleCodeM").withName("M").withSize(3).buildIntegrationInstance())

        def currency = sampleDataCreator.getCurrency(Currency.EUR)
        def priceRow = sampleDataCreator.addPriceRow(esiLicense, 100d, currency, 1L)
        priceRow.setUg(idwGroup.getUserPriceGroup())
        modelService.save(priceRow)

        modelService.saveAll(esiApp, esiLicense)

        buyerCompany = iotCompanyService.getCompanyByUidOrThrow(AA_BUYER_COMPANY_UID)
        buyerCompany.setAaExternalCustomerId(customerId)
        buyerCompany.setBuyer(true)
        buyerCompany.setAaImported(true)
        buyerCompany.setAaCustomerGroup(idwGroup)
        buyerCompany.setApprovalStatus(APPROVED_COMMERCIAL)
        modelService.save(buyerCompany)

        def payload = getPayloadFor(customerId, materialId)
        contractMigrationRequest = ContractMigrationRequestBuilder.generate()
                .withCode(requestCode)
                .withPayload(payload)
                .buildInstance()
        purgeCommunicatorQueue()
    }

    void cleanup() {
        purgeCommunicatorQueue()
    }

    def 'given contract migration request for non existing customer when create process then validation fails and message published'() {
        given:
        contractMigrationRequest = ContractMigrationRequestBuilder.generate()
                .withCode(requestCode)
                .withPayload(getPayloadFor(nonExistingCustomerId, materialId))
                .buildInstance()
        when:
        contractMigrationRequestProcessingService.process(contractMigrationRequest)

        then:
        def actualResult = findProcess(requestCode)
        actualResult
        actualResult.code
        actualResult.ownerCompanyId == nonExistingCustomerId
        actualResult.countryIsocode == at.isocode
        actualResult.sourceContractMigrationRequestCode == requestCode
        actualResult.cmtProcessExecutionId == String.valueOf(processExecutionId)

        actualResult.migrationContracts.size() == 1
        def migrationContract = actualResult.migrationContracts.first()
        migrationContract.code
        migrationContract.cmtId == String.valueOf(migrationContractId)
        migrationContract.materialId == materialId
        migrationContract.distributor == wholesaler
        migrationContract.licenseType == licenseType
        migrationContract.umpContractId == upmContractId
        migrationContract.uniqueBundleIdentifier == uniqueBundleIdentifier

        ContractMigrationProcessExecutionResultDto messageBody = readContractValidationErrorResponse(this.queueUrl)
        messageBody.contractMigrationProcessId() == String.valueOf(processExecutionId)
        messageBody.executionStep() == STORE_VALIDATION
        messageBody.contractValidationErrors().isEmpty()
        messageBody.companyValidationErrors() == [COMPANY_NOT_FOUND.name()] as Set
        messageBody.notificationType() == ContractMigrationNotificationType.ERROR
    }

    def 'given contract migration request when order draft validation fails then validation error is published'() {
        given:
        contractMigrationRequest = ContractMigrationRequestBuilder.generate()
                .withCode(requestCode)
                .withPayload(getPayloadFor(migratedCustomerId, materialId))
                .buildInstance()

        def bdsGroup = userService.getUserGroupForUID("BDS000")

        def migratedBuyerCompany = iotCompanyService.getCompanyByUidOrThrow(AA_MIGRATED_BUYER_COMPANY_UID)
        migratedBuyerCompany.setAaExternalCustomerId(migratedCustomerId)
        migratedBuyerCompany.setBuyer(true)
        migratedBuyerCompany.setAaImported(true)
        migratedBuyerCompany.setApprovalStatus(APPROVED_COMMERCIAL)
        migratedBuyerCompany.setAaCustomerGroup(bdsGroup)

        modelService.save(migratedBuyerCompany)
        when:
        contractMigrationRequestProcessingService.process(contractMigrationRequest)

        then:
        def actualResult = findProcess(requestCode)
        actualResult
        actualResult.code
        actualResult.ownerCompanyId == migratedCustomerId
        actualResult.countryIsocode == at.isocode
        actualResult.sourceContractMigrationRequestCode == requestCode
        actualResult.cmtProcessExecutionId == String.valueOf(processExecutionId)

        actualResult.migrationContracts.size() == 1
        def migrationContract = actualResult.migrationContracts.first()
        migrationContract.code
        migrationContract.cmtId == String.valueOf(migrationContractId)
        migrationContract.materialId == materialId
        migrationContract.distributor == wholesaler
        migrationContract.licenseType == licenseType
        migrationContract.umpContractId == upmContractId
        migrationContract.uniqueBundleIdentifier == uniqueBundleIdentifier

        ContractMigrationProcessExecutionResultDto messageBody = readContractValidationErrorResponse(this.queueUrl)
        messageBody.contractMigrationProcessId() == String.valueOf(processExecutionId)
        messageBody.executionStep() == STORE_VALIDATION
        messageBody.contractValidationErrors()[0].errors() as Set == [CUSTOMER_GROUP_PRICE_NOT_FOUND.name(), PRODUCT_NOT_AVAILABLE_FOR_BUYER_USER_GROUP.name()] as Set
        messageBody.notificationType() == ContractMigrationNotificationType.ERROR
    }

    def 'given contract migration request when create process then succeed'() {
        when:
        contractMigrationRequestProcessingService.process(contractMigrationRequest)

        then:
        def process = findProcess(requestCode)
        process
        process.code
        process.ownerCompanyId == buyerCompany.getAaExternalCustomerId()
        process.countryIsocode == buyerCompany.getCountry().getIsocode()
        process.sourceContractMigrationRequestCode == requestCode
        process.cmtProcessExecutionId == String.valueOf(processExecutionId)

        process.migrationOrderDrafts.size() == 1
        def migrationOrderDraft = process.migrationOrderDrafts.first()
        migrationOrderDraft.code
        migrationOrderDraft.aaDistributor
        migrationOrderDraft.aaDistributor.aaExternalId == wholesaler
        migrationOrderDraft.company == buyerCompany

        migrationOrderDraft.entries.size() == 1
        def migrationOrderEntryDraft = migrationOrderDraft.entries.first()
        migrationOrderEntryDraft.code
        migrationOrderEntryDraft.appLicense == esiLicense
        migrationOrderEntryDraft.quantity == 1

        migrationOrderEntryDraft.contractGroups.size() == 1
        def migrationContractGroup = migrationOrderEntryDraft.contractGroups.first()
        migrationContractGroup.code
        migrationContractGroup.uniqueBundleIdentifier == uniqueBundleIdentifier

        migrationContractGroup.migrationContracts.size() == 1
        def migrationContract = migrationContractGroup.migrationContracts.first()
        migrationContract.code
        migrationContract.cmtId == String.valueOf(migrationContractId)
        migrationContract.materialId == materialId
        migrationContract.distributor == wholesaler
        migrationContract.licenseType == licenseType
        migrationContract.umpContractId == upmContractId
        migrationContract.uniqueBundleIdentifier == uniqueBundleIdentifier

        process.migrationContracts.size() == 1
        process.migrationContracts.contains(migrationContract)

        ReceiveMessageResult receiveMessageResult = receiveMessage(this.queueUrl)
        assertThat(receiveMessageResult.getMessages()).hasSize(0)

        migrationOrderDraft.businessProcesses.size() == 1
        def businessProcess = migrationOrderDraft.businessProcesses.first()
        businessProcess.processDefinitionName == AA_ORDER_MIGRATION_PROCESS.getValue()
        businessProcess.migrationOrderDraft == migrationOrderDraft
    }

    def 'given contract migration request with EXECUTE mode when create process then create migration bp'() {
        given:
        def payload = getPayloadFor(customerId, materialId, EXECUTE)
        contractMigrationRequest = ContractMigrationRequestBuilder.generate()
                .withCode(requestCode)
                .withPayload(payload)
                .buildInstance()

        when:
        contractMigrationRequestProcessingService.process(contractMigrationRequest)

        then:
        def process = findProcess(requestCode)
        process
        process.code
        process.sourceContractMigrationRequestCode == requestCode
        process.cmtProcessExecutionId == String.valueOf(processExecutionId)
        process.migrationMode == EXECUTE

        def migrationOrderDraft = process.migrationOrderDrafts.first()
        migrationOrderDraft.code

        migrationOrderDraft.businessProcesses.size() == 1
        def businessProcess = migrationOrderDraft.businessProcesses.first()
        businessProcess.processDefinitionName == AA_ORDER_MIGRATION_PROCESS.getValue()
        businessProcess.migrationOrderDraft == migrationOrderDraft
    }

    def 'given contract migration request with SIMULATE mode when create process then create migration simulation bp'() {
        given:
        def payload = getPayloadFor(customerId, materialId, SIMULATE)
        contractMigrationRequest = ContractMigrationRequestBuilder.generate()
                .withCode(requestCode)
                .withPayload(payload)
                .buildInstance()

        when:
        contractMigrationRequestProcessingService.process(contractMigrationRequest)

        then:
        def process = findProcess(requestCode)
        process
        process.code
        process.sourceContractMigrationRequestCode == requestCode
        process.cmtProcessExecutionId == String.valueOf(processExecutionId)
        process.migrationMode == SIMULATE

        def migrationOrderDraft = process.migrationOrderDrafts.first()
        migrationOrderDraft.code

        migrationOrderDraft.businessProcesses.size() == 1
        def businessProcess = migrationOrderDraft.businessProcesses.first()
        businessProcess.processDefinitionName == AA_ORDER_MIGRATION_SIMULATION_PROCESS.getValue()
        businessProcess.migrationOrderDraft == migrationOrderDraft
    }

    def 'given contract migration request when migration process succeeds then success message is published'() {
        given:
        def successResponse = ContractMigrationProcessExecutionResultDto.builder()
                .contractMigrationProcessId(String.valueOf(processExecutionId))
                .migrationOrderId(requestCode)
                .contractCmtIds(Set.of("contractCmtId1", "contractCmtId2"))
                .notificationType(ContractMigrationNotificationType.SUCCESS)
                .executionStep(MIGRATION_COMPLETION)
                .build()

        when:
        migrationResponsePublisher.publish(successResponse)

        then:
        ReceiveMessageResult receiveMessageResult = receiveMessage(this.queueUrl)
        assertThat(receiveMessageResult.getMessages()).hasSize(1)

        def message = receiveMessageResult.getMessages().get(0)
        ContractMigrationProcessExecutionResultDto messageBody = objectMapperService.toObject(
                message.getBody(),
                ContractMigrationProcessExecutionResultDto.class
        )

        messageBody.contractMigrationProcessId() == String.valueOf(processExecutionId)
        messageBody.contractCmtIds() == ["contractCmtId1", "contractCmtId2"] as Set
        messageBody.executionStep() == MIGRATION_COMPLETION
        messageBody.notificationType() == ContractMigrationNotificationType.SUCCESS
        messageBody.migrationOrderId() == requestCode


    }

    def 'given indirect sales contract migration request with managedAccount different to reseller  when create process then validation succeeds'() {
        given:
        def payload = getPayloadFor(customerId, materialId, EXECUTE, "managedAccountCustomerId", managedAccountCustomerId)
        contractMigrationRequest = ContractMigrationRequestBuilder.generate()
                .withCode(requestCode)
                .withPayload(payload)
                .buildInstance()

        def indirectBuyerCompany = prepareIndirectSalesBuyer(AA_MIGRATED_BUYER_COMPANY_UID, customerId)
        def managedAccountCompany = prepareIndirectSalesManagedAccount()
        modelService.saveAll(indirectBuyerCompany, managedAccountCompany)

        when:
        contractMigrationRequestProcessingService.process(contractMigrationRequest)

        then:
        assertIndirectSalesValidContractMigrationProcess(requestCode, indirectBuyerCompany, managedAccountCompany, materialId)
    }

    def 'given indirect sales contract migration request with reseller buying for himself  when create process then validation succeeds'() {
        given:
        def payload = getPayloadFor(managedAccountCustomerId, materialId, EXECUTE, "managedAccountCustomerId", managedAccountCustomerId)
        contractMigrationRequest = ContractMigrationRequestBuilder.generate()
                .withCode(requestCode)
                .withPayload(payload)
                .buildInstance()

        def indirectBuyerCompany = prepareIndirectSalesBuyer(AA_MIGRATED_BUYER_COMPANY_UID, managedAccountCustomerId)
        modelService.save(indirectBuyerCompany)

        when:
        contractMigrationRequestProcessingService.process(contractMigrationRequest)

        then:
        assertIndirectSalesValidContractMigrationProcess(requestCode, indirectBuyerCompany, indirectBuyerCompany, materialId)
    }

    private def prepareIndirectSalesBuyer(final String companyUid, final String aaExternalCustomerId) {
        def indirectBuyerCompany = iotCompanyService.getCompanyByUidOrThrow(companyUid)
        indirectBuyerCompany.setAaExternalCustomerId(aaExternalCustomerId)
        indirectBuyerCompany.setBuyer(true)
        indirectBuyerCompany.setAaImported(true)
        indirectBuyerCompany.setApprovalStatus(APPROVED_COMMERCIAL)
        indirectBuyerCompany
    }

    private def prepareIndirectSalesManagedAccount() {
        def managedAccountCompany = prepareIndirectSalesBuyer(AA_BUYER_COMPANY_UID, managedAccountCustomerId)
        managedAccountCompany.setIsManaged(true)
        managedAccountCompany
    }

    private void assertIndirectSalesValidContractMigrationProcess(String requestCode,
                                                                  def expectedBuyerCompany,
                                                                  def expectedManagedAccountCompany,
                                                                  String expectedMaterialId) {
        def process = findProcess(requestCode)

        process
        process.code
        process.ownerCompanyId == expectedBuyerCompany.getAaExternalCustomerId()
        process.countryIsocode == expectedBuyerCompany.getCountry().getIsocode()
        process.sourceContractMigrationRequestCode == requestCode
        process.cmtProcessExecutionId == String.valueOf(processExecutionId)
        process.migrationOrderDrafts.size() == 1

        def migrationOrderDraft = process.migrationOrderDrafts.first()
        migrationOrderDraft.code
        migrationOrderDraft.company == expectedBuyerCompany
        migrationOrderDraft.managedAccount == expectedManagedAccountCompany
        migrationOrderDraft.entries.size() == 1

        def migrationOrderEntryDraft = migrationOrderDraft.entries.first()
        migrationOrderEntryDraft.code
        migrationOrderEntryDraft.appLicense == esiLicense
        migrationOrderEntryDraft.quantity == 1
        migrationOrderEntryDraft.contractGroups.size() == 1

        def migrationContractGroup = migrationOrderEntryDraft.contractGroups.first()
        migrationContractGroup.code
        migrationContractGroup.uniqueBundleIdentifier == uniqueBundleIdentifier
        migrationContractGroup.migrationContracts.size() == 1

        def migrationContract = migrationContractGroup.migrationContracts.first()
        migrationContract.code
        migrationContract.cmtId == String.valueOf(migrationContractId)
        migrationContract.materialId == expectedMaterialId
        migrationContract.managedAccount == expectedManagedAccountCompany.getAaExternalCustomerId()
        !migrationContract.distributor
        migrationContract.licenseType == licenseType
        migrationContract.umpContractId == upmContractId
        migrationContract.uniqueBundleIdentifier == uniqueBundleIdentifier
    }

    private ContractMigrationProcessModel findProcess(final String requestCode) {
        def process = new ContractMigrationProcessModel()
        process.setSourceContractMigrationRequestCode(requestCode)
        flexibleSearchService.getModelByExample(process)
    }

    private ContractMigrationProcessExecutionResultDto readContractValidationErrorResponse(final String responseQueueUrl) {
        ReceiveMessageResult receiveMessageResult = receiveMessage(responseQueueUrl)

        assertThat(receiveMessageResult.getMessages()).hasSize(1)
        final Message message = receiveMessageResult.getMessages().get(0)
        objectMapperService.toObject(
                message.getBody(),
                ContractMigrationProcessExecutionResultDto.class
        )
    }

    private ReceiveMessageResult receiveMessage(String responseQueueUrl) {
        final ReceiveMessageRequest receiveMessageRequest = new ReceiveMessageRequest()
                .withQueueUrl(responseQueueUrl)
                .withWaitTimeSeconds(10)
        final ReceiveMessageResult receiveMessageResult = amazonSQS.receiveMessage(receiveMessageRequest)
        receiveMessageResult
    }

    private String getPayloadFor(String customerId, String materialId) {
        getPayloadFor(customerId, materialId, EXECUTE)
    }

    private String getPayloadFor(String customerId, String materialId, MigrationMode migrationMode, String salesAccountField, Object salesAccountValue) {
        def migrationContract = [
                id                    : migrationContractId,
                esiCustomerId         : customerId,
                upmContractId         : upmContractId,
                uniqueBundleIdentifier: uniqueBundleIdentifier,
                licenseType           : licenseType,
                materialId            : materialId,
                startDate             : startDate,
                endDate               : endDate
        ]

        migrationContract[salesAccountField] = salesAccountValue

        JsonOutput.toJson([
                customerId        : customerId,
                countryIsocode    : at.isocode,
                processExecutionId: processExecutionId,
                creationDate      : creationDate,
                migrationMode     : migrationMode,
                migrationContracts: [migrationContract]
        ])
    }

    private String getPayloadFor(String customerId, String materialId, MigrationMode migrationMode) {
        getPayloadFor(customerId, materialId, migrationMode, "wholesaler", wholesaler)
    }

    private void purgeCommunicatorQueue() {
        amazonSQS.purgeQueue(new PurgeQueueRequest(this.queueUrl))
    }
}
