package com.sast.cis.macmt.exception;

import lombok.Getter;

@Getter
public enum ValidationErrorCode {

    /**
     * Indicates that the specified company was not found.
     */
    COMPANY_NOT_FOUND,

    /**
     * Indicates that multiple companies were found for the specified external ID.
     */
    MULTIPLE_COMPANIES_FOUND_FOR_EXTERNAL_ID,

    /**
     * Indicates that the company is not operational and cannot be used for migration.
     */
    @Deprecated
    COMPANY_NOT_OPERATIONAL,

    /**
     * Indicates that the company is in an unsupported operational stage.
     */
    COMPANY_IN_UNSUPPORTED_OPERATIONAL_STAGE,

    /**
     * Indicates that the specified company is not recognized as a buyer.
     */
    COMPANY_NOT_A_BUYER,

    /**
     * Indicates that the company is not ready for migration.
     */
    NOT_READY_FOR_MIGRATION,

    /**
     * Indicates that the company has no employee accounts, which is required for migration.
     */
    COMPANY_HAS_NO_EMPLOYEE_ACCOUNTS,

    /**
     * Indicates that the company has no BPMD ID, which is required for exporting its orders to BRIM.
     */
    COMPANY_HAS_NO_BPMD_ID,

    /**
     * Indicates that the company's status is unapproved (approvalStatus = UNAPPROVED), which prevents migration.
     */
    COMPANY_IS_UNAPPROVED,

    /**
     * Indicates that the specified material was not found.
     */
    MATERIAL_NOT_FOUND,

    /**
     * Indicates that the specified contract is already migrated or taken for migration.
     */
    CONTRACT_ALREADY_MIGRATED,

    /**
     * Indicates that the specified distributor was not found.
     */
    DISTRIBUTOR_NOT_FOUND,

    /**
     * Indicates that there is a mismatch between the distributor's country and the buyer company's country.
     */
    DISTRIBUTOR_COUNTRY_MISMATCH,

    /**
     * Indicates that the start date is after the end date, which is invalid.
     */
    INVALID_START_END_DATE,

    /**
     * Indicates that the product is not available for the buyer's user group.
     */
    PRODUCT_NOT_AVAILABLE_FOR_BUYER_USER_GROUP,

    /**
     * Indicates that no price was found for the customer group associated with the buyer company.
     */
    CUSTOMER_GROUP_PRICE_NOT_FOUND,

    /**
     * Indicates that the specified product is not recognized as a bundle product.
     */
    NOT_A_BUNDLE_PRODUCT,

    /**
     * supplied bundle size bigger than the configured bundle size
     */
    BUNDLE_SIZE_EXCEEDED,

    /**
     * Indicates that the bundle identifier is shared between two or more products.
     */
    SHARED_BUNDLE_IDENTIFIER,

    /**
     * Indicates that the bundle does not have a unique bundle identifier.
     */
    EMPTY_UNIQUE_BUNDLE_IDENTIFIER,

    /**
     * Indicates that the contracts within a bundle have different distributors.
     */
    CONTRACTS_WITHIN_BUNDLE_HAVE_DIFFERENT_DISTRIBUTORS,

    /**
     * supplied company country Differs to Company country in the store.
     */
    COMPANY_COUNTRY_IN_CMT_AND_IN_STORE_MISMATCH,

    /**
     * Indicates that the specified managed account is not a managed account company.
     */
    NOT_A_MANAGED_ACCOUNT_COMPANY,

    /**
     * Indicates that the specified managed account company was not found.
     */
    MANAGED_ACCOUNT_COMPANY_NOT_FOUND,

    /**
     * Indicates that there are many company entries with the same managed account.
     */
    MANY_MANAGED_ACCOUNT_COMPANY_FOUND_USES_SAME_EXTERNAL_ID,

    /**
     * Indicates that the contract has both a distributor and a managed account company provided.
     * This is not allowed. Only contracts in Direct Sales countries can have a distributor,
     * while only contracts in Indirect Sales countries can have a managed account company.
     */
    CONTRACT_CONTAINS_BOTH_DISTRIBUTOR_AND_MANAGED_ACCOUNT_COMPANY,

    /**
     * All inactive product contracts of a given Draft Entry should have the same end date.
     */
    INACTIVE_PRODUCT_CONTRACTS_END_DATE_MISMATCH,

}
