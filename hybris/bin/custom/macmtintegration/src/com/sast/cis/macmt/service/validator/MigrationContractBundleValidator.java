package com.sast.cis.macmt.service.validator;

import com.google.common.collect.Sets;
import com.sast.cis.aa.core.model.MigrationContractModel;
import com.sast.cis.core.dao.CatalogVersion;
import com.sast.cis.core.model.AppLicenseModel;
import com.sast.cis.core.model.IoTCompanyModel;
import com.sast.cis.core.service.AppLicenseService;
import com.sast.cis.core.service.ProductCatalogIdentifierService;
import com.sast.cis.macmt.exception.ValidationErrorCode;
import com.sast.cis.macmt.service.dto.MigrationContractErrorDto;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.sast.cis.macmt.constants.MacmtintegrationConstants.strip999;
import static com.sast.cis.macmt.exception.ValidationErrorCode.*;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Slf4j
@Component
@RequiredArgsConstructor
public class MigrationContractBundleValidator {
    private final AppLicenseService appLicenseService;
    private final ProductCatalogIdentifierService productCatalogIdentifierService;

    public Set<MigrationContractErrorDto> validate(@NonNull final IoTCompanyModel iotCompany,
        @NonNull final Set<MigrationContractModel> migrationContracts) {
        Set<MigrationContractErrorDto> validationErrors = new HashSet<>();

        // Validate that bundle products have unique bundle identifiers
        validateBundleProductsHaveIdentifiers(iotCompany, migrationContracts, validationErrors);

        // Add results from existing bundle size validation
        validationErrors.addAll(validateBundleSize(iotCompany, migrationContracts));

        return validationErrors;
    }

    private Set<MigrationContractErrorDto> validateBundleSize(@NonNull IoTCompanyModel iotCompany,
        @NonNull Set<MigrationContractModel> migrationContracts) {
        final Set<MigrationContractErrorDto> validationErrors = new HashSet<>();
        // group by UniqueBundleIdentifier
        final Map<String, List<MigrationContractModel>> byUniqueIdentifier = migrationContracts.stream()
            .filter(cm -> isNotBlank(cm.getUniqueBundleIdentifier()))
            .collect(Collectors.groupingBy(MigrationContractModel::getUniqueBundleIdentifier));

        byUniqueIdentifier.forEach((uniqueBundleIdentifier, contractsByUniqueBundleIdentifier) -> {
            final Map<String, List<MigrationContractModel>> byMaterialId = contractsByUniqueBundleIdentifier.stream()
                .collect(Collectors.groupingBy(MigrationContractModel::getMaterialId));

            if (byMaterialId.size() != 1) {
                // Multiple materials sharing the same bundle identifier - not allowed
                addValidationErrorToAllContracts(contractsByUniqueBundleIdentifier, validationErrors, SHARED_BUNDLE_IDENTIFIER);
            } else {
                // Check if the material is actually a bundle product and validate its bundle size
                final String materialId = byMaterialId.keySet().iterator().next();
                getAppLicenseModel(iotCompany, materialId).ifPresent(appLicense -> ofNullable(appLicense.getBundleInfo()).ifPresentOrElse(
                    bundleInfo -> {
                        if (bundleInfo.getSize() < contractsByUniqueBundleIdentifier.size()) {
                            addValidationErrorToAllContracts(contractsByUniqueBundleIdentifier, validationErrors, BUNDLE_SIZE_EXCEEDED);
                        }
                    },
                    () -> addValidationErrorToAllContracts(contractsByUniqueBundleIdentifier, validationErrors, NOT_A_BUNDLE_PRODUCT)
                ));
            }
            validateBundleContractsHaveSameDistributor(uniqueBundleIdentifier, contractsByUniqueBundleIdentifier, validationErrors);
        });
        return validationErrors;
    }

    private void validateBundleProductsHaveIdentifiers(@NonNull IoTCompanyModel iotCompany,
        @NonNull Set<MigrationContractModel> migrationContracts,
        Set<MigrationContractErrorDto> validationErrors) {

        for (MigrationContractModel contract : migrationContracts) {
            getAppLicenseModel(iotCompany, contract.getMaterialId()).ifPresent(appLicense -> {
                if (appLicense.getBundleInfo() != null &&
                    appLicense.getBundleInfo().getSize() > 1 &&
                    StringUtils.isBlank(contract.getUniqueBundleIdentifier())) {
                    // Bundle product must have a unique bundle identifier
                    final MigrationContractErrorDto contractError = MigrationContractErrorDto.builder()
                        .upmContractId(contract.getUmpContractId())
                        .cmtId(contract.getCmtId())
                        .errors(Sets.newHashSet(EMPTY_UNIQUE_BUNDLE_IDENTIFIER.name()))
                        .build();
                    validationErrors.add(contractError);
                }
            });
        }
    }

    private void validateBundleContractsHaveSameDistributor(
        final String uniqueBundleIdentifier,
        final List<MigrationContractModel> migrationContracts,
        final Set<MigrationContractErrorDto> validationErrors) {

        final List<String> distributors = migrationContracts.stream()
            .map(MigrationContractModel::getDistributor)
            .distinct()
            .toList();
        if (distributors.size() > 1) {
            LOG.warn(
                "Multiple distributors found for the same bundle identifier: {}. Distributors: {}",
                uniqueBundleIdentifier, distributors
            );
            addValidationErrorToAllContracts(migrationContracts, validationErrors, CONTRACTS_WITHIN_BUNDLE_HAVE_DIFFERENT_DISTRIBUTORS);
        }
    }

    private void addValidationErrorToAllContracts(List<MigrationContractModel> contractsByUniqueBundleIdentifier,
        Set<MigrationContractErrorDto> validationErrors, ValidationErrorCode errorCode) {
        contractsByUniqueBundleIdentifier.forEach(contract -> {
            MigrationContractErrorDto contractError = MigrationContractErrorDto.builder()
                .upmContractId(contract.getUmpContractId())
                .cmtId(contract.getCmtId())
                .errors(Sets.newHashSet((errorCode.name()))).build();
            validationErrors.add(contractError);
        });
    }

    private Optional<AppLicenseModel> getAppLicenseModel(IoTCompanyModel buyerCompany, String materialId) {
        final String catalogId = productCatalogIdentifierService.getSessionProductCatalog().getId();
        final String countryIsoCode = buyerCompany.getCountry().getIsocode();
        return appLicenseService.getAppLicenseForSellerProductIdAndCountry(
            strip999(materialId), countryIsoCode, catalogId, CatalogVersion.ONLINE);
    }
}
