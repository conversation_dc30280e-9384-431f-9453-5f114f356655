package com.sast.cis.devcon.api.orders.controller;

import com.google.gson.Gson;
import com.sast.cis.core.data.PriceData;
import com.sast.cis.core.data.SBIData;
import com.sast.cis.devcon.api.controller.BaseControllerUnitTest;
import com.sast.cis.devcon.api.controller.handlers.DevconRestApiExceptionHandler;
import com.sast.cis.devcon.api.orders.facade.SelfBillingInvoiceFacade;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.core.servicelayer.data.PaginationData;
import de.hybris.platform.core.servicelayer.data.SearchPageData;
import de.hybris.platform.core.servicelayer.data.SortData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class RevenueControllerUnitTest extends BaseControllerUnitTest {

    @Mock
    private SelfBillingInvoiceFacade selfBillingInvoiceFacade;
    @InjectMocks
    private RevenueController revenueController;

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders
            .standaloneSetup(revenueController)
            .setControllerAdvice(new DevconRestApiExceptionHandler(null))
            .setMessageConverters(new MappingJackson2HttpMessageConverter(objectMapper))
            .build();
    }

    @Test
    public void getRevenue_returnsListOfRevenues() throws Exception {
        PaginationData pagination = new PaginationData().withNeedsTotal(true)
            .withPageSize(5)
            .withHasNext(false)
            .withHasPrevious(false)
            .withNumberOfPages(1)
            .withTotalNumberOfResults(1);
        var results = List.of(getSbiData());
        SortData invoice = new SortData().withAsc(false).withCode("invoicedate");
        var sorts = List.of(invoice);
        var searchPageData = new SearchPageData<SBIData>()
            .withPagination(pagination).withResults(results).withSorts(sorts);
        when(selfBillingInvoiceFacade.getSbiData(any())).thenReturn(searchPageData);

        MvcResult mvcResult = mockMvc.perform(get("/revenues")
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().json(toString(searchPageData)))
            .andReturn();

        String responseBody = mvcResult.getResponse().getContentAsString();
        SearchPageData<SBIData> actualSearchPageData = new Gson().fromJson(responseBody, SearchPageData.class);
        assertThat(actualSearchPageData.getPagination()).usingRecursiveComparison().isEqualTo(searchPageData.getPagination());
        assertThat(actualSearchPageData.getSorts())
            .usingRecursiveFieldByFieldElementComparatorOnFields()
            .contains(searchPageData.getSorts().toArray(new SortData[0]));
        assertThat(actualSearchPageData.getResults().size()).isEqualTo(searchPageData.getResults().size());
    }

    private SBIData getSbiData() {
        SBIData sbiData = new SBIData();
        sbiData.setOrderId("1234");
        sbiData.setInvoiceId("232334343");
        sbiData.setInvoiceUrl("invoiceUrl");
        sbiData.setPaymentStatus("paymentStatus");
        sbiData.setTotalPrice(new PriceData().withSymbol("EUR").withValue("10"));
        sbiData.setApps(Collections.emptyList());
        sbiData.setCustomer("customer");
        sbiData.setDate(new Date());
        return sbiData;
    }
}
