package com.sast.cis.devcon.api.companyprofile.controller;

import com.sast.cis.companyprofile.data.CompanySizeData;
import com.sast.cis.companyprofile.data.CompanySizeList;
import com.sast.cis.companyprofile.facade.CompanyProfileFacade;
import com.sast.cis.core.service.TranslationService;
import com.sast.cis.devcon.api.controller.BaseControllerUnitTest;
import com.sast.cis.devcon.api.controller.handlers.DevconRestApiExceptionHandler;
import de.hybris.bootstrap.annotations.UnitTest;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CompanySizeControllerUnitTest extends BaseControllerUnitTest {
    private static final String BASE_PATH = "/company-sizes";

    @Mock
    private CompanyProfileFacade companyProfileFacade;

    @Mock
    private TranslationService translationService;

    @InjectMocks
    private CompanySizeController companySizeController;

    @Before
    public void setUp() {
        this.mockMvc = MockMvcBuilders
            .standaloneSetup(companySizeController)
            .setControllerAdvice(new DevconRestApiExceptionHandler(translationService))
            .build();
    }

    @Test
    @SneakyThrows
    public void getCompanySizes_noCompanySizes_returnsEmptySupportedCompanySizeResponse() {
        var companySizeList = new CompanySizeList().withSupportedCompanySizes(List.of());
        when(companyProfileFacade.getCompanySizeList()).thenReturn(companySizeList);

        String jsonResponse = mockMvc.perform(get(BASE_PATH)
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andReturn().getResponse().getContentAsString();

        CompanySizeList actualData = toObject(jsonResponse, CompanySizeList.class);
        assertThat(actualData).isNotNull();
        assertThat(actualData.getSupportedCompanySizes()).isEmpty();
        assertThat(actualData).usingRecursiveComparison().isEqualTo(companySizeList);
        verify(companyProfileFacade).getCompanySizeList();
    }

    @Test
    @SneakyThrows
    public void getCompanySizes_existsCompanySizes_returnsSupportedCompanySizeResponse() {
        CompanySizeData mini = new CompanySizeData().withCode("MINI").withValue("2-5 employees").withOrder(2);
        CompanySizeData small = new CompanySizeData().withCode("SMALL").withValue("6-10 employees").withOrder(2);
        var companySizeList = new CompanySizeList().withSupportedCompanySizes(List.of(mini, small));
        when(companyProfileFacade.getCompanySizeList()).thenReturn(companySizeList);

        String jsonResponse = mockMvc.perform(get(BASE_PATH)
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andReturn().getResponse().getContentAsString();

        CompanySizeList actualData = toObject(jsonResponse, CompanySizeList.class);
        assertThat(actualData).isNotNull();
        assertThat(actualData).usingRecursiveComparison().isEqualTo(companySizeList);
        verify(companyProfileFacade).getCompanySizeList();
    }
}
