#% impex.setLocale( Locale.ENGLISH );

# numerical code for FR, used as prefix for product code
$frCc = 033
$aaCc = 040
$aaPackageName = com.sast.aa.fr.

$productCatalog = aav2ProductCatalog
$catalogVersion = catalogversion(catalog(id[default = $productCatalog]), version[default = 'Staged'])
$supercategories = supercategories(code, $catalogVersion)
$baseProduct = baseProduct(code, $catalogVersion)
$approved = approvalstatus(code)[default = 'approved']
$taxGroup = Europe1PriceFactory_PTG(code)[default = eu-vat-full]
$emailAddress = <EMAIL>

UPDATE GenericItem[processor = de.hybris.platform.commerceservices.impex.impl.ConfigPropertyImportProcessor]; pk[unique = true]
$companyId = $config-franceSeller1Id
$staticLegalResourcesUrl = $config-aa.corporate.home

$privacyPolicyUrl = $staticLegalResourcesUrl/Datenschutzhinweise_ESItronic_AT_2021-01.pdf

$packDiagnosticDescription = L'essentiel pour le diagnostic, la réparation et l'entretien professionnels. Il permet un diagnostic électronique efficace et offre un large éventail d'autres fonctions pour tous les véhicules couverts. Fonction SDA incluse.
$packDiagnosticDescription_en = The entry into professional diagnosis, repair and maintenance. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.
$packAdvancedDescription = Le niveau supérieur pour des diagnostics professionnels. En plus du Pack Diagnostic, des instructions et des manuels sont inclus. Connected Repair fournit l'historique de l'entretien et de la maintenance ainsi que des informations sur les réparations. Fonction SDA incluse.
$packAdvancedDescription_en = The next level of professional workshop equipment. In addition to the Diagnostic package, instructions and manuals are included. Connected Repair provides service and maintenance history and repair information.
$packMasterDescription = Le logiciel complet pour le diagnostic professionnel des véhicules. Il fournit toutes les informations nécessaires au diagnostic, à la réparation, à l'entretien, aux pièces de rechange, à la documentation et à la gestion des données. L'assistance technique vous aide à trouver des solutions. Fonction SDA incluse.
$packMasterDescription_en = The fully comprehensive package for professional vehicle diagnostics. It provides all the necessary information for diagnosis, repair, maintenance, spare parts, documentation and data management. Technical Support assists you in finding solutions.

$packComponentCatDescription = Le catalogue des pièces détachées comprend les applications, les fonctions et l'équipement automobile ainsi que les pièces détachées diesel et les pièces détachées électriques, y compris les archives et la pièce détachée électrique ESI[tronic]-F.
$packComponentCatDescription_en = The spare parts catalog package includes the applications, functions and automotive equipment as well as the diesel spare parts and electrical spare parts incl. archive and electrical spare part ESI[tronic]-F.
$packComponentRepairDieselDescription = Le pack Réparation Diesel & Electrique fournit des informations sur les pièces détachées et la réparation des composants diesel et électrique. Il permet d'identifier le véhicule, l'équipement automobile Bosch et comprend des instructions de réparation et des informations sur l'entretien.
$packComponentRepairDieselDescription_en = The Repair Diesel package provides information on spare parts and repair of diesel and electrical components. It allows identification of the vehicle, Bosch automotive equipment and includes repair instructions & service information.
$packComponentRepairElectricDescription = En raison du nombre croissant de modèles de véhicules, il est difficile pour les ateliers de disposer d'informations actualisées sur les systèmes électriques des véhicules. Le pack Réparation Electrique fournit une assistance avec des données sur les pièces détachées pour les systèmes électriques des voitures dans un format clair.
$packComponentRepairElectricDescription_en = The increasing number of vehicle models makes it difficult for workshops to have up-to-date information on vehicle electrical systems at their fingertips. The Repair Electrics Package provides support with spare parts data on car electrics in a clear format.

$packTruckDescription = Le pack Truck permet aux ateliers d'effectuer un diagnostic efficace, un entretien complet et une réparation efficace de tous les véhicules utilitaires légers et poids lourds, remorques, utilitaires et autobus courants.
$packTruckDescription_en = The Truck package supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.

$ohw1Description = Le pack de diagnostic pour les machines agricoles fournit des informations sur le diagnostic, l'entretien et la réparation des véhicules agricoles. Il comprend, entre autres, des fonctions de réglage et de paramétrage des systèmes hydrauliques.
$ohw1Description_en = The Agricultural Machinery Diagnostic Package provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.
$ohw2Description = Le pack de diagnostic pour les engins de construction et les machines fournit des informations sur le diagnostic, l'entretien et la réparation des véhicules agricoles. Il comprend notamment des fonctions de réglage et de paramétrage des systèmes hydrauliques.
$ohw2Description_en = The diagnosis package for construction machinery and engines provides information on the diagnosis, maintenance and repair of agricultural vehicles. Included are, among other things, adjustment and parameterisation functions for hydraulic systems.

$thlPkwDescription = Vous avez besoin d'une assistance technique pour l'entretien ou la réparation d'une voiture, ou simplement d'un deuxième avis fiable ? Contactez notre équipe d'assistance et obtenez des solutions rapides et fiables.
$thlPkwDescription_en = Do you need technical support to maintain or repair a car, or simply a reliable second opinion? Then contact our support team and get fast and sound solutions.

$alltrucksDescription = Le logiciel Alltrucks contient des informations importantes sur les véhicules utilitaires, telles que la série du modèle, les performances, l'identification du moteur et la configuration des essieux. Inclut ESI[tronic] Truck, NEO | orange de Knorr-Bremse et ZF-TESTMAN de ZF.
$alltrucksDescription_en = The Alltrucks software contains important information about commercial vehicles such as model series, performance, engine identification as well as axle configuration. Includes ESI[tronic] Truck, NEO | orange from Knorr-Bremse and ZF-TESTMAN from ZF.
$compacFsa500Description = Le CompacSoft[plus] pour FSA 500 est équipé de tests de composants prédéfinis et peut être connecté à des systèmes existants, ou être utilisé pour étendre progressivement votre système de test en atelier.
$compacFsa500Description_en = The CompacSoft[plus] for FSA 500 is equipped with preset component tests and can be connected to existing systems, as well as used to gradually expand your workshop test system.
$compacFsa7xxDescription = Avec CompacSoft[plus] pour FSA 7xx, le confort pour toutes les tâches de mesure sur le véhicule est encore accru par les étapes de test guidées par le menu, les valeurs de consigne spécifiques au véhicule en option, ainsi que l'affichage des valeurs réelles.
$compacFsa7xxDescription_en = With CompacSoft[plus] for FSA 7xx, the comfort for all measuring tasks on the vehicle is further increased by the menu-guided test steps, the optional vehicle-specific set values, as well as the display of the actual values.

$criDescription = Le logiciel ESI[tronic] CRI pour DCI 700 fournit des données actualisées, garantit le bon déroulement des processus et inclut les essais d'injecteurs piézo pour les systèmes Common Rail Bosch VL.
$criDescription_en = CRI for DCI 700 software provides up-to-date data, ensures smooth processes and includes testing of piezo injectors for common rail systems.
$crinDescription = Le logiciel ESI[tronic] CRIN pour DCI 700 fournit des données actualisées, garantit le bon déroulement des processus et permet de tester les injecteurs à électrovanne pour les systèmes Common Rail Bosch PL.
$crinDescription_en = CRIN for DCI 700 software provides up-to-date data, ensures smooth processes and includes the testing of solenoid valve injectors for common rail systems.

$coReDescription = Bosch Connected Repair est un logiciel qui relie les équipements de l'atelier, le véhicule et les données de réparation. Que ce soit en cas de dysfonctionnement ou pour le stockage de données et d'images, CoRe OnLine a été adapté aux besoins des utilisateurs, et respecte le règlement de base sur la protection des données.
$coReDescription_en = Bosch Connected Repair is software that connects workshop equipment, vehicle and repair data. Whether in the event of malfunctions or the storage of data and images in accordance with the Basic Data Protection Regulation - CoRe has been adapted to the needs of customers.

$kts250SDDescription = L'essentiel pour le diagnostic professionnel, la réparation et l'entretien spécifiquement pour le KTS 250. Cette licence permet un diagnostic électrique efficace et offre un large éventail d'autres fonctions pour tous les véhicules couverts. Fonction SDA incluse.
$kts250SDDescription_en = The entry into professional diagnosis, repair and maintenance specifically for KTS 250. It enables competent electrical diagnosis and offers a wide range of other functions for all covered vehicles.

$infoartWDescription = L'ESI[tronic] 2.0 W contient des informations sur les valeurs de test diesel pour les combinaisons de pompes en ligne ainsi que pour les pompes VE, la procédure de test complet du processus de contrôle : de la détermination des valeurs de mesure à l'impression du rapport et à l'affichage des étapes de test dans l'ordre du protocole.
$infoartWDescription_en = The ESI[tronic] 2.0 Infoart W contains information about diesel test values for in-line pump combinations as well as for VE pumps, the complete test process test procedure from the determination of the measured values to the printout of the report and the display of the test steps in the optimum sequence.

$infoartTestdataDescription = Testdata d'ESI[tronic] 2.0 contient les valeurs de test pour les pompes haute pression Common Rail Bosch, les injecteurs Common Rail et les pompes d'injection VP 29 / 30 / 44.
$infoartTestdataDescription_en = The ESI[tronic] 2.0-Infotype Testdata (CD) contains test values for Bosch Common Rail high pressure pumps, Common Rail injectors and VP 29 / 30 / 44 distributor injection pumps.
$truckUpgradeDescription = La 2ème ligence ESI[tronic] 2.0 Truck est dédié aux utilisateurs d'ESI[tronic] VL et permet aux ateliers d'effectuer un diagnostic efficace, une maintenance complète et une réparation efficace de tous les véhicules utilitaires légers et lourds, remorques, utilitaires et autobus.
$truckUpgradeDescription_en = The Truck Upgrade package is dedicated to ESI[tronic] Car users and supports workshops in the reliable diagnosis, complete maintenance and efficient repair of all common light and heavy commercial vehicles, trailers, vans and buses.

$thlKtsTruckDescription = La Hotline SAV vous assiste dans l'utilisation de votre KTS et du logiciel ESI[tronic]
$thlKtsTruckDescription_en =
$thlKts3hDescription = La Hotline SAV vous assiste dans l'utilisation de votre KTS et du logiciel ESI[tronic] + 3h Hotline Diagnostic
$thlKts3hDescription_en =
$thlDiag1hDescription = Vous avez besoin d'une assistance technique pour l'entretien ou la réparation d'une voiture, ou simplement d'un deuxième avis fiable ? Contactez notre Hotline Diagnostic et obtenez des solutions rapides et fiables.
$thlDiag1hDescription_en =
$thlDiag3hDescription = Vous avez besoin d'une assistance technique pour l'entretien ou la réparation d'une voiture, ou simplement d'un deuxième avis fiable ? Contactez notre Hotline Diagnostic et obtenez des solutions rapides et fiables.
$thlDiag3hDescription_en =
$thlDiag9hDescription = Vous avez besoin d'une assistance technique pour l'entretien ou la réparation d'une voiture, ou simplement d'un deuxième avis fiable ? Contactez notre Hotline Diagnostic et obtenez des solutions rapides et fiables.
$thlDiag9hDescription_en =
$thlDiag17hDescription = Vous avez besoin d'une assistance technique pour l'entretien ou la réparation d'une voiture, ou simplement d'un deuxième avis fiable ? Contactez notre Hotline Diagnostic et obtenez des solutions rapides et fiables.
$thlDiag17hDescription_en =
$thlDiagBcs9hDescription = Vous avez besoin d'une assistance technique pour l'entretien ou la réparation d'une voiture, ou simplement d'un deuxième avis fiable ? Contactez notre Hotline Diagnostic et obtenez des solutions rapides et fiables.
$thlDiagBcs9hDescription_en =
$thlDiagBcs16hDescription = Vous avez besoin d'une assistance technique pour l'entretien ou la réparation d'une voiture, ou simplement d'un deuxième avis fiable ? Contactez notre Hotline Diagnostic et obtenez des solutions rapides et fiables.
$thlDiagBcs16hDescription_en =
$diagIsotechSDDescription = Fonctionnalité de diagnostic spécialement conçue pour le Diag Isotech. La licence Diag Isotech permet un diagnostic électrique efficace et offre une large gamme d'autres fonctions pour tous les véhicules couverts. Fonction SDA incluse.
$diagIsotechSDDescription_en =

$alltrucksSubsS1BrimName = Alltrucks Diagnose-Paket
$alltrucksSubsM3BrimName = ESI[tronic] 2.0 All Trucks Unlim Multi
$compacFsa500Full3YBrimName = CompacSoft[plus] FSA 500 (3 years)
$compacFsa500SubsBrimName = CompacSoft[plus] FSA 500
$compacFsa7xxFull3YBrimName = CompacSoft[plus] FSA 7xx (3 years)
$compacFsa7xxSubsBrimName = CompacSoft[plus] FSA 7xx
$criSubsS1BrimName = Component DCI-CRI
$criSubsM3BrimName = Component DCI-CRI_Multi-users_3
$criFull3YBrimName = Component DCI-CRI (3 year)
$crinSubsS1BrimName = Component DCI-CRIN
$crinSubsM3BrimName = Component DCI-CRIN_Multi-users_3
$crinFull3YBrimName = Component DCI-CRIN (3 year)
$packAdvancedSubsS1BrimName = ESI 2.0 Advanced Unlimited
$packAdvancedSubsM3BrimName = ESI 2.0 Advanced Unlimited Multi
$packAdvancedFull3YS1BrimName = ESI 2.0 Advanced 3 years
$packAdvancedFull3YM3BrimName = ESI 2.0 Advanced 3 years Multi
$packComponentCatSubsS1BrimName = ESI 2.0 ComponentCatalog D+E Unlimited
$packComponentCatSubsM3BrimName = ESI 2.0 ComponentCatalog D+E Unlim Multi
$packComponentCatFull3YS1BrimName = ESI 2.0 ComponentCatalog D+E 3 years
$packComponentCatFull3YM3BrimName = ESI 2.0 ComponentCatalog D+E 3y Multi
$packComponentRepairDieselSubsS1BrimName = ESI 2.0 ComponentRepair D+E Unlimited
$packComponentRepairDieselSubsM3BrimName = ESI 2.0 ComponentRepair D+E Unlim Multi
$packComponentRepairDieselFull3YS1BrimName = ESI 2.0 ComponentRepair D+E 3 years
$packComponentRepairDieselFull3YM3BrimName = ESI 2.0 ComponentRepair D+E 3y Multi
$packComponentRepairElectricSubsS1BrimName = ESI 2.0 ComponentRepair E Unlimited
$packComponentRepairElectricSubsM3BrimName = ESI 2.0 ComponentRepair E Unlim Multi
$packComponentRepairElectricFull3YS1BrimName = ESI 2.0 ComponentRepair E 3 years
$packComponentRepairElectricFull3YM3BrimName = ESI 2.0 ComponentRepair E 3 years Multi
$packDiagnosticSubsS1BrimName = ESI 2.0 Diagnostic Unlimited
$packDiagnosticSubsM3BrimName = ESI 2.0 Diagnostic Unlimited Multi
$packDiagnosticFull3YS1BrimName = ESI 2.0 Diagnostic 3 years
$packDiagnosticFull3YM3BrimName = ESI 2.0 Diagnostic 3 years Multi
$packMasterSubsS1BrimName = ESI 2.0 Master Unlimited
$packMasterSubsM3BrimName = ESI 2.0 Master Unlimited Multi
$packMasterFull3YS1BrimName = ESI 2.0 Master 3 years
$packMasterFull3YM3BrimName = ESI 2.0 Master 3 years Multi

$ohw1SubsS1BrimName = ESI[tronic] 2.0 Truck OHW I
$ohw1SubsM3BrimName = ESI[tronic] 2.0 Truck OHW I Multi
$ohw1Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW I (3 years)
$ohw1Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW I (3y) Multi

$ohw2SubsS1BrimName = ESI[tronic] 2.0 Truck OHW II
$ohw2SubsM3BrimName = ESI[tronic] 2.0 Truck OHW II Multi
$ohw2Full3YS1BrimName = ESI[tronic] 2.0 Truck OHW II (3 years)
$ohw2Full3YM3BrimName = ESI[tronic] 2.0 Truck OHW II (3y) Multi

$packTruckSubsS1BrimName = ESI[tronic] 2.0 Truck
$packTruckSubsM3BrimName = ESI[tronic] 2.0 Truck Multi
$packTruckFull3YS1BrimName = ESI[tronic] 2.0 Truck (3 years)
$packTruckFull3YM3BrimName = ESI[tronic] 2.0 Truck (3 years) Multi
$packTruckFullBrimName = ESI[tronic] 2.0 Truck (Einmalkauf)
$infoartWSubsBrimName = ESI[tronic] W Diesel Test Data (WP)
$infoartWFull3YBrimName = ESI[tronic] W (3 Jahre)
$thlPkwSubsBrimName = THL use technical hotline
$thlPkwFull3YBrimName = Technische Hotline LKW (3 Jahre)
$thlTrkSubsBrimName = Technische Hotline ESI[tronic] for Truck
$thlPkwTrkSubsBrimName = Technische Hotline fuer LKWs und PKWs
$infoartTestdataSubsBrimName = Testdata VP-M/CP
$truckUpgradeSubsS1BrimName = ESI[tronic] 2.0 Truck Upgrade
$truckUpgradeSubsM3BrimName = ESI[tronic] 2.0 Truck Upgrade Multi
$truckUpgradeFull3YS1BrimName = ESI[tronic] 2.0 Truck Upgrade (3 years)
$truckUpgradeFull3YM3BrimName = ESI[tronic] 2.0 Truck Upgrade (3y) Multi

$coreSubsS1BrimName = CoRe for ESI 2.0 packages

$kts250SDFullBrimName = KTS 250 SD (OTP one time purchase)
$kts250SDSubsBrimName = KTS 250 SD ECU Diagnosis

$thlKtsTruckSubsBrimName = HOTLINE KTS Truck Unlimited
$thlKtsTruckFull3YBrimName = HOTLINE KTS Truck 3 Years (3 h/year)
$thlKts3HSubsBrimName = HOTLINE KTS 3h Unlimited
$thlKts3HFull3YBrimName = HOTLINE KTS 3h 3 Years (3 h/year)
$thlDiag1HFullBrimName = HOTLINE DIAG FORFAIT 1h
$thlDiag3HFullBrimName = HOTLINE DIAG FORFAIT 3h
$thlDiag9HSubsBrimName = HOTLINE DIAG FORFAIT 9h
$thlDiag17HFullBrimName = HOTLINE DIAG FORFAIT 17H
$thlBcsDiag9HFullBrimName = HOTLINE BCS DIAG FORFAIT 9h
$thlBcsDiag16HFullBrimName = HOTLINE BCS DIAG FORFAIT 16h
$diagIsoTechSDSubsBrimName = DiagISOTECH SD Steuergeraetediagnose
$diagIsoTechSDFullBrimName = Diagisotech OTP
$fsa7xxMulti = CompacSoft[plus] FSA 7xx Multi
$fsa7xx3YearsMulti = CompacSoft[plus] FSA 7xx (3 years) Multi
$fsa5xxMulti = CompacSoft[plus] FSA 500 Multi
$fsa5xx3YearsMulti = CompacSoft[plus] FSA 500 (3 years)Multi

$countryRestricted = true
# @formatter:off
$enabledIn = FR
# @formatter:on

INSERT_UPDATE App; code[unique = true]         ; packageName                      ; emailAddress[default = $emailAddress]; submittedBy(uid); storeAvailabilityMode(code)[default = RESTRICTED_BUYER_GROUP]; unit(code)[default = pieces]; variantType(code)[default = AppLicense]; $catalogVersion[unique = true]; $approved; $taxGroup; privacyPolicyUrl[default = $privacyPolicyUrl]; termsOfUseUrl; supportPageUrl; productWebsiteUrl; company(uid)[default = $companyId]; countryRestricted[default = $countryRestricted]
                 ; AA2_$frCc1987_ALLTRUCKS     ; $aaPackageName1987_ALLTRUCKS     ;
                 ; AA2_$frCc1687_CSFSA500      ; $aaPackageName1687_CSFSA500      ;
                 ; AA2_$frCc1687_CSFSA7XX      ; $aaPackageName1687_CSFSA7XX      ;
                 ; AA2_$frCc1687_DCICRI        ; $aaPackageName1687_DCICRI        ;
                 ; AA2_$frCc1687_DCICRIN       ; $aaPackageName1687_DCICRIN       ;
                 ; AA2_$frCc1987_ESIADV        ; $aaPackageName1987_ESIADV        ;
                 ; AA2_$frCc1987_ESIREPCAT     ; $aaPackageName1987_ESIREPCAT     ;
                 ; AA2_$frCc1987_ESIREPD       ; $aaPackageName1987_ESIREPD       ;
                 ; AA2_$frCc1987_ESIREPE       ; $aaPackageName1987_ESIREPE       ;
                 ; AA2_$frCc1987_ESIDIAG       ; $aaPackageName1987_ESIDIAG       ;
                 ; AA2_$frCc1987_ESIMASTER     ; $aaPackageName1987_ESIMASTER     ;
                 ; AA2_$frCc1987_TRKOHW1       ; $aaPackageName1987_TRKOHW1       ;
                 ; AA2_$frCc1987_TRKOHW2       ; $aaPackageName1987_TRKOHW2       ;
                 ; AA2_$frCc1987_TRKTRUCK      ; $aaPackageName1987_TRKTRUCK      ;
                 ; AA2_$frCc1987_TSTINFOAW     ; $aaPackageName1987_TSTINFOAW     ;
                 ; AA2_$frCc1687_TSTINFODAT    ; $aaPackageName1687_TSTINFODAT    ;
                 ; AA2_$frCc1987_TRKUPG        ; $aaPackageName1987_TRUCKUPG      ;
                 ; AA2_$frCc1987_KTS250SD      ; $aaPackageName1987_KTS250        ;
                 ; AA2_$frCc1987_THLKTSTRK     ; $aaPackageName1987_THLKTSTRK     ;
                 ; AA2_$frCc1987_THLKTS3H      ; $aaPackageName1987_THLKTS3H      ;
                 ; AA2_$frCc1987_THLDIAG1H     ; $aaPackageName1987_THLDIAG1H     ;
                 ; AA2_$frCc1987_THLDIAG3H     ; $aaPackageName1987_THLDIAG3H     ;
                 ; AA2_$frCc1987_THLDIAG9H     ; $aaPackageName1987_THLDIAG9H     ;
                 ; AA2_$frCc1987_THLDIAG17H    ; $aaPackageName1987_THLDIAG17H    ;
                 ; AA2_$frCc1987_THLBCSDIAG9H  ; $aaPackageName1987_THLBCSDIAG9H  ;
                 ; AA2_$frCc1987_THLBCSDIAG16H ; $aaPackageName1987_THLBCSDIAG16H ;
                 ; AA2_$frCc1987_DIAGISOTECHSD ; $aaPackageName1987_DIAGISOTECHSD ;


INSERT_UPDATE App; code[unique = true]         ; name[lang = fr]                          ; summary[lang = fr]                      ; description[lang = fr]; $catalogVersion[unique = true];
                 ; AA2_$frCc1987_ALLTRUCKS     ; ESI[tronic] Alltrucks                    ; $alltrucksDescription                   ; $alltrucksDescription
                 ; AA2_$frCc1687_CSFSA500      ; CompacSoft[plus] FSA 500                 ; $compacFsa500Description                ; $compacFsa500Description
                 ; AA2_$frCc1687_CSFSA7XX      ; CompacSoft[plus] FSA 7xx                 ; $compacFsa7xxDescription                ; $compacFsa7xxDescription
                 ; AA2_$frCc1687_DCICRI        ; Composant DCI-CRI                        ; $criDescription                         ; $criDescription
                 ; AA2_$frCc1687_DCICRIN       ; Composant DCI-CRIN                       ; $crinDescription                        ; $crinDescription
                 ; AA2_$frCc1987_ESIADV        ; ESI[tronic] Advanced                     ; $packAdvancedDescription                ; $packAdvancedDescription
                 ; AA2_$frCc1987_ESIREPCAT     ; ESI[tronic] Catalogue                    ; $packComponentCatDescription            ; $packComponentCatDescription
                 ; AA2_$frCc1987_ESIREPD       ; ESI[tronic] Repair D+E                   ; $packComponentRepairDieselDescription   ; $packComponentRepairDieselDescription
                 ; AA2_$frCc1987_ESIREPE       ; ESI[tronic] Repair E                     ; $packComponentRepairElectricDescription ; $packComponentRepairElectricDescription
                 ; AA2_$frCc1987_ESIDIAG       ; ESI[tronic] Diagnostic                   ; $packDiagnosticDescription              ; $packDiagnosticDescription
                 ; AA2_$frCc1987_ESIMASTER     ; ESI[tronic] Master                       ; $packMasterDescription                  ; $packMasterDescription
                 ; AA2_$frCc1987_TRKOHW1       ; ESI[tronic] Off Highway I (OHW I)        ; $ohw1Description                        ; $ohw1Description
                 ; AA2_$frCc1987_TRKOHW2       ; ESI[tronic] Off Highway II (OHW II)      ; $ohw2Description                        ; $ohw2Description
                 ; AA2_$frCc1987_TRKTRUCK      ; ESI[tronic] Truck                        ; $packTruckDescription                   ; $packTruckDescription
                 ; AA2_$frCc1987_TSTINFOAW     ; ESI[tronic] W                            ; $infoartWDescription                    ; $infoartWDescription
                 ; AA2_$frCc1687_TSTINFODAT    ; Testdata VP-M/CP                         ; $infoartTestdataDescription             ; $infoartTestdataDescription
                 ; AA2_$frCc1987_TRKUPG        ; ESI[tronic] Truck Upgrade                ; $truckUpgradeDescription                ; $truckUpgradeDescription
                 ; AA2_$frCc1987_KTS250SD      ; KTS 250 SD                               ; $kts250SDDescription                    ; $kts250SDDescription

                 ; AA2_$frCc1987_THLKTSTRK     ; Hotline SAV : Utilisation KTS & Logiciel ; $thlKtsTruckDescription                 ; $thlKtsTruckDescription
                 ; AA2_$frCc1987_THLKTS3H      ; Hotline SAV: Utilisation KTS & Logiciel  ; $thlKts3hDescription                    ; $thlKts3hDescription
                 ; AA2_$frCc1987_THLDIAG1H     ; Hotline Diagnostic 1h                    ; $thlDiag1hDescription                   ; $thlDiag1hDescription
                 ; AA2_$frCc1987_THLDIAG3H     ; Hotline Diagnostic 3h                    ; $thlDiag3hDescription                   ; $thlDiag3hDescription
                 ; AA2_$frCc1987_THLDIAG9H     ; Hotline Diagnostic 9h                    ; $thlDiag9hDescription                   ; $thlDiag9hDescription
                 ; AA2_$frCc1987_THLDIAG17H    ; Hotline Diagnostic 17h                   ; $thlDiag17hDescription                  ; $thlDiag17hDescription
                 ; AA2_$frCc1987_THLBCSDIAG9H  ; Hotline Diagnostic BCS 9h                ; $thlDiagBcs9hDescription                ; $thlDiagBcs9hDescription
                 ; AA2_$frCc1987_THLBCSDIAG16H ; Hotline Diagnostic BCS 16h               ; $thlDiagBcs16hDescription               ; $thlDiagBcs16hDescription
                 ; AA2_$frCc1987_DIAGISOTECHSD ; Diag Isotech SD                          ; $diagIsotechSDDescription               ; $diagIsotechSDDescription


INSERT_UPDATE App; code[unique = true]         ; name[lang = en]                      ; summary[lang = en]                         ; description[lang = en]; $catalogVersion[unique = true];
                 ; AA2_$frCc1987_ALLTRUCKS     ; Alltrucks Diagnosis                  ; $alltrucksDescription_en                   ; $alltrucksDescription_en
                 ; AA2_$frCc1687_CSFSA500      ; CompacSoft[plus] FSA 500             ; $compacFsa500Description_en                ; $compacFsa500Description_en
                 ; AA2_$frCc1687_CSFSA7XX      ; CompacSoft[plus] FSA 7xx             ; $compacFsa7xxDescription_en                ; $compacFsa7xxDescription_en
                 ; AA2_$frCc1687_DCICRI        ; Component DCI-CRI                    ; $criDescription_en                         ; $criDescription_en
                 ; AA2_$frCc1687_DCICRIN       ; Component DCI-CRIN                   ; $crinDescription_en                        ; $crinDescription_en
                 ; AA2_$frCc1987_ESIADV        ; ESI[tronic] 2.0 Advanced             ; $packAdvancedDescription_en                ; $packAdvancedDescription_en
                 ; AA2_$frCc1987_ESIREPCAT     ; ESI[tronic] 2.0 ComponentCat D+E     ; $packComponentCatDescription_en            ; $packComponentCatDescription_en
                 ; AA2_$frCc1987_ESIREPD       ; ESI[tronic] 2.0 ComponentRepair D+E  ; $packComponentRepairDieselDescription_en   ; $packComponentRepairDieselDescription_en
                 ; AA2_$frCc1987_ESIREPE       ; ESI[tronic] 2.0 ComponentRepair E    ; $packComponentRepairElectricDescription_en ; $packComponentRepairElectricDescription_en
                 ; AA2_$frCc1987_ESIDIAG       ; ESI[tronic] 2.0 Diagnostic           ; $packDiagnosticDescription_en              ; $packDiagnosticDescription_en
                 ; AA2_$frCc1987_ESIMASTER     ; ESI[tronic] 2.0 Master               ; $packMasterDescription_en                  ; $packMasterDescription_en
                 ; AA2_$frCc1987_TRKOHW1       ; ESI[tronic] 2.0 Truck OHW I          ; $ohw1Description_en                        ; $ohw1Description_en
                 ; AA2_$frCc1987_TRKOHW2       ; ESI[tronic] 2.0 Truck OHW II         ; $ohw2Description_en                        ; $ohw2Description_en
                 ; AA2_$frCc1987_TRKTRUCK      ; ESI[tronic] 2.0 Truck                ; $packTruckDescription_en                   ; $packTruckDescription_en
                 ; AA2_$frCc1987_TSTINFOAW     ; ESI[tronic] W                        ; $infoartWDescription_en                    ; $infoartWDescription_en
                 ; AA2_$frCc1687_TSTINFODAT    ; Testdata VP-M/CP                     ; $infoartTestdataDescription_en             ; $infoartTestdataDescription_en
                 ; AA2_$frCc1987_TRKUPG        ; ESI[tronic] 2.0 Truck Upgrade        ; $truckUpgradeDescription_en                ; $truckUpgradeDescription_en
                 ; AA2_$frCc1987_KTS250SD      ; KTS 250 SD ECU Diagnosis             ; $kts250SDDescription_en                    ; $kts250SDDescription_en
                 ; AA2_$frCc1987_THLKTSTRK     ; Hotline KTS Truck                    ; $thlKtsTruckDescription_en                 ; $thlKtsTruckDescription_en
                 ; AA2_$frCc1987_THLKTS3H      ; Hotline KTS                          ; $thlKts3hDescription_en                    ; $thlKts3hDescription_en
                 ; AA2_$frCc1987_THLDIAG1H     ; HOTLINE DIAG FORFAIT 1h              ; $thlDiag1hDescription_en                   ; $thlDiag1hDescription_en
                 ; AA2_$frCc1987_THLDIAG3H     ; HOTLINE DIAG FORFAIT 3h              ; $thlDiag3hDescription_en                   ; $thlDiag3hDescription_en
                 ; AA2_$frCc1987_THLDIAG9H     ; HOTLINE DIAG FORFAIT 9h              ; $thlDiag9hDescription_en                   ; $thlDiag9hDescription_en
                 ; AA2_$frCc1987_THLDIAG17H    ; HOTLINE DIAG FORFAIT 17H             ; $thlDiag17hDescription_en                  ; $thlDiag17hDescription_en
                 ; AA2_$frCc1987_THLBCSDIAG9H  ; HOTLINE BCS DIAG FORFAIT 9h          ; $thlDiagBcs9hDescription_en                ; $thlDiagBcs9hDescription_en
                 ; AA2_$frCc1987_THLBCSDIAG16H ; HOTLINE BCS DIAG FORFAIT 16h         ; $thlDiagBcs16hDescription_en               ; $thlDiagBcs16hDescription_en
                 ; AA2_$frCc1987_DIAGISOTECHSD ; DiagISOTECH SD Steuergeraetediagnose ; $diagIsotechSDDescription_en               ; $diagIsotechSDDescription_en


INSERT_UPDATE ProductContainer; code[unique = true]          ; app(code, $catalogVersion); company(uid)[unique = true][default = $companyId];
                              ; pcaa_$frCc1987_ALLTRUCKS     ; AA2_$frCc1987_ALLTRUCKS
                              ; pcaa_$frCc1687_CSFSA500      ; AA2_$frCc1687_CSFSA500
                              ; pcaa_$frCc1687_CSFSA7XX      ; AA2_$frCc1687_CSFSA7XX
                              ; pcaa_$frCc1687_DCICRI        ; AA2_$frCc1687_DCICRI
                              ; pcaa_$frCc1687_DCICRIN       ; AA2_$frCc1687_DCICRIN
                              ; pcaa_$frCc1987_ESIADV        ; AA2_$frCc1987_ESIADV
                              ; pcaa_$frCc1987_ESIREPCAT     ; AA2_$frCc1987_ESIREPCAT
                              ; pcaa_$frCc1987_ESIREPD       ; AA2_$frCc1987_ESIREPD
                              ; pcaa_$frCc1987_ESIREPE       ; AA2_$frCc1987_ESIREPE
                              ; pcaa_$frCc1987_ESIDIAG       ; AA2_$frCc1987_ESIDIAG
                              ; pcaa_$frCc1987_ESIMASTER     ; AA2_$frCc1987_ESIMASTER
                              ; pcaa_$frCc1987_TRKOHW1       ; AA2_$frCc1987_TRKOHW1
                              ; pcaa_$frCc1987_TRKOHW2       ; AA2_$frCc1987_TRKOHW2
                              ; pcaa_$frCc1987_TRKTRUCK      ; AA2_$frCc1987_TRKTRUCK
                              ; pcaa_$frCc1987_TSTINFOAW     ; AA2_$frCc1987_TSTINFOAW
                              ; pcaa_$frCc1687_TSTINFODAT    ; AA2_$frCc1687_TSTINFODAT
                              ; pcaa_$frCc1987_TRKUPG        ; AA2_$frCc1987_TRKUPG
                              ; pcaa_$frCc1987_KTS250SD      ; AA2_$frCc1987_KTS250SD
                              ; pcaa_$frCc1987_THLKTSTRK     ; AA2_$frCc1987_THLKTSTRK
                              ; pcaa_$frCc1987_THLKTS3H      ; AA2_$frCc1987_THLKTS3H
                              ; pcaa_$frCc1987_THLDIAG1H     ; AA2_$frCc1987_THLDIAG1H
                              ; pcaa_$frCc1987_THLDIAG3H     ; AA2_$frCc1987_THLDIAG3H
                              ; pcaa_$frCc1987_THLDIAG9H     ; AA2_$frCc1987_THLDIAG9H
                              ; pcaa_$frCc1987_THLDIAG17H    ; AA2_$frCc1987_THLDIAG17H
                              ; pcaa_$frCc1987_THLBCSDIAG9H  ; AA2_$frCc1987_THLBCSDIAG9H
                              ; pcaa_$frCc1987_THLBCSDIAG16H ; AA2_$frCc1987_THLBCSDIAG16H
                              ; pcaa_$frCc1987_DIAGISOTECHSD ; AA2_$frCc1987_DIAGISOTECHSD

INSERT_UPDATE AppLicense; code[unique = true] ; $baseProduct                ; sellerProductId; brimName[lang = en]                          ; licenseType(code)[default = SUBSCRIPTION]; runtime(code)          ; bundleInfo(code); specifiedPrice; enabledCountries(isocode)[default = $enabledIn]; billingSystemStatus(code)[default = IN_SYNC]; unit(code)[default = pieces]; $catalogVersion[unique = true]; $approved; $taxGroup; availabilityStatus(code)[default = PUBLISHED]
                        ; AA2_$frCc1987P12760 ; AA2_$frCc1987_ALLTRUCKS     ; 1987P12760     ; $alltrucksSubsS1BrimName                     ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 2190.00       ;
                        ; AA2_$frCc1987P12949 ; AA2_$frCc1987_ALLTRUCKS     ; 1987P12949     ; $alltrucksSubsM3BrimName                     ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 2300.00       ;
                        ; AA2_$frCc1687P15063 ; AA2_$frCc1687_CSFSA500      ; 1687P15063     ; $compacFsa500Full3YBrimName                  ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 200.00        ;
                        ; AA2_$frCc1687P15060 ; AA2_$frCc1687_CSFSA500      ; 1687P15060     ; $compacFsa500SubsBrimName                    ;                                          ; runtime_subs_unlimited ; <ignore>        ; 100.00        ;
                        ; AA2_$frCc1687P15048 ; AA2_$frCc1687_CSFSA7XX      ; 1687P15048     ; $compacFsa7xxFull3YBrimName                  ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 630.00        ;
                        ; AA2_$frCc1687P15045 ; AA2_$frCc1687_CSFSA7XX      ; 1687P15045     ; $compacFsa7xxSubsBrimName                    ;                                          ; runtime_subs_unlimited ; <ignore>        ; 315.00        ;
                        ; AA2_$frCc1687P15093 ; AA2_$frCc1687_DCICRI        ; 1687P15093     ; $criFull3YBrimName                           ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 900.00        ;
                        ; AA2_$frCc1687P15090 ; AA2_$frCc1687_DCICRI        ; 1687P15090     ; $criSubsS1BrimName                           ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 300.00        ;
                        ; AA2_$frCc1687P15102 ; AA2_$frCc1687_DCICRI        ; 1687P15102     ; $criSubsM3BrimName                           ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 410.00        ;
                        ; AA2_$frCc1687P15103 ; AA2_$frCc1687_DCICRIN       ; 1687P15103     ; $crinFull3YBrimName                          ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 900.00        ;
                        ; AA2_$frCc1687P15100 ; AA2_$frCc1687_DCICRIN       ; 1687P15100     ; $crinSubsS1BrimName                          ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 300.00        ;
                        ; AA2_$frCc1687P15107 ; AA2_$frCc1687_DCICRIN       ; 1687P15107     ; $crinSubsM3BrimName                          ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 410.00        ;
                        ; AA2_$frCc1987P12843 ; AA2_$frCc1987_ESIADV        ; 1987P12843     ; $packAdvancedFull3YS1BrimName                ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 2890.00       ;
                        ; AA2_$frCc1987P12840 ; AA2_$frCc1987_ESIADV        ; 1987P12840     ; $packAdvancedSubsS1BrimName                  ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1445.00       ;
                        ; AA2_$frCc1987P12846 ; AA2_$frCc1987_ESIADV        ; 1987P12846     ; $packAdvancedFull3YM3BrimName                ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 3110.00       ;
                        ; AA2_$frCc1987P12847 ; AA2_$frCc1987_ESIADV        ; 1987P12847     ; $packAdvancedSubsM3BrimName                  ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1555.00       ;
                        ; AA2_$frCc1987P12998 ; AA2_$frCc1987_ESIREPCAT     ; 1987P12998     ; $packComponentCatSubsS1BrimName              ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 356.00        ;
                        ; AA2_$frCc1987P12783 ; AA2_$frCc1987_ESIREPCAT     ; 1987P12783     ; $packComponentCatFull3YM3BrimName            ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 872.00        ;
                        ; AA2_$frCc1987P12784 ; AA2_$frCc1987_ESIREPCAT     ; 1987P12784     ; $packComponentCatSubsM3BrimName              ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 436.00        ;
                        ; AA2_$frCc1987P12988 ; AA2_$frCc1987_ESIREPCAT     ; 1987P12988     ; $packComponentCatFull3YS1BrimName            ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 712           ;
                        ; AA2_$frCc1987P12973 ; AA2_$frCc1987_ESIREPD       ; 1987P12973     ; $packComponentRepairDieselFull3YS1BrimName   ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1442.00       ;
                        ; AA2_$frCc1987P12970 ; AA2_$frCc1987_ESIREPD       ; 1987P12970     ; $packComponentRepairDieselSubsS1BrimName     ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 721.00        ;
                        ; AA2_$frCc1987P12296 ; AA2_$frCc1987_ESIREPD       ; 1987P12296     ; $packComponentRepairDieselFull3YM3BrimName   ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1662.00       ;
                        ; AA2_$frCc1987P12297 ; AA2_$frCc1987_ESIREPD       ; 1987P12297     ; $packComponentRepairDieselSubsM3BrimName     ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 831.00        ;
                        ; AA2_$frCc1987P12993 ; AA2_$frCc1987_ESIREPE       ; 1987P12993     ; $packComponentRepairElectricFull3YS1BrimName ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 602.00        ;
                        ; AA2_$frCc1987P12990 ; AA2_$frCc1987_ESIREPE       ; 1987P12990     ; $packComponentRepairElectricSubsS1BrimName   ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 301.00        ;
                        ; AA2_$frCc1987P12294 ; AA2_$frCc1987_ESIREPE       ; 1987P12294     ; $packComponentRepairElectricFull3YM3BrimName ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 822.00        ;
                        ; AA2_$frCc1987P12295 ; AA2_$frCc1987_ESIREPE       ; 1987P12295     ; $packComponentRepairElectricSubsM3BrimName   ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 411.00        ;
                        ; AA2_$frCc1987P12823 ; AA2_$frCc1987_ESIDIAG       ; 1987P12823     ; $packDiagnosticFull3YS1BrimName              ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1490.00       ;
                        ; AA2_$frCc1987P12820 ; AA2_$frCc1987_ESIDIAG       ; 1987P12820     ; $packDiagnosticSubsS1BrimName                ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 745.00        ;
                        ; AA2_$frCc1987P12822 ; AA2_$frCc1987_ESIDIAG       ; 1987P12822     ; $packDiagnosticFull3YM3BrimName              ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1710.00       ;
                        ; AA2_$frCc1987P12824 ; AA2_$frCc1987_ESIDIAG       ; 1987P12824     ; $packDiagnosticSubsM3BrimName                ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 855.00        ;
                        ; AA2_$frCc1987P12913 ; AA2_$frCc1987_ESIMASTER     ; 1987P12913     ; $packMasterFull3YS1BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 4010.00       ;
                        ; AA2_$frCc1987P12910 ; AA2_$frCc1987_ESIMASTER     ; 1987P12910     ; $packMasterSubsS1BrimName                    ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 2005.00       ;
                        ; AA2_$frCc1987P12916 ; AA2_$frCc1987_ESIMASTER     ; 1987P12916     ; $packMasterFull3YS1BrimName                  ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 4230.00       ;
                        ; AA2_$frCc1987P12917 ; AA2_$frCc1987_ESIMASTER     ; 1987P12917     ; $packMasterSubsM3BrimName                    ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 2115.00       ;
                        ; AA2_$frCc1987P12263 ; AA2_$frCc1987_TRKOHW1       ; 1987P12263     ; $ohw1Full3YS1BrimName                        ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 1200.00       ;
                        ; AA2_$frCc1987P12260 ; AA2_$frCc1987_TRKOHW1       ; 1987P12260     ; $ohw1SubsS1BrimName                          ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 600.00        ;
                        ; AA2_$frCc1987P12262 ; AA2_$frCc1987_TRKOHW1       ; 1987P12262     ; $ohw1SubsM3BrimName                          ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 710.00        ;
                        ; AA2_$frCc1987P12265 ; AA2_$frCc1987_TRKOHW1       ; 1987P12265     ; $ohw1Full3YM3BrimName                        ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 1420.00       ;
                        ; AA2_$frCc1987P12280 ; AA2_$frCc1987_TRKOHW2       ; 1987P12280     ; $ohw2Full3YS1BrimName                        ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 2360.00       ;
                        ; AA2_$frCc1987P12278 ; AA2_$frCc1987_TRKOHW2       ; 1987P12278     ; $ohw2SubsS1BrimName                          ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1180.00       ;
                        ; AA2_$frCc1987P12275 ; AA2_$frCc1987_TRKOHW2       ; 1987P12275     ; $ohw2SubsM3BrimName                          ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1290.00       ;
                        ; AA2_$frCc1987P12276 ; AA2_$frCc1987_TRKOHW2       ; 1987P12276     ; $ohw2Full3YM3BrimName                        ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 2580.00       ;
                        ; AA2_$frCc1987P12402 ; AA2_$frCc1987_TRKTRUCK      ; 1987P12402     ; $packTruckFull3YS1BrimName                   ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 2750.00       ;
                        ; AA2_$frCc1987P12400 ; AA2_$frCc1987_TRKTRUCK      ; 1987P12400     ; $packTruckSubsS1BrimName                     ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1280.00       ;
                        ; AA2_$frCc1987P12936 ; AA2_$frCc1987_TRKTRUCK      ; 1987P12936     ; $packTruckSubsM3BrimName                     ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1390.00       ;
                        ; AA2_$frCc1987P12937 ; AA2_$frCc1987_TRKTRUCK      ; 1987P12937     ; $packTruckFull3YM3BrimName                   ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 2970.00       ;
                        ; AA2_$frCc1987P12503 ; AA2_$frCc1987_TSTINFOAW     ; 1987P12503     ; $infoartWFull3YBrimName                      ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 1060.00       ;
                        ; AA2_$frCc1987P12500 ; AA2_$frCc1987_TSTINFOAW     ; 1987P12500     ; $infoartWSubsBrimName                        ;                                          ; runtime_subs_unlimited ; <ignore>        ; 530.00        ;
                        ; AA2_$frCc1687P15015 ; AA2_$frCc1687_TSTINFODAT    ; 1687P15015     ; $infoartTestdataSubsBrimName                 ;                                          ; runtime_subs_unlimited ; <ignore>        ; 315.00        ;
                        ; AA2_$frCc1987P12404 ; AA2_$frCc1987_TRKUPG        ; 1987P12404     ; $truckUpgradeSubsS1BrimName                  ;                                          ; runtime_subs_unlimited ; BI_S_1          ; 1050.00       ;
                        ; AA2_$frCc1987P12140 ; AA2_$frCc1987_TRKUPG        ; 1987P12140     ; $truckUpgradeFull3YS1BrimName                ; "FULL"                                   ; runtime_full_3y        ; BI_S_1          ; 2100.00       ;
                        ; AA2_$frCc1987P12359 ; AA2_$frCc1987_TRKUPG        ; 1987P12359     ; $truckUpgradeSubsM3BrimName                  ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 1160.00       ;
                        ; AA2_$frCc1987P12364 ; AA2_$frCc1987_TRKUPG        ; 1987P12364     ; $truckUpgradeFull3YM3BrimName                ; "FULL"                                   ; runtime_full_3y        ; BI_M_3          ; 2320.00       ;
                        ; AA2_$frCc1987P12389 ; AA2_$frCc1987_KTS250SD      ; 1987P12389     ; $kts250SDFullBrimName                        ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 650.00        ;
                        ; AA2_$frCc1987P12385 ; AA2_$frCc1987_KTS250SD      ; 1987P12385     ; $kts250SDSubsBrimName                        ;                                          ; runtime_subs_unlimited ; <ignore>        ; 734.00        ;
                        ; AA2_$frCc1987P13821 ; AA2_$frCc1987_THLKTSTRK     ; 1987P13821     ; $thlKtsTruckSubsBrimName                     ;                                          ; runtime_subs_unlimited ; <ignore>        ; 95.00         ;
                        ; AA2_$frCc1987P13523 ; AA2_$frCc1987_THLKTSTRK     ; 1987P13523     ; $thlKtsTruckFull3YBrimName                   ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 285.00        ;
                        ; AA2_$frCc1987P13517 ; AA2_$frCc1987_THLKTS3H      ; 1987P13517     ; $thlKts3HSubsBrimName                        ;                                          ; runtime_subs_unlimited ; <ignore>        ; 145.00        ;
                        ; AA2_$frCc1987P13817 ; AA2_$frCc1987_THLKTS3H      ; 1987P13817     ; $thlKts3HFull3YBrimName                      ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 435.00        ;
                        ; AA2_$frCc1987P13813 ; AA2_$frCc1987_THLDIAG1H     ; 1987P13813     ; $thlDiag1HFullBrimName                       ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 87.00         ;
                        ; AA2_$frCc1987P13826 ; AA2_$frCc1987_THLDIAG3H     ; 1987P13826     ; $thlDiag3HFullBrimName                       ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 229.00        ;
                        ; AA2_$frCc1987P13812 ; AA2_$frCc1987_THLDIAG9H     ; 1987P13812     ; $thlDiag9HSubsBrimName                       ;                                          ; runtime_subs_unlimited ; <ignore>        ; 490.00        ;
                        ; AA2_$frCc1987P13810 ; AA2_$frCc1987_THLDIAG17H    ; 1987P13810     ; $thlDiag17HFullBrimName                      ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 850.00        ;
                        ; AA2_$frCc1987P13816 ; AA2_$frCc1987_THLBCSDIAG9H  ; 1987P13816     ; $thlBcsDiag9HFullBrimName                    ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 313.60        ;
                        ; AA2_$frCc1987P13814 ; AA2_$frCc1987_THLBCSDIAG16H ; 1987P13814     ; $thlBcsDiag16HFullBrimName                   ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 392.00        ;
                        ; AA2_$frCc1987P12224 ; AA2_$frCc1987_DIAGISOTECHSD ; 1987P12224     ; $diagIsoTechSDSubsBrimName                   ;                                          ; runtime_subs_unlimited ; <ignore>        ; 588.00        ;
                        ; AA2_$frCc1987P12229 ; AA2_$frCc1987_DIAGISOTECHSD ; 1987P12229     ; $diagIsoTechSDFullBrimName                   ; "FULL"                                   ; runtime_full_unlimited ; <ignore>        ; 0.80          ;
                        ; AA2_$frCc1687P15170 ; AA2_$frCc1687_CSFSA500      ; 1687P15170     ; $fsa5xxMulti                                 ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$frCc1687P15173 ; AA2_$frCc1687_CSFSA500      ; 1687P15173     ; $fsa5xx3YearsMulti                           ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;
                        ; AA2_$frCc1687P15160 ; AA2_$frCc1687_CSFSA7XX      ; 1687P15160     ; $fsa7xxMulti                                 ;                                          ; runtime_subs_unlimited ; BI_M_3          ; 0.01          ;
                        ; AA2_$frCc1687P15163 ; AA2_$frCc1687_CSFSA7XX      ; 1687P15163     ; $fsa7xx3YearsMulti                           ; "FULL"                                   ; runtime_full_3y        ; <ignore>        ; 0.01          ;

# The licenses below were discontinued by MA and should be available only for the contract migration.
UPDATE AppLicense; code[unique = true] ; $catalogVersion[unique = true]; availabilityStatus(code)[default = UNPUBLISHED]
                 ; AA2_$frCc1987P12389 ;

UPDATE AppLicense; code[unique = true] ; userGroups(uid)                                                                                                 ; $catalogVersion[unique = true]
                 ; AA2_$frCc1987P12760 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12949 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1687P15063 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1687P15060 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1687P15048 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1687P15045 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1687P15093 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1687P15090 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1687P15102 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1687P15103 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1687P15100 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1687P15107 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12843 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P12840 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P12846 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P12847 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P12998 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P12783 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12988 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12784 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P12973 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12970 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P12296 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12297 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P12993 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12990 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12294 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12295 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12823 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12820 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12822 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12824 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12913 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P12910 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P12916 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P12917 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P12263 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12260 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12262 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12265 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12280 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12278 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12275 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12276 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12402 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12400 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12936 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12937 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12503 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12500 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1687P15015 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12404 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12140 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12359 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12364 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12389 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P12385 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0010                      ;
                 ; AA2_$frCc1987P13821 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P13523 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P13517 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010               ;
                 ; AA2_$frCc1987P13817 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010               ;
                 ; AA2_$frCc1987P13813 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P13826 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P13812 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P13810 ; IDW000,BCS000,BDS000,BDC000,AT0000,WD0000,WD0001,WD0002,WD0003,WD0004,WD0005,AUT000,KA0002,KA0008,KA0009,KA0010 ;
                 ; AA2_$frCc1987P13816 ; WD0002,WD0004,WD0005                                                                                            ;
                 ; AA2_$frCc1987P13814 ; WD0002,WD0004,WD0005                                                                                            ;
                 ; AA2_$frCc1987P12224 ; WD0003,WD0004                                                                                                   ;
                 ; AA2_$frCc1987P12229 ; WD0003,WD0004                                                                                                   ;
                 ; AA2_$frCc1687P15163 ; IDW000                                                                                                          ;
                 ; AA2_$frCc1687P15170 ; IDW000                                                                                                          ;
                 ; AA2_$frCc1687P15160 ; IDW000                                                                                                          ;
                 ; AA2_$frCc1687P15173 ; IDW000                                                                                                          ;


INSERT_UPDATE PriceRow; product(code, $catalogVersion)[unique = true]; price; currency(isocode)[unique = true, default = EUR]; unit(code[unique = true, default = pieces]); minqtd[default = 1]; unitFactor[default = 1]; net[default = true]; ug[unique = true, default = '']
                      ; AA2_$frCc1987P12760                          ; 2190.00
                      ; AA2_$frCc1987P12949                          ; 2300.00
                      ; AA2_$frCc1687P15063                          ; 200.00
                      ; AA2_$frCc1687P15060                          ; 100.00
                      ; AA2_$frCc1687P15048                          ; 630.00
                      ; AA2_$frCc1687P15045                          ; 315.00
                      ; AA2_$frCc1687P15093                          ; 900.00
                      ; AA2_$frCc1687P15090                          ; 300.00
                      ; AA2_$frCc1687P15102                          ; 410.00
                      ; AA2_$frCc1687P15103                          ; 900.00
                      ; AA2_$frCc1687P15100                          ; 300.00
                      ; AA2_$frCc1687P15107                          ; 410.00
                      ; AA2_$frCc1987P12843                          ; 2890.00
                      ; AA2_$frCc1987P12840                          ; 1445.00
                      ; AA2_$frCc1987P12846                          ; 3110.00
                      ; AA2_$frCc1987P12847                          ; 1555.00
                      ; AA2_$frCc1987P12998                          ; 356.00
                      ; AA2_$frCc1987P12783                          ; 872.00
                      ; AA2_$frCc1987P12988                          ; 712.00
                      ; AA2_$frCc1987P12784                          ; 436.00
                      ; AA2_$frCc1987P12973                          ; 1442.00
                      ; AA2_$frCc1987P12970                          ; 721.00
                      ; AA2_$frCc1987P12296                          ; 1662.00
                      ; AA2_$frCc1987P12297                          ; 831.00
                      ; AA2_$frCc1987P12993                          ; 602.00
                      ; AA2_$frCc1987P12990                          ; 301.00
                      ; AA2_$frCc1987P12294                          ; 822.00
                      ; AA2_$frCc1987P12295                          ; 411.00
                      ; AA2_$frCc1987P12823                          ; 1490.00
                      ; AA2_$frCc1987P12820                          ; 745.00
                      ; AA2_$frCc1987P12822                          ; 1710.00
                      ; AA2_$frCc1987P12824                          ; 855.00
                      ; AA2_$frCc1987P12913                          ; 4010.00
                      ; AA2_$frCc1987P12910                          ; 2005.00
                      ; AA2_$frCc1987P12916                          ; 4230.00
                      ; AA2_$frCc1987P12917                          ; 2115.00
                      ; AA2_$frCc1987P12263                          ; 1200.00
                      ; AA2_$frCc1987P12260                          ; 600.00
                      ; AA2_$frCc1987P12262                          ; 710.00
                      ; AA2_$frCc1987P12265                          ; 1420.00
                      ; AA2_$frCc1987P12280                          ; 2360.00
                      ; AA2_$frCc1987P12278                          ; 1180.00
                      ; AA2_$frCc1987P12275                          ; 1290.00
                      ; AA2_$frCc1987P12276                          ; 2580.00
                      ; AA2_$frCc1987P12402                          ; 2750.00
                      ; AA2_$frCc1987P12400                          ; 1280.00
                      ; AA2_$frCc1987P12936                          ; 1390.00
                      ; AA2_$frCc1987P12937                          ; 2970.00
                      ; AA2_$frCc1987P12503                          ; 1060.00
                      ; AA2_$frCc1987P12500                          ; 530.00
                      ; AA2_$frCc1687P15015                          ; 315.00
                      ; AA2_$frCc1987P12404                          ; 1050.00
                      ; AA2_$frCc1987P12140                          ; 2100.00
                      ; AA2_$frCc1987P12359                          ; 1160.00
                      ; AA2_$frCc1987P12364                          ; 2320.00
                      ; AA2_$frCc1987P12389                          ; 650.00
                      ; AA2_$frCc1987P12385                          ; 734.00
                      ; AA2_$frCc1987P13821                          ; 95.00
                      ; AA2_$frCc1987P13523                          ; 285.00
                      ; AA2_$frCc1987P13517                          ; 145.00
                      ; AA2_$frCc1987P13817                          ; 435.00
                      ; AA2_$frCc1987P13813                          ; 87.00
                      ; AA2_$frCc1987P13826                          ; 229.00
                      ; AA2_$frCc1987P13812                          ; 490.00
                      ; AA2_$frCc1987P13810                          ; 850.00
                      ; AA2_$frCc1987P13816                          ; 313.60
                      ; AA2_$frCc1987P13814                          ; 392.00
                      ; AA2_$frCc1987P12224                          ; 588.00
                      ; AA2_$frCc1987P12229                          ; 0.80
                      ; AA2_$frCc1687P15163                          ; 0.01 ;
                      ; AA2_$frCc1687P15170                          ; 0.01 ;
                      ; AA2_$frCc1687P15160                          ; 0.01 ;
                      ; AA2_$frCc1687P15173                          ; 0.01 ;

INSERT_UPDATE App; code[unique = true]     ; boms(code); $catalogVersion[unique = true]
                 ; AA2_$frCc1987_ALLTRUCKS ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$frCc1987_ESIADV    ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe
                 ; AA2_$frCc1987_ESIREPCAT ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE
                 ; AA2_$frCc1987_ESIREPD   ; MAT_$aaCcA,MAT_$aaCcD,MAT_$aaCcE,MAT_$aaCcK3
                 ; AA2_$frCc1987_ESIREPE   ; MAT_$aaCcA,MAT_$aaCcE,MAT_$aaCcK2
                 ; AA2_$frCc1987_ESIDIAG   ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB
                 ; AA2_$frCc1987_ESIMASTER ; MAT_$aaCcA,MAT_$aaCcSDSDA,MAT_$aaCcEBR,MAT_$aaCcTSB,MAT_$aaCcSIS,MAT_$aaCcCoRe,MAT_$aaCcM,MAT_$aaCcP
                 ; AA2_$frCc1987_TRKOHW1   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$frCc1987_TRKOHW2   ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$frCc1987_TRKTRUCK  ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK
                 ; AA2_$frCc1987_TRKUPG    ; MAT_$aaCcTrFZ,MAT_$aaCcTrD,MAT_$aaCcTrSp,MAT_$aaCcTrWt,MAT_$aaCcTrTD,MAT_$aaCcTrETK

INSERT_UPDATE App; code[unique = true]         ; contentModules(code); $catalogVersion[unique = true];
                 ; AA2_$frCc1987_ALLTRUCKS     ; CM_$aaCcNEOOrangeATruck, CM_$aaCcETruck
                 ; AA2_$frCc1687_CSFSA500      ; CM_$aaCcCSFSA5
                 ; AA2_$frCc1687_CSFSA7XX      ; CM_$aaCcCSS,CM_$aaCcCSK
                 ; AA2_$frCc1687_DCICRI        ; CM_$aaCcDCICRI
                 ; AA2_$frCc1687_DCICRIN       ; CM_$aaCcDCICRIN
                 ; AA2_$frCc1987_ESIADV        ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe
                 ; AA2_$frCc1987_ESIREPCAT     ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE
                 ; AA2_$frCc1987_ESIREPD       ; CM_$aaCcA,CM_$aaCcD,CM_$aaCcE,CM_$aaCcK3
                 ; AA2_$frCc1987_ESIREPE       ; CM_$aaCcA,CM_$aaCcE,CM_$aaCcK2
                 ; AA2_$frCc1987_ESIDIAG       ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB
                 ; AA2_$frCc1987_ESIMASTER     ; CM_$aaCcA,CM_$aaCcSDSDA,CM_$aaCcEBR,CM_$aaCcTSB,CM_$aaCcSIS,CM_$aaCcCoRe,CM_$aaCcM,CM_$aaCcP
                 ; AA2_$frCc1987_TRKOHW1       ; CM_$aaCcETOHW1
                 ; AA2_$frCc1987_TRKOHW2       ; CM_$aaCcETOHW2
                 ; AA2_$frCc1987_TRKTRUCK      ; CM_$aaCcETruck
                 ; AA2_$frCc1987_TSTINFOAW     ; CM_$aaCcEW
                 ; AA2_$frCc1687_TSTINFODAT    ; CM_$aaCcTVPMCP
                 ; AA2_$frCc1987_TRKUPG        ; CM_$aaCcTRKUPG
                 ; AA2_$frCc1987_KTS250SD      ; CM_$aaCcKTS250ECUD
                 ; AA2_$frCc1987_THLKTSTRK     ; CM_$aaCcTHLKTSTRK
                 ; AA2_$frCc1987_THLKTS3H      ; CM_$aaCcTHLKTS3H
                 ; AA2_$frCc1987_THLDIAG1H     ; CM_$aaCcTHLDIAG1H
                 ; AA2_$frCc1987_THLDIAG3H     ; CM_$aaCcTHLDIAG3H
                 ; AA2_$frCc1987_THLDIAG9H     ; CM_$aaCcTHLDIAG9H
                 ; AA2_$frCc1987_THLDIAG17H    ; CM_$aaCcTHLDIAG17H
                 ; AA2_$frCc1987_THLBCSDIAG9H  ; CM_$aaCcTHLBCSDIAG9H
                 ; AA2_$frCc1987_THLBCSDIAG16H ; CM_$aaCcTHLBCSDIAG16H
                 ; AA2_$frCc1987_DIAGISOTECHSD ; CM_$aaCcDIAGISOTECHSD


INSERT_UPDATE App; code[unique = true]         ; $supercategories; $catalogVersion[unique = true];
                 ; AA2_$frCc1987_ALLTRUCKS     ; cat_1010201
                 ; AA2_$frCc1687_CSFSA500      ; cat_201
                 ; AA2_$frCc1687_CSFSA7XX      ; cat_201
                 ; AA2_$frCc1687_DCICRI        ; cat_401
                 ; AA2_$frCc1687_DCICRIN       ; cat_401
                 ; AA2_$frCc1987_ESIADV        ; cat_10101
                 ; AA2_$frCc1987_ESIREPCAT     ; cat_1010101
                 ; AA2_$frCc1987_ESIREPD       ; cat_1010101
                 ; AA2_$frCc1987_ESIREPE       ; cat_1010101
                 ; AA2_$frCc1987_ESIDIAG       ; cat_10101
                 ; AA2_$frCc1987_ESIMASTER     ; cat_10101
                 ; AA2_$frCc1987_TRKOHW1       ; cat_10102
                 ; AA2_$frCc1987_TRKOHW2       ; cat_10102
                 ; AA2_$frCc1987_TRKTRUCK      ; cat_10102
                 ; AA2_$frCc1987_TSTINFOAW     ; cat_40101
                 ; AA2_$frCc1687_TSTINFODAT    ; cat_40101
                 ; AA2_$frCc1987_TRKUPG        ; cat_10102
                 ; AA2_$frCc1987_KTS250SD      ; cat_1010104
                 ; AA2_$frCc1987_THLKTSTRK     ; cat_1010202
                 ; AA2_$frCc1987_THLKTS3H      ; cat_1010102
                 ; AA2_$frCc1987_THLDIAG1H     ; cat_1010102
                 ; AA2_$frCc1987_THLDIAG3H     ; cat_1010102
                 ; AA2_$frCc1987_THLDIAG9H     ; cat_1010102
                 ; AA2_$frCc1987_THLDIAG17H    ; cat_1010102
                 ; AA2_$frCc1987_THLBCSDIAG9H  ; cat_1010102
                 ; AA2_$frCc1987_THLBCSDIAG16H ; cat_1010102
                 ; AA2_$frCc1987_DIAGISOTECHSD ; cat_1010105

INSERT_UPDATE App; code[unique = true]         ; eulaContainers(code); $catalogVersion[unique = true]
                 ; AA2_$frCc1687_DCICRI        ; AA2_DCICRI
                 ; AA2_$frCc1687_DCICRIN       ; AA2_DCICRIN
                 ; AA2_$frCc1687_TSTINFODAT    ; AA2_ESItronic
                 ; AA2_$frCc1987_TSTINFOAW     ; AA2_ESItronic
                 ; AA2_$frCc1687_CSFSA500      ; AA2_FSA
                 ; AA2_$frCc1687_CSFSA7XX      ; AA2_FSA
                 ; AA2_$frCc1987_KTS250SD      ; AA2_ESItronic
                 ; AA2_$frCc1987_THLKTSTRK     ; AA2_THL
                 ; AA2_$frCc1987_THLKTS3H      ; AA2_THL
                 ; AA2_$frCc1987_THLDIAG1H     ; AA2_THL
                 ; AA2_$frCc1987_THLDIAG3H     ; AA2_THL
                 ; AA2_$frCc1987_THLDIAG9H     ; AA2_THL
                 ; AA2_$frCc1987_THLDIAG17H    ; AA2_THL
                 ; AA2_$frCc1987_THLBCSDIAG9H  ; AA2_THL
                 ; AA2_$frCc1987_THLBCSDIAG16H ; AA2_THL
                 ; AA2_$frCc1987_DIAGISOTECHSD ; AA2_ESItronic
