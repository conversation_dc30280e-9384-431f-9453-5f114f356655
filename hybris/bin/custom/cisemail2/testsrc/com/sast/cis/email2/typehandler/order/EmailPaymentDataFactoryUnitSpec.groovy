package com.sast.cis.email2.typehandler.order

import com.sast.cis.core.enums.PaymentMethodType
import com.sast.cis.email2.dto.aa.AaEmailPaymentData
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.testframework.JUnitPlatformSpecification
import generated.com.sast.cis.core.model.SepaCreditTransferPaymentInfoBuilder
import generated.com.sast.cis.core.model.SepaMandatePaymentInfoBuilder
import org.junit.Test

@UnitTest
class EmailPaymentDataFactoryUnitSpec extends JUnitPlatformSpecification {
    @Test
    void 'given SepaMandatePaymentInfo returns type SEPA_DIRECTDEBIT with truncated IBAN as identifier'() {
        when:
        def actualData = EmailPaymentDataFactory
                .forPaymentInfo(SepaMandatePaymentInfoBuilder.generate().withIBAN("************************").buildInstance())

        then:
        actualData == new AaEmailPaymentData(PaymentMethodType.SEPA_DIRECTDEBIT, '····4876')
    }

    @Test
    void 'given SepaCreditTransferPaymentInfo returns type SEPA_CREDIT with null identifier'() {
        when:
        def actualData = EmailPaymentDataFactory
                .forPaymentInfo(SepaCreditTransferPaymentInfoBuilder.generate().buildInstance())

        then:
        actualData == new AaEmailPaymentData(PaymentMethodType.SEPA_CREDIT, null)
    }
}
