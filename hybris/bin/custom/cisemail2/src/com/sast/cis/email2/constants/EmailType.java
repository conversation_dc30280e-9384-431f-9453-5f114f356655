package com.sast.cis.email2.constants;

// We should use the template IDs from the Mailjet
public enum EmailType {

    ORDER_SUCCESS("order_success"),
    ORDER_SUCCESS_EVAL_ONLY("order_success_eval_only"),
    ORDER_SUCCESS_DELAYED_ACTIVATION("order_success_delayed_activation"),
    INVOICE_DOWNLOAD("invoice_download"),
    SEPA_CREDIT_INVOICE_READY("sepa_credit_invoice_ready"),
    FIRST_PAYMENT_REMINDER("first_payment_reminder"),
    SECOND_PAYMENT_REMINDER("second_payment_reminder"),
    THIRD_PAYMENT_REMINDER("third_payment_reminder"),
    APP_APPROVED("app_approved"),
    APP_REJECTED("app_rejected"),
    APP_VERSION_APPROVED("app_version_approved"),
    APP_VERSION_REJECTED("app_version_rejected"),
    APP_VERSION_APK_SCAN_FAILED("app_version_apk_scan_failed"),
    APP_VERSION_ECCN_CHECK_FAILED("app_version_eccn_check_failed"),
    MANUAL_APPROVAL_WORKFLOW_INITIATED("manual_approval_workflow_initiated"),
    INITIATE_DPG_SELLER_ONBOARDING("initiate_dpg_seller_onboarding"),
    PSP_ACTIVATION_EMAIL("psp_activation_email"),
    SELLER_ONBOARDING_USER_EMAIL("seller_onboarding_user_email"),
    ORDER_SUCCESS_NOTIFY_DEVELOPER("order_success_notify_developer"),
    ORDER_SUCCESS_NOTIFY_CUSTOMER_SUPPORT("order_success_notify_customer_support"),
    PRIVATE_OFFER_REQUEST_NOTIFY_DEVELOPER("private_offer_request_notify_developer"),
    PRIVATE_OFFER_REQUEST_NOTIFY_INTEGRATOR("private_offer_request_notify_integrator"),
    PRIVATE_OFFER_REQUEST_NOTIFY_INTERNAL_TEAM("private_offer_request_notify_internal_team"),
    NEW_APP_VERSION_NOTIFY_FOLLOWERS("new_app_version_notify_followers"),
    APP_AVAILABILITY_UPDATED("app_availability_updated"),
    TRIAL_EXTENSION_REQUEST_RECEIVED("trial_extension_request_received"),
    SUCCESSFUL_DB_ONBOARDING("successful_db_onboarding"),
    SUBSCRIPTION_PRICE_UPDATED("subscription_price_updated"),
    SUCCESSFUL_PAYMENT("successful_payment"),
    FAILED_PAYMENT("failed_payment"),
    AA_SUBSCRIPTION_REPORT("aa_subscription_report"),
    CREDIT_NOTE("credit_note"),
    PAYMENT_METHOD_AVAILABILITY_REPORT("payment_method_availability_report"),
    SEPA_DD_PAYMENT_METHOD_CREATION_REMINDER("sepa_dd_method_creation_reminder");


    private final String templateName;

    EmailType(String templateName) {
        this.templateName = templateName;
    }

    public String getTemplate() {
        return templateName;
    }
}
