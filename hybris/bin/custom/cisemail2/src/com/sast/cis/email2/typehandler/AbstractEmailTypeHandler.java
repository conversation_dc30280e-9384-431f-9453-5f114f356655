package com.sast.cis.email2.typehandler;

import com.sast.cis.core.config.WebsiteUrlProvider;
import com.sast.cis.core.enums.Feature;
import com.sast.cis.core.enums.StoreEnum;
import com.sast.cis.core.service.FeatureToggleService;
import com.sast.cis.email2.constants.EmailType;
import com.sast.email.client.dto.EmailDto;
import com.sast.email.client.dto.TenantDto;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.NoSuchElementException;

import static com.sast.cis.email2.constants.Cisemail2Constants.*;

public abstract class AbstractEmailTypeHandler implements EmailTypeHandler {

    private final static String AZENA_TEMPLATE_PREFIX = "store-azena/";
    private final static String AA_TEMPLATE_PREFIX = "store-aa/";

    protected final ConfigurationService configurationService;
    private final FeatureToggleService featureToggleService;

    protected AbstractEmailTypeHandler(
        ConfigurationService configurationService,
        FeatureToggleService featureToggleService) {
        this.configurationService = configurationService;
        this.featureToggleService = featureToggleService;
    }

    public EmailDto getEmailDto(EmailType emailType, TenantDto tenant, String locale) {
        return new EmailDto()
                .locale(locale)
                .tenant(tenant)
                .from(getFrom(tenant))
                .templateName(getTemplate(emailType, tenant));
    }

    public TenantDto getTenantForStoreUid(String storeUid) {
        if (StoreEnum.IOTSTORE.getCode().equals(storeUid)) {
            return TenantDto.AZENA;
        } else if (StoreEnum.AASTORE.getCode().equals(storeUid)) {
            return TenantDto.BAAM;
        } else {
            throw new NoSuchElementException("Couldn't find tenant for StoreUid = " + storeUid);
        }
    }

    // Mailjet crumbles to ash in the presence of a null, but can handle empty strings,
    // so an empty string in serialized json is a safe choice
    public Object checkForNull(Object property) {
        if (property == null) {
            return StringUtils.EMPTY;
        }
        return property;
    }

    // REMOVE after the email communicator is removed from the code.
    private String getTemplate(EmailType emailType, TenantDto tenant) {
        if (featureToggleService.isEnabled(Feature.FEATURE_EMAIL_CLIENT_ENABLED)) {
            return getTemplateName(emailType, tenant);
        }
        return getTemplateId(emailType, tenant);
    }

    private String getTemplateName(EmailType emailType, TenantDto tenantDto) {
        String prefix = tenantDto.equals(TenantDto.AZENA) ? AZENA_TEMPLATE_PREFIX : AA_TEMPLATE_PREFIX;
        return prefix + emailType.getTemplate();
    }

    private String getTemplateId(EmailType emailType, TenantDto tenant) {
        String templateName = null;
        if (TenantDto.AZENA == tenant) {
            templateName = AZENA_TEMPLATE_PROPERTY_PREFIX + emailType.getTemplate();
        } else if (TenantDto.BAAM == tenant) {
            templateName = AA_TEMPLATE_PROPERTY_PREFIX + emailType.getTemplate();
        }

        if (StringUtils.isEmpty(templateName)) {
            throw new NoSuchElementException("Couldn't find the matching template name");
        }

        return configurationService.getConfiguration().getString(templateName);
    }

    private String getFrom(TenantDto tenant) {
        if (TenantDto.AZENA == tenant) {
            return configurationService.getConfiguration().getString(AZENA_EMAIL_SENDER_PROPERTY);
        }
        return configurationService.getConfiguration().getString(AA_EMAIL_SENDER_PROPERTY);
    }

    protected Map<String, Object> asPropertyMap(final Object templateData) {
        final Map<String, Object> propMap = new HashMap<>();
        Arrays.stream(templateData.getClass().getDeclaredFields()).forEach(field -> {
            field.setAccessible(true);
            try {
                if (field.get(templateData) != null) {
                    propMap.put(field.getName(), field.get(templateData));
                }
            } catch (final IllegalAccessException e) {
                throw new IllegalStateException("Cannot convert template data.", e);
            }
        });
        return propMap;
    }

}
