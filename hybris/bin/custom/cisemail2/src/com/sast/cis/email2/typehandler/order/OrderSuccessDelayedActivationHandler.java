package com.sast.cis.email2.typehandler.order;

import com.sast.cis.core.service.FeatureToggleService;
import com.sast.cis.email2.constants.EmailType;
import com.sast.cis.email2.dto.aa.AaOrderSuccessData;
import com.sast.cis.email2.typehandler.AbstractEmailTypeHandler;
import com.sast.email.client.dto.EmailDto;
import com.sast.email.client.dto.TenantDto;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import org.springframework.stereotype.Service;

@Service
public class OrderSuccessDelayedActivationHandler extends AbstractEmailTypeHandler {
    protected OrderSuccessDelayedActivationHandler(final ConfigurationService configurationService,
                                                   final FeatureToggleService featureToggleService) {
        super(configurationService, featureToggleService);
    }

    @Override
    public EmailType getType() {
        return EmailType.ORDER_SUCCESS_DELAYED_ACTIVATION;
    }

    @Override
    public EmailDto getEmailDto(final Object templateData) {
        if (templateData instanceof final AaOrderSuccessData aaOrderSuccessData) {
            return super.getEmailDto(getType(), TenantDto.BAAM, aaOrderSuccessData.emailLanguage())
                    .properties(asPropertyMap(aaOrderSuccessData));
        } else {
            throw new IllegalArgumentException("Template data is not of type AaOrderSuccessData: " + templateData);
        }
    }
}
