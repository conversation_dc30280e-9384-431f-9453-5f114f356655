package com.sast.cis.shop.frontend.controllers.rest;

import com.google.gson.Gson;
import com.sast.cis.core.category.data.CategoryHierarchyDTO;
import com.sast.cis.core.category.data.PriceDTO;
import com.sast.cis.core.category.data.ProductDTO;
import com.sast.cis.core.category.facade.CategoryFacade;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.store.BaseStoreModel;
import de.hybris.platform.store.services.BaseStoreService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CategoryResourceUnitTest {

    private static final String CATEGORY_CODE = "cat_123";
    private static final String API_CATEGORIES = "/api/categories/" + CATEGORY_CODE;
    private static final Gson gson = new Gson();
    @Mock
    private CategoryFacade categoryFacade;
    @Mock
    private BaseStoreService baseStoreFacade;
    @InjectMocks
    private CategoryResource categoryResource;

    private MockMvc mockMvc;
    @Mock
    private BaseStoreModel baseStore;
    private CategoryHierarchyDTO categoryHierarchyDto;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(categoryResource).build();
        List<ProductDTO> products = List.of();
        categoryHierarchyDto = getCategoryHierarchy(products);
        ProductDTO productDto = getSampleProduct();
        CategoryHierarchyDTO subCategoryHierarchy = getCategoryHierarchy(List.of(productDto));
        categoryHierarchyDto.setSubcategories(List.of(subCategoryHierarchy));
        when(baseStoreFacade.getCurrentBaseStore()).thenReturn(baseStore);
        when(categoryFacade.getCategoryDataForCode(baseStore, CATEGORY_CODE)).thenReturn(categoryHierarchyDto);
    }

    @Test
    public void givenExistingCategories_whenGetCategoryHierarchy_thenReturnOkWithResult() throws Exception {

        final MvcResult mvcResult = mockMvc.perform(get(API_CATEGORIES)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(status().isOk())
            .andReturn();

        final CategoryHierarchyDTO response = gson.fromJson(mvcResult.getResponse().getContentAsString(), CategoryHierarchyDTO.class);
        assertThat(response).usingRecursiveComparison().isEqualTo(categoryHierarchyDto);
    }

    private CategoryHierarchyDTO getCategoryHierarchy(List<ProductDTO> products) {
        CategoryHierarchyDTO categoryHierarchyDto = new CategoryHierarchyDTO();
        categoryHierarchyDto.setCode(CATEGORY_CODE);
        categoryHierarchyDto.setName("main");
        categoryHierarchyDto.setProducts(products);
        return categoryHierarchyDto;
    }

    private ProductDTO getSampleProduct() {
        ProductDTO productDto = new ProductDTO();
        productDto.setName("name");
        productDto.setCode("AA_1234");
        productDto.setMinPrice(new PriceDTO());
        return productDto;
    }

}
