package com.sast.cis.shop.frontend.controllers.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sast.cis.shop.frontend.handlers.HttpExceptionHandler;
import com.sast.cis.shop.frontend.messages.UserMessageFactory;
import com.sast.cis.shop.frontend.service.SepaMandateService;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class SepaMandateResourceTest {

    @Mock
    private SepaMandateService sepaMandateService;

    @Mock
    private UserMessageFactory userMessageFactory;

    @InjectMocks
    private SepaMandateResource sepaMandateResource;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    private static final String BASE_URL = "/sepa-mandates";
    private static final String TEST_COMPANY_ID = "test-company-123";
    private static final String TEST_REFERENCE = "test-reference-456";

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(sepaMandateResource)
                .setControllerAdvice(new HttpExceptionHandler(userMessageFactory))
                .build();
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testInitializeSepaMandateDraft_Success() throws Exception {
        // Given
        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("mandateReference", "MANDATE-123");
        expectedResponse.put("status", "DRAFT");

        ResponseEntity<Map<String, Object>> serviceResponse = new ResponseEntity<>(expectedResponse, HttpStatus.OK);
        when(sepaMandateService.initializeSepaMandateDraft(TEST_COMPANY_ID)).thenReturn(serviceResponse);

        // When & Then
        mockMvc.perform(post(BASE_URL + "/initialize")
                        .param("companyId", TEST_COMPANY_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.mandateReference").value("MANDATE-123"))
                .andExpect(jsonPath("$.status").value("DRAFT"));
    }

    @Test
    public void testUpdateOrFinalizeSepaMandateByReference_Success() throws Exception {
        // Given
        Map<String, Object> payload = new HashMap<>();
        payload.put("accountHolderName", "John Doe");
        payload.put("iban", "**********************");
        payload.put("useAsDefault", true);

        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("mandateReference", TEST_REFERENCE);
        expectedResponse.put("status", "ACTIVE");

        ResponseEntity<Map<String, Object>> serviceResponse = new ResponseEntity<>(expectedResponse, HttpStatus.OK);
        when(sepaMandateService.updateOrFinalizeSepaMandateByReference(eq(TEST_REFERENCE), any(Map.class)))
                .thenReturn(serviceResponse);

        // When & Then
        mockMvc.perform(post(BASE_URL + "/" + TEST_REFERENCE + "/activate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(payload)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.mandateReference").value(TEST_REFERENCE))
                .andExpect(jsonPath("$.status").value("ACTIVE"));
    }

    @Test
    public void testInitializeSepaMandateDraft_MissingCompanyId() throws Exception {
        // When & Then
        mockMvc.perform(post(BASE_URL + "/initialize")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void testUpdateOrFinalizeSepaMandateByReference_EmptyPayload() throws Exception {
        // When & Then
        mockMvc.perform(post(BASE_URL + "/" + TEST_REFERENCE + "/activate")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andExpect(status().isOk()); // Should still work with empty payload
    }

    @Test
    public void testInitializeSepaMandateDraft_ServiceException() throws Exception {
        // Given
        when(sepaMandateService.initializeSepaMandateDraft(TEST_COMPANY_ID))
                .thenThrow(new RuntimeException("Service unavailable"));

        // When & Then
        mockMvc.perform(post(BASE_URL + "/initialize")
                        .param("companyId", TEST_COMPANY_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isInternalServerError());
    }
}
