package com.sast.cis.shop.frontend.controllers.rest;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sast.cis.companyprofile.data.AboutData;
import com.sast.cis.companyprofile.data.CompanyProfileData;
import com.sast.cis.companyprofile.enums.CompanyProfileStatus;
import com.sast.cis.companyprofile.facade.StoreCompanyProfileFacade;
import com.sast.cis.core.constants.shop.ShopErrorCode;
import com.sast.cis.core.data.IotCompanyData;
import com.sast.cis.core.data.UserMessage;
import com.sast.cis.core.exceptions.web.NotFoundException;
import com.sast.cis.shop.frontend.messages.UserMessageFactory;
import com.sast.cis.shop.frontend.handlers.HttpExceptionHandler;
import de.hybris.bootstrap.annotations.UnitTest;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultMatcher;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class AppCompanyProfileResourceUnitTest {

    public static final String APP_COMPANY_PROFILE_URI = "/api/app/343343/company-profile";
    private static final String COMPANY_UID = "12345";
    private MockMvc mockMvc;

    @Mock
    private StoreCompanyProfileFacade storeCompanyProfileFacade;

    @InjectMocks
    private AppCompanyProfileResource appCompanyProfileResource;

    @Mock
    private UserMessageFactory userMessageFactory;

    private ObjectMapper objectMapper;

    @Before
    public void setUp() {
        this.objectMapper = buildObjectMapper();
        this.mockMvc = MockMvcBuilders
            .standaloneSetup(appCompanyProfileResource)
            .setControllerAdvice(new HttpExceptionHandler(userMessageFactory))
            .setMessageConverters(new MappingJackson2HttpMessageConverter(this.objectMapper))
            .build();

        CompanyProfileData companyProfileData = buildCompanyProfileData(CompanyProfileStatus.DRAFT);
        when(storeCompanyProfileFacade.getSellerCompanyProfile(any())).thenReturn(Optional.of(companyProfileData));
        when(userMessageFactory.error(ShopErrorCode.GENERIC_NOT_FOUND)).thenReturn(new UserMessage().withCode("lala"));
    }

    @Test
    public void getCompanyProfile_productDoesNotExists_givesResourceNotFoundHttpStatus() {
        doThrow(new NotFoundException()).when(storeCompanyProfileFacade).getSellerCompanyProfile(any());
        var actualResponse = get(APP_COMPANY_PROFILE_URI, status().isNotFound());
        assertThat(actualResponse).isNotEmpty();
    }

    @Test
    public void getCompanyProfile_productExistsAndAppCompanyHasNoCompanyProfile_200StatusWithEmptyCompanyProfile() {
        CompanyProfileData expectedCompanyData = new CompanyProfileData();
        when(storeCompanyProfileFacade.getSellerCompanyProfile(any())).thenReturn(Optional.empty());
        var actualResponse = get(APP_COMPANY_PROFILE_URI, status().isOk(), CompanyProfileData.class);
        assertThat(actualResponse).usingRecursiveComparison().isEqualTo(expectedCompanyData);
    }

    @Test
    public void getCompanyProfile_appsCompanyHasCompanyProfileWithPublishedStatus_200StatusWithCompanyProfile() {
        CompanyProfileData publishedCompanyProfileData = buildCompanyProfileData(CompanyProfileStatus.PUBLISHED);
        when(storeCompanyProfileFacade.getSellerCompanyProfile(any())).thenReturn(Optional.ofNullable(publishedCompanyProfileData));

        var actualResponse = get(APP_COMPANY_PROFILE_URI, status().isOk(), CompanyProfileData.class);
        assertThat(actualResponse).usingRecursiveComparison().isEqualTo(publishedCompanyProfileData);
    }

    private CompanyProfileData buildCompanyProfileData(CompanyProfileStatus companyProfileStatus) {
        return new CompanyProfileData()
            .withCompanyData(new IotCompanyData()
                .withCompanyUid(COMPANY_UID).withName("DUMMY COMPANY"))
            .withStatus(companyProfileStatus.getCode())
            .withAboutData(new AboutData().withDescription("DESCRIPTION"));
    }

    @SneakyThrows
    private String get(String path, ResultMatcher status) {
        return mockMvc.perform(MockMvcRequestBuilders.get(path)
            .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status)
            .andReturn().getResponse().getContentAsString();
    }

    @SneakyThrows
    private <T> T get(String path, ResultMatcher status, Class<T> clazz) {
        return toObject(get(path, status), clazz);
    }

    private <T> T toObject(String content, Class<T> clazz) throws JsonProcessingException {
        return objectMapper.readValue(content, clazz);
    }

    private ObjectMapper buildObjectMapper() {
        var objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);
        return objectMapper;
    }
}
