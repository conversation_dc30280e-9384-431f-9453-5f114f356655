package com.sast.cis.shop.frontend.service.authentication;

import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;
import java.net.URI;

import static org.mockito.Mockito.*;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class SepaMandateAuthenticationFilterTest {

    @Mock
    private SepaMandateAccessTokenProvider accessTokenProvider;

    @Mock
    private HttpRequest request;

    @Mock
    private ClientHttpRequestExecution execution;

    @Mock
    private ClientHttpResponse response;

    @InjectMocks
    private SepaMandateAuthenticationFilter authenticationFilter;

    private static final String TEST_JWT_TOKEN = "test-jwt-token";
    private static final byte[] EMPTY_BODY = new byte[0];

    @Test
    public void testFilter_WithValidToken_AddsAuthorizationHeader() {
        // Given
        HttpHeaders headers = new HttpHeaders();
        when(request.getHeaders()).thenReturn(headers);
        when(request.getMethod()).thenReturn(HttpMethod.POST);
        when(request.getURI()).thenReturn(URI.create("http://localhost:8081/api/v1/mandates"));
        when(accessTokenProvider.getAccessToken()).thenReturn(TEST_JWT_TOKEN);

        // When
        authenticationFilter.filter(request);

        // Then
        verify(accessTokenProvider).getAccessToken();
        // Verify that the Bearer token was added to headers
        assert headers.getFirst(HttpHeaders.AUTHORIZATION).equals("Bearer " + TEST_JWT_TOKEN);
    }

    @Test
    public void testFilter_WithNullToken_DoesNotAddAuthorizationHeader() {
        // Given
        HttpHeaders headers = new HttpHeaders();
        when(request.getHeaders()).thenReturn(headers);
        when(request.getMethod()).thenReturn(HttpMethod.POST);
        when(request.getURI()).thenReturn(URI.create("http://localhost:8081/api/v1/mandates"));
        when(accessTokenProvider.getAccessToken()).thenReturn(null);

        // When
        authenticationFilter.filter(request);

        // Then
        verify(accessTokenProvider).getAccessToken();
        // Verify that no authorization header was added
        assert !headers.containsKey(HttpHeaders.AUTHORIZATION);
    }

    @Test
    public void testFilter_WithExistingAuthorizationHeader_DoesNotOverride() {
        // Given
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.AUTHORIZATION, "Bearer existing-token");
        when(request.getHeaders()).thenReturn(headers);

        // When
        authenticationFilter.filter(request);

        // Then
        verify(accessTokenProvider, never()).getAccessToken();
        // Verify that the existing authorization header was not changed
        assert headers.getFirst(HttpHeaders.AUTHORIZATION).equals("Bearer existing-token");
    }

    @Test
    public void testIntercept_WithValidToken_AddsAuthorizationHeader() throws IOException {
        // Given
        HttpHeaders headers = new HttpHeaders();
        when(request.getHeaders()).thenReturn(headers);
        when(request.getMethod()).thenReturn(HttpMethod.POST);
        when(request.getURI()).thenReturn(URI.create("http://localhost:8081/api/v1/mandates"));
        when(accessTokenProvider.getAccessToken()).thenReturn(TEST_JWT_TOKEN);
        when(execution.execute(request, EMPTY_BODY)).thenReturn(response);

        // When
        authenticationFilter.intercept(request, EMPTY_BODY, execution);

        // Then
        verify(accessTokenProvider).getAccessToken();
        verify(execution).execute(request, EMPTY_BODY);
        // Verify that the Bearer token was added to headers
        assert headers.getFirst(HttpHeaders.AUTHORIZATION).equals("Bearer " + TEST_JWT_TOKEN);
    }

    @Test
    public void testIntercept_DelegatesToFilterMethod() throws IOException {
        // Given
        HttpHeaders headers = new HttpHeaders();
        when(request.getHeaders()).thenReturn(headers);
        when(accessTokenProvider.getAccessToken()).thenReturn(TEST_JWT_TOKEN);
        when(execution.execute(request, EMPTY_BODY)).thenReturn(response);

        // When
        authenticationFilter.intercept(request, EMPTY_BODY, execution);

        // Then
        // Verify that the filter method was effectively called (token was added)
        verify(accessTokenProvider).getAccessToken();
        verify(execution).execute(request, EMPTY_BODY);
        assert headers.getFirst(HttpHeaders.AUTHORIZATION).equals("Bearer " + TEST_JWT_TOKEN);
    }
}
