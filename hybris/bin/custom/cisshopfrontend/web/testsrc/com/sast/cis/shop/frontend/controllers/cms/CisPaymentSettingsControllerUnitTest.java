package com.sast.cis.shop.frontend.controllers.cms;

import com.sast.cis.core.data.CreditCardPaymentInfoData;
import com.sast.cis.core.data.InvoicePaymentInfoData;
import com.sast.cis.core.data.PaymentSettingsData;
import com.sast.cis.core.facade.payment.PaymentSettingsFacade;
import com.sast.cis.core.service.ObjectMapperService;
import com.sast.cis.shop.frontend.controllers.pages.CisPaymentSettingsPageController;
import com.sast.cis.shop.frontend.handlers.HttpExceptionHandler;
import com.sast.cis.shop.frontend.messages.UserMessageFactory;
import com.sast.cis.shop.frontend.resources.BaseResourceUnitTest;
import de.hybris.bootstrap.annotations.UnitTest;
import de.hybris.platform.cms2.servicelayer.services.CMSPageService;
import de.hybris.platform.cms2.servicelayer.services.CMSPreviewService;
import de.hybris.platform.cms2.servicelayer.services.CMSSiteService;
import de.hybris.platform.commercefacades.customer.CustomerFacade;
import de.hybris.platform.commercefacades.storesession.StoreSessionFacade;
import org.assertj.core.internal.DeepDifference;
import org.assertj.core.internal.TypeComparators;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatcher;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.internal.TypeComparators.defaultTypeComparators;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.model;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@UnitTest
@RunWith(MockitoJUnitRunner.Silent.class)
public class CisPaymentSettingsControllerUnitTest extends BaseResourceUnitTest {

    private static final String URI_PATH = "/my-account";
    private static final String DEFAULT_SUB_PATH = "/payment-details";
    private static final String PAGE_JSON_DATA = "pageJsonData";

    @Mock
    private StoreSessionFacade storeSessionFacade;

    @Mock
    private CMSSiteService cmsSiteService;

    @Mock
    private CustomerFacade customerFacade;

    @Mock
    private PaymentSettingsFacade paymentSettingsFacade;

    @Mock
    private CMSPageService cmsPageService;

    @Mock
    private CMSPreviewService cmsPreviewService;

    @Mock
    private ObjectMapperService objectMapperService;

    @Mock
    private UserMessageFactory userMessageFactory;

    @InjectMocks
    private CisPaymentSettingsPageController controller;

    @Before
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(controller).setControllerAdvice(new HttpExceptionHandler(userMessageFactory)).build();
    }

    @Test
    public void getPaymentSettings_returnsCorrectModel() throws Exception {
        CreditCardPaymentInfoData ccPaymentInfoData = new CreditCardPaymentInfoData();
        InvoicePaymentInfoData invoicePaymentInfoData = new InvoicePaymentInfoData();

        PaymentSettingsData paymentSettingsData = new PaymentSettingsData()
            .withCcPaymentInfos(List.of(ccPaymentInfoData))
            .withInvoicePaymentInfos(List.of(invoicePaymentInfoData));

        String jsonResponse = objectMapper.writeValueAsString(paymentSettingsData);

        when(paymentSettingsFacade.getPaymentSettingsData()).thenReturn(paymentSettingsData);
        when(objectMapperService.toJsonUnescaped(argThat(new PaymentSettingsDataMatcher(paymentSettingsData)))).thenReturn(jsonResponse);

        mockMvc
            .perform(get(URI_PATH + DEFAULT_SUB_PATH))
            .andExpect(model().attribute(PAGE_JSON_DATA, jsonResponse))
            .andExpect(status().isOk());

        verify(paymentSettingsFacade, times(1)).getPaymentSettingsData();
    }

    private static class PaymentSettingsDataMatcher implements ArgumentMatcher<PaymentSettingsData> {

        private PaymentSettingsData leftData;

        PaymentSettingsDataMatcher(PaymentSettingsData paymentSettingsData) {
            leftData = paymentSettingsData;
        }

        @Override
        public boolean matches(PaymentSettingsData rightData) {
            Map<String, Comparator<?>> comparatorByPropertyOrField = new HashMap<>();
            TypeComparators comparatorByType = defaultTypeComparators();

            List<DeepDifference.Difference> differences = DeepDifference.determineDifferences(leftData, rightData,
                comparatorByPropertyOrField, comparatorByType);

            return differences.isEmpty();
        }
    }
}
