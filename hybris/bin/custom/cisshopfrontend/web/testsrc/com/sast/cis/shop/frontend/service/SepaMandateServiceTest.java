package com.sast.cis.shop.frontend.service;

import com.sast.cis.payment.sepa.client.SepaMandateGatewayApiServiceFactory;
import com.sast.cis.payment.sepa.client.api.SepaMandateApi;
import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class SepaMandateServiceTest {

    @Mock
    private SepaMandateGatewayApiServiceFactory sepaMandateGatewayApiServiceFactory;

    @Mock
    private SepaMandateApi sepaMandateApi;

    @Mock
    private Response jaxrsResponse;

    @InjectMocks
    private SepaMandateService sepaMandateService;

    private static final String TEST_COMPANY_ID = "test-company-123";
    private static final String TEST_REFERENCE = "test-reference-456";

    @Test
    public void testInitializeSepaMandateDraft_Success() {
        // Given
        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("mandateReference", "MANDATE-123");
        expectedResponse.put("status", "DRAFT");

        when(sepaMandateGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(sepaMandateApi.initializeSepaMandateDraft(TEST_COMPANY_ID)).thenReturn(jaxrsResponse);
        when(jaxrsResponse.getStatus()).thenReturn(200);
        when(jaxrsResponse.readEntity(Map.class)).thenReturn(expectedResponse);

        // When
        ResponseEntity<Map<String, Object>> result = sepaMandateService.initializeSepaMandateDraft(TEST_COMPANY_ID);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(result.getBody()).isEqualTo(expectedResponse);
    }

    @Test
    public void testUpdateOrFinalizeSepaMandateByReference_Success() {
        // Given
        Map<String, Object> payload = new HashMap<>();
        payload.put("accountHolderName", "John Doe");
        payload.put("iban", "**********************");

        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("mandateReference", TEST_REFERENCE);
        expectedResponse.put("status", "ACTIVE");

        when(sepaMandateGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(sepaMandateApi.updateOrFinalizeSepaMandateByReference(TEST_REFERENCE, payload)).thenReturn(jaxrsResponse);
        when(jaxrsResponse.getStatus()).thenReturn(200);
        when(jaxrsResponse.readEntity(Map.class)).thenReturn(expectedResponse);

        // When
        ResponseEntity<Map<String, Object>> result = sepaMandateService.updateOrFinalizeSepaMandateByReference(TEST_REFERENCE, payload);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(result.getBody()).isEqualTo(expectedResponse);
    }

    @Test
    public void testInitializeSepaMandateDraft_Exception() {
        // Given
        when(sepaMandateGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(sepaMandateApi.initializeSepaMandateDraft(TEST_COMPANY_ID)).thenThrow(new RuntimeException("Service unavailable"));

        // When & Then
        assertThatThrownBy(() -> sepaMandateService.initializeSepaMandateDraft(TEST_COMPANY_ID))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to initialize SEPA mandate draft");
    }

    @Test
    public void testUpdateOrFinalizeSepaMandateByReference_Exception() {
        // Given
        Map<String, Object> payload = new HashMap<>();
        payload.put("accountHolderName", "John Doe");

        when(sepaMandateGatewayApiServiceFactory.getSepaMandateApi()).thenReturn(sepaMandateApi);
        when(sepaMandateApi.updateOrFinalizeSepaMandateByReference(TEST_REFERENCE, payload))
                .thenThrow(new RuntimeException("Service unavailable"));

        // When & Then
        assertThatThrownBy(() -> sepaMandateService.updateOrFinalizeSepaMandateByReference(TEST_REFERENCE, payload))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to update/finalize SEPA mandate");
    }
}
