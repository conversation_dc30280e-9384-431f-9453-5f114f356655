package com.sast.cis.shop.frontend.service;

import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class SepaMandateServiceTest {

    @Mock
    private RestTemplate restTemplate;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private Authentication authentication;

    @Mock
    private JwtAuthenticationToken jwtAuthenticationToken;

    @Mock
    private Jwt jwt;

    @InjectMocks
    private SepaMandateService sepaMandateService;

    private static final String TEST_COMPANY_ID = "test-company-123";
    private static final String TEST_REFERENCE = "test-reference-456";
    private static final String TEST_JWT_TOKEN = "test-jwt-token";
    private static final String SEPA_MANDATE_SERVICE_URL = "http://localhost:8081";

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(sepaMandateService, "sepaMandateServiceUrl", SEPA_MANDATE_SERVICE_URL);
        SecurityContextHolder.setContext(securityContext);
    }

    @Test
    public void testInitializeSepaMandateDraft_Success() {
        // Given
        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("mandateReference", "MANDATE-123");
        expectedResponse.put("status", "DRAFT");

        ResponseEntity<Map<String, Object>> mockResponse = new ResponseEntity<>(expectedResponse, HttpStatus.OK);

        when(securityContext.getAuthentication()).thenReturn(jwtAuthenticationToken);
        when(jwtAuthenticationToken.getToken()).thenReturn(jwt);
        when(jwt.getTokenValue()).thenReturn(TEST_JWT_TOKEN);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), any(HttpEntity.class), any(Class.class)))
                .thenReturn(mockResponse);

        // When
        ResponseEntity<Map<String, Object>> result = sepaMandateService.initializeSepaMandateDraft(TEST_COMPANY_ID);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(result.getBody()).isEqualTo(expectedResponse);
    }

    @Test
    public void testUpdateOrFinalizeSepaMandateByReference_Success() {
        // Given
        Map<String, Object> payload = new HashMap<>();
        payload.put("accountHolderName", "John Doe");
        payload.put("iban", "**********************");

        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("mandateReference", TEST_REFERENCE);
        expectedResponse.put("status", "ACTIVE");

        ResponseEntity<Map<String, Object>> mockResponse = new ResponseEntity<>(expectedResponse, HttpStatus.OK);

        when(securityContext.getAuthentication()).thenReturn(jwtAuthenticationToken);
        when(jwtAuthenticationToken.getToken()).thenReturn(jwt);
        when(jwt.getTokenValue()).thenReturn(TEST_JWT_TOKEN);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), any(HttpEntity.class), any(Class.class)))
                .thenReturn(mockResponse);

        // When
        ResponseEntity<Map<String, Object>> result = sepaMandateService.updateOrFinalizeSepaMandateByReference(TEST_REFERENCE, payload);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(result.getBody()).isEqualTo(expectedResponse);
    }

    @Test
    public void testInitializeSepaMandateDraft_WithoutJwtToken() {
        // Given
        when(securityContext.getAuthentication()).thenReturn(authentication);

        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("mandateReference", "MANDATE-123");

        ResponseEntity<Map<String, Object>> mockResponse = new ResponseEntity<>(expectedResponse, HttpStatus.OK);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), any(HttpEntity.class), any(Class.class)))
                .thenReturn(mockResponse);

        // When
        ResponseEntity<Map<String, Object>> result = sepaMandateService.initializeSepaMandateDraft(TEST_COMPANY_ID);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getStatusCode()).isEqualTo(HttpStatus.OK);
    }

    @Test
    public void testInitializeSepaMandateDraft_RestTemplateException() {
        // Given
        when(securityContext.getAuthentication()).thenReturn(jwtAuthenticationToken);
        when(jwtAuthenticationToken.getToken()).thenReturn(jwt);
        when(jwt.getTokenValue()).thenReturn(TEST_JWT_TOKEN);
        when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), any(HttpEntity.class), any(Class.class)))
                .thenThrow(new RuntimeException("Connection failed"));

        // When & Then
        assertThatThrownBy(() -> sepaMandateService.initializeSepaMandateDraft(TEST_COMPANY_ID))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Failed to initialize SEPA mandate draft");
    }
}
