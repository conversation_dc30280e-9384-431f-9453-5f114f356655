package com.sast.cis.shop.frontend.service.authentication;

import de.hybris.bootstrap.annotations.UnitTest;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@UnitTest
@RunWith(MockitoJUnitRunner.class)
public class SepaMandateAccessTokenProviderTest {

    @Mock
    private SecurityContext securityContext;

    @Mock
    private Authentication authentication;

    @Mock
    private JwtAuthenticationToken jwtAuthenticationToken;

    @Mock
    private Jwt jwt;

    @InjectMocks
    private SepaMandateAccessTokenProvider accessTokenProvider;

    private static final String TEST_JWT_TOKEN = "test-jwt-token-value";

    @Before
    public void setUp() {
        SecurityContextHolder.setContext(securityContext);
    }

    @After
    public void tearDown() {
        SecurityContextHolder.clearContext();
    }

    @Test
    public void testGetAccessToken_WithJwtAuthentication_ReturnsToken() {
        // Given
        when(securityContext.getAuthentication()).thenReturn(jwtAuthenticationToken);
        when(jwtAuthenticationToken.getToken()).thenReturn(jwt);
        when(jwt.getTokenValue()).thenReturn(TEST_JWT_TOKEN);

        // When
        String result = accessTokenProvider.getAccessToken();

        // Then
        assertThat(result).isEqualTo(TEST_JWT_TOKEN);
    }

    @Test
    public void testGetAccessToken_WithNonJwtAuthentication_ReturnsNull() {
        // Given
        when(securityContext.getAuthentication()).thenReturn(authentication);

        // When
        String result = accessTokenProvider.getAccessToken();

        // Then
        assertThat(result).isNull();
    }

    @Test
    public void testGetAccessToken_WithNullAuthentication_ReturnsNull() {
        // Given
        when(securityContext.getAuthentication()).thenReturn(null);

        // When
        String result = accessTokenProvider.getAccessToken();

        // Then
        assertThat(result).isNull();
    }

    @Test
    public void testGetAccessToken_WithException_ReturnsNull() {
        // Given
        when(securityContext.getAuthentication()).thenThrow(new RuntimeException("Security context error"));

        // When
        String result = accessTokenProvider.getAccessToken();

        // Then
        assertThat(result).isNull();
    }
}
