package com.sast.cis.shop.frontend.controllers.pages.checkout.steps;

import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.sast.cis.core.constants.OrderSuccessType;
import com.sast.cis.core.constants.shop.ShopErrorCode;
import com.sast.cis.core.data.*;
import com.sast.cis.core.enums.PaymentMethodType;
import com.sast.cis.core.enums.PaymentProvider;
import com.sast.cis.core.exceptions.cart.CartUnpayableException;
import com.sast.cis.core.exceptions.web.BadRequestException;
import com.sast.cis.core.exceptions.web.InvalidCartStateException;
import com.sast.cis.core.facade.CisCartFacade;
import com.sast.cis.core.facade.CisCheckoutFacade;
import com.sast.cis.core.facade.cart.SessionCartExtensionFacade;
import com.sast.cis.core.facade.cart.eula.EulaNotAcceptedException;
import com.sast.cis.core.paymentintegration.data.AuthorizationResult;
import com.sast.cis.core.paymentintegration.data.AuthorizationStatus;
import com.sast.cis.core.paymentintegration.data.PaymentRedirectStatus;
import com.sast.cis.core.service.ObjectMapperService;
import com.sast.cis.core.service.TranslationService;
import com.sast.cis.shop.frontend.controllers.ControllerConstants;
import com.sast.cis.shop.frontend.form.CisPlaceOrderForm;
import com.sast.cis.shop.frontend.form.PaymentForm;
import com.sast.cis.shop.frontend.messages.UserMessageFactory;
import de.hybris.platform.acceleratorstorefrontcommons.annotations.PreValidateCheckoutStep;
import de.hybris.platform.acceleratorstorefrontcommons.annotations.PreValidateQuoteCheckoutStep;
import de.hybris.platform.acceleratorstorefrontcommons.checkout.steps.CheckoutStep;
import de.hybris.platform.acceleratorstorefrontcommons.constants.WebConstants;
import de.hybris.platform.acceleratorstorefrontcommons.controllers.pages.checkout.steps.AbstractCheckoutStepController;
import de.hybris.platform.acceleratorstorefrontcommons.controllers.util.GlobalMessages;
import de.hybris.platform.cms2.exceptions.CMSItemNotFoundException;
import de.hybris.platform.cms2.model.pages.ContentPageModel;
import de.hybris.platform.commercefacades.order.data.CartData;
import de.hybris.platform.commercefacades.order.data.OrderData;
import de.hybris.platform.commercefacades.user.data.AddressData;
import de.hybris.platform.order.InvalidCartException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.ResponseEntity.BodyBuilder;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Pattern;

import static com.sast.cis.shop.frontend.constants.CisshopfrontendConstants.SINGLE_CMS_PAGE;
import static com.sast.cis.shop.frontend.controllers.CheckoutControllerConstants.*;
import static com.sast.cis.shop.frontend.controllers.CisControllerConstants.PAGE_JSON_DATA;
import static com.sast.cis.shop.frontend.controllers.CisControllerConstants.PLACE_ORDER_FORM;
import static com.sast.cis.shop.frontend.controllers.pages.CartPageController.REDIRECT_CART_URL;
import static org.springframework.http.HttpStatus.OK;

@Controller
// If this URL is modified, PaymentRedirectTargetProvider also needs to change
@RequestMapping(value = "/checkout/multi/payment")
@Slf4j
@PreAuthorize("@cisCartFacade.hasCartAccess()")
public class PaymentSummaryCheckoutStepController extends AbstractCheckoutStepController {

    private static final String ERROR = "error";
    private static final String REDIRECT_PAGE = "redirectPage";
    private static final String CART_PAGE = "cart";
    private static final String PAYMENT_CHECKOUT_PAGE = "payment";
    private static final String SUMMARY_CHECKOUT_STEP = "summary";
    private static final String CART_DATA_ATTR = "cartData";
    private static final Pattern DPG_EXPIRY_PATTERN = Pattern.compile("^(?<expiryMonth>\\d{2})\\/(?<expiryYear>\\d{2})$");


    @Resource
    private CisCheckoutFacade cisCheckoutFacade;

    @Resource
    private CisCartFacade cisCartFacade;

    @Resource
    private SessionCartExtensionFacade sessionCartExtensionFacade;

    @Resource
    private ObjectMapperService objectMapperService;

    @Resource
    private TranslationService translationService;

    @Resource
    private UserMessageFactory userMessageFactory;

    @GetMapping
    @Override
    @PreValidateQuoteCheckoutStep
    @PreValidateCheckoutStep(checkoutStep = "payment-summary")
    public String enterStep(Model model, RedirectAttributes redirectAttributes) throws CMSItemNotFoundException {

        setupGenerals(model);

        model.addAttribute(PLACE_ORDER_FORM, new CisPlaceOrderForm());

        try {
            OrderPaymentData pageData = cisCheckoutFacade.getCheckoutPaymentSummaryPageData();
            model.addAttribute(PAGE_JSON_DATA, objectMapperService.toJsonUnescaped(pageData));
            return ControllerConstants.Views.Pages.MultiStepCheckout.CheckoutPaymentPage;
        } catch (CartUnpayableException e) {
            LOG.error("Cart (code={}) is unpayable.", e.getCartCode());
            GlobalMessages.addFlashMessage(redirectAttributes, GlobalMessages.ERROR_MESSAGES_HOLDER,
                    ShopErrorCode.CART_UNPAYABLE.getValue(), null);
            return REDIRECT_CART_URL;
        } catch (RuntimeException e) {
            LOG.error("Unhandled RuntimeException during checkout creation. See stacktrace for details", e);
            GlobalMessages.addFlashMessage(redirectAttributes, GlobalMessages.ERROR_MESSAGES_HOLDER,
                    ShopErrorCode.CART_CHECKOUT_PREPARATION_FAILURE.getValue(), null);
            return REDIRECT_CART_URL;
        }

    }

    private void setupGenerals(Model model) throws CMSItemNotFoundException {
        ContentPageModel pageModel = getContentPageForLabelOrId(SINGLE_CMS_PAGE);
        storeCmsPageInModel(model, pageModel);
        setUpMetaDataForContentPage(model, pageModel);
        model.addAttribute(WebConstants.BREADCRUMBS_KEY, getResourceBreadcrumbBuilder().getBreadcrumbs("checkout.multi.summary.breadcrumb"));
        model.addAttribute("metaRobots", "noindex,nofollow");
        setCheckoutStepLinksForModel(model, getCheckoutStep());
    }

    @PostMapping(value = "/placeOrder")
    @ResponseBody
    public ResponseEntity<String> placeOrder(@RequestParam(value = "selectedPaymentMethodId", required = false) String selectedPaymentMethodId,
                                             @RequestBody PlaceOrderData placeOrderData, HttpServletRequest request, RedirectAttributes redirectModel) {

        if (StringUtils.isNotBlank(selectedPaymentMethodId)) {
            LOG.info("Selecting payment method with id={}", selectedPaymentMethodId);
            cisCheckoutFacade.setPaymentInfo(selectedPaymentMethodId);
        }

        ResponseEntity<String> response = validate(placeOrderData, request, redirectModel);
        if (response.getStatusCode() != OK) {
            return response;
        }

        ResponseEntity<String> cartExtensionResult = getSessionCartExtensionResponse(placeOrderData);
        if (cartExtensionResult.getStatusCode() != OK) {
            return cartExtensionResult;
        }

        return getAuthorizePaymentResponse();
    }

    private ResponseEntity<String> getAuthorizePaymentResponse() {
        try {
            Optional<ResponseEntity<String>> stringResponseEntity = authorizePayment();
            return stringResponseEntity.orElseGet(this::placeOrder);
        } catch (RuntimeException e) {
            LOG.error("Error during payment authorization.", e);
            return internalServerError(ImmutableMap.of(
                    ERROR, translationService.translate(ShopErrorCode.CHECKOUT_PLACE_ORDER_FAILED.getValue()),
                    REDIRECT_PAGE, CART_PAGE));
        }
    }

    private ResponseEntity<String> getSessionCartExtensionResponse(PlaceOrderData placeOrderData) {
        try {
            sessionCartExtensionFacade.extendSessionCart(placeOrderData);
        } catch (BadRequestException exception) {
            return ResponseEntity.badRequest()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(objectMapperService.toJsonUnescaped(exception.getErrorMessageData()));
        } catch (RuntimeException e) {
            LOG.error("Error during session cart extension.", e);
            return internalServerError(ImmutableMap.of(
                    ERROR, "An unexpected error occurred during session cart extension.",
                    REDIRECT_PAGE, CART_PAGE));
        }
        return ResponseEntity.ok().build();
    }

    private Optional<ResponseEntity<String>> authorizePayment() {
        AuthorizationResult authorizationResult = cisCheckoutFacade.authorize();

        if (AuthorizationStatus.REQUIRE_USER.equals(authorizationResult.getAuthorizationStatus())) {
            return Optional.of(ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
                    .body(objectMapperService.toJsonUnescaped(authorizationResult.getUserActionParameters())));
        } else if (AuthorizationStatus.PENDING.equals(authorizationResult.getAuthorizationStatus())) {
            return Optional.of(ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
                    .body(objectMapperService.toJsonUnescaped(Map.of("pending", true))));
        } else if (AuthorizationStatus.ERROR.equals(authorizationResult.getAuthorizationStatus())) {
            LOG.error("Error during payment authorization. See log for further details.");
            return Optional.of(badRequest(ImmutableMap.of(
                ERROR, translationService.translate(ShopErrorCode.CHECKOUT_PLACE_ORDER_FAILED.getValue()),
                REDIRECT_PAGE, PAYMENT_CHECKOUT_PAGE)));
        }
        return Optional.empty();
    }

    private ResponseEntity<String> placeOrder() {
        try {
            OrderData orderData = getCheckoutFlowFacade().placeOrder();
            LOG.info("order with code={} was successfully created. User will be redirected to order success page",
                    orderData.getCode());

            ImmutableMap<String, String> successData = ImmutableMap
                    .of("url", getPlaceOrderUrl(cisCheckoutFacade.getOrderSuccessType(), orderData.getCode()));

            return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON)
                    .body(objectMapperService.toJsonUnescaped(successData));
        } catch (InvalidCartException e) {
            throw new RuntimeException(e);
        }
    }


    private String getPlaceOrderUrl(OrderSuccessType type, String orderCode) {
        if (type.equals(OrderSuccessType.PENDING)) {
            return CHECKOUT_PATH_PREFIX + ORDER_PENDING_CONFIRMATION_PATH_PREFIX + orderCode;
        } else if (type.equals(OrderSuccessType.DELAYED_LICENSE_ACTIVATION)) {
            return CHECKOUT_PATH_PREFIX + ORDER_LICENSE_DELAYED_CONFIRMATION_PATH_PREFIX + orderCode;
        }
        return CHECKOUT_PATH_PREFIX + ORDER_CONFIRMATION_PATH_PREFIX + orderCode;
    }


    @PostMapping(value = "/confirmOrder")
    @ResponseBody
    public ResponseEntity<String> confirmOrder(@RequestBody PlaceOrderData placeOrderData, HttpServletRequest request,
                                               RedirectAttributes redirectModel) {
        LOG.debug("Entered confirmOrder method");

        ResponseEntity<String> response = validate(placeOrderData, request, redirectModel);
        if (response.getStatusCode() != OK) {
            return response;
        }

        Optional<ResponseEntity<String>> stringResponseEntity = authorizePayment();
        return stringResponseEntity.orElseGet(this::placeOrder);
    }

    private ResponseEntity<String> validate(PlaceOrderData placeOrderData, HttpServletRequest request,
                                            RedirectAttributes redirectModel) {
        if (getCheckoutFlowFacade().hasNoPaymentInfo()) {
            return badRequest(ImmutableMap.of(
                    ERROR, translationService.translate(request, ShopErrorCode.CHECKOUT_INVALID_CART_STATE.getValue()),
                    REDIRECT_PAGE, PAYMENT_CHECKOUT_PAGE));
        }

        Map<String, String> errorMap = validateCartState(request);
        if (!errorMap.isEmpty()) {
            return internalServerError(errorMap);
        }

        if (validateCart(redirectModel)) {
            LOG.warn("Invalid cart for paymentIntentId={}: {}", placeOrderData.getPaymentIntentId(), redirectModel.getFlashAttributes());
            return internalServerError(ImmutableMap.of(
                    ERROR, translationService.translate(ShopErrorCode.CHECKOUT_PLACE_ORDER_FAILED.getValue()),
                    REDIRECT_PAGE, CART_PAGE));
        }

        if (cisCartFacade.hasOrderHashChanged(placeOrderData.getCartHash())) {
            CartData cart = cisCartFacade.getSessionCart();
            LOG.debug("Rejecting placeOrder as cart with code={} has changed.", cart.getCode());
            return conflict(ImmutableMap.of(
                    ERROR, translationService.translate(ShopErrorCode.CHECKOUT_CART_MODIFIED.getValue()),
                    REDIRECT_PAGE, CART_PAGE));
        }

        return ResponseEntity.ok().build();
    }

    private Map<String, String> validateCartState(HttpServletRequest request) {
        CartData cartData = getCheckoutFacade().getCheckoutCart();

        if (!getCheckoutFacade().containsTaxValues()) {
            LOG.error("Cart {} does not have any tax values, which means the tax calculation was not properly done," +
                            " placement of order can't continue",
                    cartData.getCode());
            return ImmutableMap.of(
                    ERROR, translationService.translate(request, ShopErrorCode.CHECKOUT_INVALID_CART_STATE.getValue()),
                    REDIRECT_PAGE, CART_PAGE);
        }

        if (!cartData.isCalculated()) {
            LOG.error("Cart {} has a calculated flag of FALSE, placement of order can't continue", cartData.getCode());
            return ImmutableMap.of(
                    ERROR, translationService.translate(request, ShopErrorCode.CHECKOUT_INVALID_CART_STATE.getValue()),
                    REDIRECT_PAGE, CART_PAGE);
        }

        return Collections.emptyMap();
    }

    // If this URL is modified, PaymentRedirectTargetProvider also needs to change
    @GetMapping("/{orderId}/successful")
    public String handleSuccessfulRedirect(RedirectAttributes redirectAttributes, @PathVariable("orderId") @NotNull String orderId) {
        cisCheckoutFacade.notifyRedirectResult(orderId, PaymentRedirectStatus.SUCCESS);
        return getCheckoutStep().currentStep();
    }

    // If this URL is modified, PaymentRedirectTargetProvider also needs to change
    @GetMapping("/{orderId}/failed")
    public String handleFailureRedirect(RedirectAttributes redirectAttributes, @PathVariable("orderId") @NotNull String orderId) {
        cisCheckoutFacade.notifyRedirectResult(orderId, PaymentRedirectStatus.FAILURE);
        GlobalMessages.addFlashMessage(redirectAttributes, GlobalMessages.ERROR_MESSAGES_HOLDER, ShopErrorCode.CHECKOUT_PAYMENT_FAILED.getValue());
        return getCheckoutStep().currentStep();
    }

    // If this URL is modified, PaymentRedirectTargetProvider also needs to change
    @GetMapping("/{orderId}/cancelled")
    public String handleCancelRedirect(RedirectAttributes redirectAttributes, @PathVariable("orderId") @NotNull String orderId) {
        cisCheckoutFacade.notifyRedirectResult(orderId, PaymentRedirectStatus.CANCEL);
        GlobalMessages.addFlashMessage(redirectAttributes, GlobalMessages.ERROR_MESSAGES_HOLDER, ShopErrorCode.CHECKOUT_PAYMENT_FAILED.getValue());
        return getCheckoutStep().currentStep();
    }

    @PostMapping("/add")
    public ResponseEntity<String> add(Model model, @RequestBody @Valid PaymentForm paymentForm) throws CMSItemNotFoundException {

        CartData cartData = getCheckoutFacade().getCheckoutCart();
        model.addAttribute(CART_DATA_ATTR, cartData);

        PaymentMethodType paymentMethod = PaymentMethodType.valueOf(paymentForm.getPaymentMethod());
        PaymentProvider paymentProvider = PaymentProvider.valueOf(paymentForm.getPaymentProvider());

        AddressData addressData = cartData.getPaymentAddress();
        if (addressData != null) {
            addressData.setBillingAddress(true);
            getAddressVerificationFacade().verifyAddressData(addressData);
        }

        PaymentInfoData paymentInfo = switch (paymentMethod) {
            case SEPA_DIRECTDEBIT -> new SepaMandatePaymentInfoData();
            case CREDIT_CARD -> {
                Preconditions.checkArgument(PaymentProvider.DPG.equals(paymentProvider), "Credit card is only supported for DPG.");
                var matcher = DPG_EXPIRY_PATTERN.matcher(paymentForm.getExpiry());
                Preconditions.checkArgument(matcher.matches(), "Given expiry date does not match pattern MM/YY.");
                yield new CreditCardPaymentInfoData()
                        .withBillingAddress(addressData)
                        .withSubscriptionId(paymentForm.getExternalPaymentMethodId())
                        .withAccountHolderName(paymentForm.getOwner())
                        .withToken(paymentForm.getToken())
                        .withExpiryMonth(matcher.group("expiryMonth"))
                        .withExpiryYear(matcher.group("expiryYear"))
                        .withCardNumber(paymentForm.getTruncatedCardNumber());
            }
            default -> new PaymentInfoData();
        };

        paymentInfo.setPaymentProvider(paymentProvider);
        paymentInfo.setReusable(paymentForm.isSaveCard());
        paymentInfo.setPaymentMethod(paymentMethod);


        try {
            cisCheckoutFacade.createPaymentInfo(paymentInfo);
        } catch (RuntimeException e) {
            LOG.error("Creation of new payment info failed with error={}.", e.getMessage(), e);
            throw e;
        }

        return ResponseEntity.ok().build();
    }

    @GetMapping("/error")
    public String handleRedirectForError(RedirectAttributes redirectAttributes,
                                         @RequestParam(required = false, name = "redirectPage") String redirectPage,
                                         @RequestParam(required = false, name = "errorCode") String errorCode) {
        GlobalMessages.addFlashMessage(redirectAttributes, GlobalMessages.ERROR_MESSAGES_HOLDER, errorCode);
        switch (Strings.nullToEmpty(redirectPage)) {
            case CART_PAGE:
                return REDIRECT_PREFIX + "/cart";
            case PAYMENT_CHECKOUT_PAGE:
                return getCheckoutStep().previousStep();
            case SUMMARY_CHECKOUT_STEP:
                return getCheckoutStep().currentStep();
            default:
        }
        LOG.error("Unknown redirect page {}. Will redirect to cart page.", redirectPage);
        return REDIRECT_PREFIX + "/cart";

    }

    @GetMapping("/back")
    @Override
    public String back(RedirectAttributes redirectAttributes) {
        if (cisCheckoutFacade.getPaymentProviderOfSessionCart().stream().anyMatch(PaymentProvider.ZERO::equals)) {
            return REDIRECT_CART_URL;
        }
        return getCheckoutStep().previousStep();
    }

    @GetMapping("/next")
    @Override
    public String next(RedirectAttributes redirectAttributes) {
        return getCheckoutStep().nextStep();
    }

    protected CheckoutStep getCheckoutStep() {
        return getCheckoutStep("payment-summary");
    }

    private ResponseEntity<String> conflict(Object json) {
        return withJsonBody(ResponseEntity.status(HttpStatus.CONFLICT), json);
    }

    private ResponseEntity<String> badRequest(Object json) {
        return withJsonBody(ResponseEntity.badRequest(), json);
    }

    private ResponseEntity<String> internalServerError(Object json) {
        LOG.warn("Error during checkout processing {}", json);
        return withJsonBody(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR), json);
    }

    private ResponseEntity<String> withJsonBody(BodyBuilder bodyBuilder, Object json) {
        return bodyBuilder.contentType(MediaType.APPLICATION_JSON)
                .body(objectMapperService.toJsonUnescaped(json));
    }

    @ExceptionHandler(EulaNotAcceptedException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public StoreErrorResponseData handleEulaNotAcceptedException(EulaNotAcceptedException exc) {
        return createErrorResponsePayload(exc.getCode()).withErrorMessage(exc.getMessage());
    }

    @ExceptionHandler(InvalidCartStateException.class)
    public String handleInvalidCart(InvalidCartStateException exc) {
        return REDIRECT_PREFIX + "/checkout/multi/payment/" + exc.getCartCode() + "/failed";
    }

    private StoreErrorResponseData createErrorResponsePayload(ShopErrorCode shopErrorCode) {
        var userMessage = userMessageFactory.error(shopErrorCode);
        return new StoreErrorResponseData()
                .withErrorCode(shopErrorCode.getValue())
                .withUserMessages(userMessage != null ? List.of(userMessage) : List.of());
    }

}
