package com.sast.cis.shop.frontend.service.authentication;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Authentication filter for SEPA mandate service REST calls.
 * Automatically adds JWT Bearer token to outgoing requests.
 * Follows the same naming pattern as AuthenticationFilter used in AdyenGatewayApiServiceFactory.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SepaMandateAuthenticationFilter implements ClientHttpRequestInterceptor {

    private final SepaMandateAccessTokenProvider accessTokenProvider;

    /**
     * Main filter method - follows the same naming convention as JAX-RS AuthenticationFilter.
     * This method contains the core authentication logic, similar to:
     * com.sast.cis.payment.adyen.client.authentication.filter.AuthenticationFilter.filter(ClientRequestContext)
     * 
     * @param request the HTTP request
     */
    public void filter(HttpRequest request) {
        HttpHeaders headers = request.getHeaders();
        
        // Only add authorization header if not already present
        if (!headers.containsKey(HttpHeaders.AUTHORIZATION)) {
            String token = accessTokenProvider.getAccessToken();
            if (token != null) {
                headers.setBearerAuth(token);
                LOG.debug("Added JWT Bearer token to SEPA mandate request: {} {}", 
                        request.getMethod(), request.getURI());
            } else {
                LOG.warn("No JWT token available for SEPA mandate request: {} {}", 
                        request.getMethod(), request.getURI());
            }
        }
    }

    /**
     * ClientHttpRequestInterceptor implementation method.
     * Delegates to filter() method to maintain consistency with AuthenticationFilter pattern.
     */
    @Override
    public ClientHttpResponse intercept(
            HttpRequest request, 
            byte[] body, 
            ClientHttpRequestExecution execution) throws IOException {
        
        // Delegate to filter method to maintain consistency with AuthenticationFilter pattern
        filter(request);
        
        return execution.execute(request, body);
    }
}
