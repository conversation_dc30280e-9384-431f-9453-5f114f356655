package com.sast.cis.shop.frontend.service.authentication;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;

/**
 * Generic authentication filter for RestTemplate requests.
 * This is the RestTemplate equivalent of the JAX-RS AuthenticationFilter.
 * Reuses the same logic and pattern as the existing AuthenticationFilter but adapted for Spring's ClientHttpRequestInterceptor.
 */
@RequiredArgsConstructor
@Slf4j
public class RestTemplateAuthenticationFilter implements ClientHttpRequestInterceptor {

    private final AccessTokenProvider accessTokenProvider;

    /**
     * Main filter method - follows the same naming convention and logic as JAX-RS AuthenticationFilter.
     * This method contains the core authentication logic, identical to:
     * com.sast.cis.payment.adyen.client.authentication.filter.AuthenticationFilter.filter(ClientRequestContext)
     * 
     * @param request the HTTP request
     */
    public void filter(HttpRequest request) {
        HttpHeaders headers = request.getHeaders();
        
        // Only add authorization header if not already present
        if (!headers.containsKey(HttpHeaders.AUTHORIZATION)) {
            String token = accessTokenProvider.getAccessToken();
            if (token != null) {
                headers.setBearerAuth(token);
                LOG.debug("Added JWT Bearer token to request: {} {}", 
                        request.getMethod(), request.getURI());
            } else {
                LOG.warn("No JWT token available for request: {} {}", 
                        request.getMethod(), request.getURI());
            }
        }
    }

    /**
     * ClientHttpRequestInterceptor implementation method.
     * Delegates to filter() method to maintain consistency with JAX-RS AuthenticationFilter pattern.
     */
    @Override
    public ClientHttpResponse intercept(
            HttpRequest request, 
            byte[] body, 
            ClientHttpRequestExecution execution) throws IOException {
        
        // Delegate to filter method to maintain consistency with AuthenticationFilter pattern
        filter(request);
        
        return execution.execute(request, body);
    }
}
