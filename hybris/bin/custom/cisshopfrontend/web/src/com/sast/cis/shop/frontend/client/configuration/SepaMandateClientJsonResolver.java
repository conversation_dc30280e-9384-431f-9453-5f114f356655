package com.sast.cis.shop.frontend.client.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import javax.ws.rs.ext.ContextResolver;
import javax.ws.rs.ext.Provider;

/**
 * JSON resolver for SEPA mandate client.
 * Configures Jackson ObjectMapper for proper JSON serialization/deserialization.
 */
@Provider
public class SepaMandateClientJsonResolver implements ContextResolver<ObjectMapper> {

    private final ObjectMapper objectMapper;

    public SepaMandateClientJsonResolver() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
    }

    @Override
    public ObjectMapper getContext(Class<?> type) {
        return objectMapper;
    }
}
