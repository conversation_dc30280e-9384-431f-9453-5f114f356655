package com.sast.cis.shop.frontend.service;

import com.sast.cis.shop.frontend.service.authentication.SepaMandateAuthenticationFilter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * Factory for creating RestTemplate instances configured for SEPA mandate service calls.
 * Inspired by AdyenGatewayApiServiceFactory pattern.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SepaMandateRestTemplateFactory {

    private final SepaMandateAuthenticationFilter authenticationFilter;

    @Resource(name = "sastClientHttpRequestFactory")
    private HttpComponentsClientHttpRequestFactory sastClientHttpRequestFactory;

    /**
     * Creates a RestTemplate configured with authentication filter for SEPA mandate service calls
     * @return Configured RestTemplate instance
     */
    public RestTemplate createSepaMandateRestTemplate() {
        LOG.debug("Creating SEPA mandate RestTemplate with authentication filter");
        
        RestTemplate restTemplate = new RestTemplate(
                new BufferingClientHttpRequestFactory(sastClientHttpRequestFactory)
        );
        
        // Add the authentication filter to automatically handle JWT tokens
        restTemplate.getInterceptors().add(authenticationFilter);
        
        LOG.debug("SEPA mandate RestTemplate created successfully");
        return restTemplate;
    }
}
