package com.sast.cis.shop.frontend.service;

import com.sast.cis.shop.frontend.service.authentication.AccessTokenProvider;
import com.sast.cis.shop.frontend.service.authentication.RestTemplateAuthenticationFilter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * Factory for creating RestTemplate instances configured for SEPA mandate service calls.
 * Inspired by AdyenGatewayApiServiceFactory pattern.
 * Uses the generic RestTemplateAuthenticationFilter which reuses the same logic as the JAX-RS AuthenticationFilter.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SepaMandateRestTemplateFactory {

    private final AccessTokenProvider sepaMandateAccessTokenProvider;

    @Resource(name = "sastClientHttpRequestFactory")
    private HttpComponentsClientHttpRequestFactory sastClientHttpRequestFactory;

    /**
     * Creates a RestTemplate configured with authentication filter for SEPA mandate service calls.
     * This follows the same pattern as AdyenGatewayApiServiceFactory but for RestTemplate instead of JAX-RS Client.
     * @return Configured RestTemplate instance
     */
    public RestTemplate createSepaMandateRestTemplate() {
        LOG.debug("Creating SEPA mandate RestTemplate with authentication filter");
        
        RestTemplate restTemplate = new RestTemplate(
                new BufferingClientHttpRequestFactory(sastClientHttpRequestFactory)
        );
        
        // Create the generic authentication filter with the SEPA mandate access token provider
        // This reuses the same logic as the JAX-RS AuthenticationFilter
        RestTemplateAuthenticationFilter authenticationFilter = 
                new RestTemplateAuthenticationFilter(sepaMandateAccessTokenProvider);
        
        // Add the authentication filter to automatically handle JWT tokens
        restTemplate.getInterceptors().add(authenticationFilter);
        
        LOG.debug("SEPA mandate RestTemplate created successfully");
        return restTemplate;
    }
}
