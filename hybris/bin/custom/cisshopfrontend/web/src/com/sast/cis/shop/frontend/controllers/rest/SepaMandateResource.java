package com.sast.cis.shop.frontend.controllers.rest;

import com.sast.cis.shop.frontend.service.SepaMandateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * REST controller for SEPA mandate operations.
 * This controller acts as a proxy between the frontend and the sepa-mandate service,
 * ensuring proper authentication and authorization.
 */
@RestController
@RequestMapping("/sepa-mandates")
@PreAuthorize("@userPermissionService.hasShopPermission('EDIT_PAYMENT_DETAILS')")
@Api(tags = "SEPA Mandates")
@RequiredArgsConstructor
@Slf4j
public class SepaMandateResource {

    private final SepaMandateService sepaMandateService;

    /**
     * Initialize a draft SEPA mandate
     * @param companyId - The company ID for the mandate
     * @return Response with SepaMandateDto containing auto-generated mandate reference
     */
    @PostMapping("/initialize")
    @ApiOperation(value = "Initialize a draft SEPA mandate")
    public ResponseEntity<Map<String, Object>> initializeSepaMandateDraft(
            @RequestParam @NotNull String companyId) {
        
        LOG.info("REST: Initializing SEPA mandate draft for company: {}", companyId);
        return sepaMandateService.initializeSepaMandateDraft(companyId);
    }

    /**
     * Update or finalize a SEPA mandate by reference
     * @param reference - The mandate reference
     * @param payload - The mandate data (can be partial for updates or complete for finalization)
     * @return Response with updated/finalized SepaMandateDto
     */
    @PostMapping("/{reference}/activate")
    @ApiOperation(value = "Update or finalize a SEPA mandate by reference")
    public ResponseEntity<Map<String, Object>> updateOrFinalizeSepaMandateByReference(
            @PathVariable @NotNull String reference,
            @RequestBody @NotNull Map<String, Object> payload) {
        
        LOG.info("REST: Updating/finalizing SEPA mandate with reference: {}", reference);
        return sepaMandateService.updateOrFinalizeSepaMandateByReference(reference, payload);
    }
}
