package com.sast.cis.shop.frontend.service.authentication;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;

/**
 * Access token provider for SEPA mandate service.
 * Extracts JWT token from the current security context.
 */
@Component
@Slf4j
public class SepaMandateAccessTokenProvider {

    /**
     * Get the current JWT access token from the security context
     * @return JWT token string or null if not found
     */
    public String getAccessToken() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication instanceof JwtAuthenticationToken) {
                JwtAuthenticationToken jwtAuth = (JwtAuthenticationToken) authentication;
                Jwt jwt = jwtAuth.getToken();
                String token = jwt.getTokenValue();
                LOG.debug("Successfully extracted JWT token from security context");
                return token;
            }
            
            LOG.debug("Current authentication is not a JWT token: {}", 
                    authentication != null ? authentication.getClass().getSimpleName() : "null");
            return null;
            
        } catch (Exception e) {
            LOG.error("Error extracting JWT token from security context", e);
            return null;
        }
    }
}
