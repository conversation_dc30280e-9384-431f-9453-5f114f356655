package com.sast.cis.shop.frontend.service;

import com.sast.cis.core.util.BearerAuthInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.Map;

/**
 * Service for handling SEPA mandate operations with JWT authentication.
 * This service acts as a proxy between the frontend and the sepa-mandate service,
 * reusing the existing BearerAuthInterceptor for JWT authentication.
 */
@Service
@Slf4j
public class SepaMandateService {

    @Resource(name = "sastClientHttpRequestFactory")
    private HttpComponentsClientHttpRequestFactory sastClientHttpRequestFactory;

    @Value("${sepa.mandate.service.url:http://localhost:8081}")
    private String sepaMandateServiceUrl;

    private static final String MANDATES_API_PATH = "/api/v1/mandates";

    /**
     * Initialize a draft SEPA mandate
     * @param companyId - The company ID for the mandate
     * @return Response from the sepa-mandate service
     */
    public ResponseEntity<Map<String, Object>> initializeSepaMandateDraft(String companyId) {
        LOG.info("Initializing SEPA mandate draft for company: {}", companyId);
        
        try {
            String url = UriComponentsBuilder.fromHttpUrl(sepaMandateServiceUrl + MANDATES_API_PATH + "/initialize")
                    .queryParam("companyId", companyId)
                    .toUriString();

            RestTemplate restTemplate = createAuthenticatedRestTemplate();
            
            HttpHeaders headers = createHeaders();
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    (Class<Map<String, Object>>) (Class<?>) Map.class
            );

            LOG.info("Successfully initialized SEPA mandate draft for company: {}", companyId);
            return response;

        } catch (Exception e) {
            LOG.error("Failed to initialize SEPA mandate draft for company: {}", companyId, e);
            throw new RuntimeException("Failed to initialize SEPA mandate draft", e);
        }
    }

    /**
     * Update or finalize a SEPA mandate by reference
     * @param reference - The mandate reference
     * @param payload - The mandate data
     * @return Response from the sepa-mandate service
     */
    public ResponseEntity<Map<String, Object>> updateOrFinalizeSepaMandateByReference(String reference, Map<String, Object> payload) {
        LOG.info("Updating/finalizing SEPA mandate with reference: {}", reference);
        
        try {
            String url = sepaMandateServiceUrl + MANDATES_API_PATH + "/" + reference + "/activate";

            RestTemplate restTemplate = createAuthenticatedRestTemplate();
            
            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(payload, headers);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    (Class<Map<String, Object>>) (Class<?>) Map.class
            );

            LOG.info("Successfully updated/finalized SEPA mandate with reference: {}", reference);
            return response;

        } catch (Exception e) {
            LOG.error("Failed to update/finalize SEPA mandate with reference: {}", reference, e);
            throw new RuntimeException("Failed to update/finalize SEPA mandate", e);
        }
    }

    /**
     * Create RestTemplate with BearerAuthInterceptor for JWT authentication.
     * Reuses the existing BearerAuthInterceptor from the codebase.
     * @return Configured RestTemplate instance
     */
    private RestTemplate createAuthenticatedRestTemplate() {
        RestTemplate restTemplate = new RestTemplate(
                new BufferingClientHttpRequestFactory(sastClientHttpRequestFactory)
        );
        
        // Add the existing BearerAuthInterceptor for JWT authentication
        restTemplate.getInterceptors().add(new BearerAuthInterceptor());
        
        return restTemplate;
    }

    /**
     * Create HTTP headers for requests
     * @return HttpHeaders with Content-Type
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        return headers;
    }
}
