package com.sast.cis.shop.frontend.service;

import com.sast.cis.shop.frontend.client.SepaMandateGatewayApiServiceFactory;
import com.sast.cis.shop.frontend.client.api.SepaMandateApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.ws.rs.core.Response;
import java.util.Map;

/**
 * Service for handling SEPA mandate operations with JWT authentication.
 * This service acts as a proxy between the frontend and the sepa-mandate service,
 * using the SepaMandateGatewayApiServiceFactory which follows the same pattern as AdyenGatewayApiServiceFactory.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SepaMandateService {

    private final SepaMandateGatewayApiServiceFactory sepaMandateGatewayApiServiceFactory;

    /**
     * Initialize a draft SEPA mandate
     * @param companyId - The company ID for the mandate
     * @return Response from the sepa-mandate service
     */
    public ResponseEntity<Map<String, Object>> initializeSepaMandateDraft(String companyId) {
        LOG.info("Initializing SEPA mandate draft for company: {}", companyId);
        
        try {
            SepaMandateApi sepaMandateApi = sepaMandateGatewayApiServiceFactory.getSepaMandateApi();
            Response response = sepaMandateApi.initializeSepaMandateDraft(companyId);
            
            Map<String, Object> responseBody = response.readEntity(Map.class);
            HttpStatus httpStatus = HttpStatus.valueOf(response.getStatus());
            
            LOG.info("Successfully initialized SEPA mandate draft for company: {}", companyId);
            return new ResponseEntity<>(responseBody, httpStatus);

        } catch (Exception e) {
            LOG.error("Failed to initialize SEPA mandate draft for company: {}", companyId, e);
            throw new RuntimeException("Failed to initialize SEPA mandate draft", e);
        }
    }

    /**
     * Update or finalize a SEPA mandate by reference
     * @param reference - The mandate reference
     * @param payload - The mandate data
     * @return Response from the sepa-mandate service
     */
    public ResponseEntity<Map<String, Object>> updateOrFinalizeSepaMandateByReference(String reference, Map<String, Object> payload) {
        LOG.info("Updating/finalizing SEPA mandate with reference: {}", reference);
        
        try {
            SepaMandateApi sepaMandateApi = sepaMandateGatewayApiServiceFactory.getSepaMandateApi();
            Response response = sepaMandateApi.updateOrFinalizeSepaMandateByReference(reference, payload);
            
            Map<String, Object> responseBody = response.readEntity(Map.class);
            HttpStatus httpStatus = HttpStatus.valueOf(response.getStatus());
            
            LOG.info("Successfully updated/finalized SEPA mandate with reference: {}", reference);
            return new ResponseEntity<>(responseBody, httpStatus);

        } catch (Exception e) {
            LOG.error("Failed to update/finalize SEPA mandate with reference: {}", reference, e);
            throw new RuntimeException("Failed to update/finalize SEPA mandate", e);
        }
    }
}
