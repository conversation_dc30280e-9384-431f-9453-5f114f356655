package com.sast.cis.shop.frontend.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Map;

/**
 * Service for handling SEPA mandate operations with JWT authentication.
 * This service acts as a proxy between the frontend and the sepa-mandate service,
 * using the AuthenticationFilter pattern similar to AdyenGatewayApiServiceFactory.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SepaMandateService {

    private final SepaMandateRestTemplateFactory restTemplateFactory;

    @Value("${sepa.mandate.service.url:http://localhost:8081}")
    private String sepaMandateServiceUrl;

    private static final String MANDATES_API_PATH = "/api/v1/mandates";

    /**
     * Initialize a draft SEPA mandate
     * @param companyId - The company ID for the mandate
     * @return Response from the sepa-mandate service
     */
    public ResponseEntity<Map<String, Object>> initializeSepaMandateDraft(String companyId) {
        LOG.info("Initializing SEPA mandate draft for company: {}", companyId);
        
        try {
            String url = UriComponentsBuilder.fromHttpUrl(sepaMandateServiceUrl + MANDATES_API_PATH + "/initialize")
                    .queryParam("companyId", companyId)
                    .toUriString();

            // Get RestTemplate with authentication filter configured
            RestTemplate restTemplate = restTemplateFactory.createSepaMandateRestTemplate();
            
            HttpHeaders headers = createHeaders();
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    (Class<Map<String, Object>>) (Class<?>) Map.class
            );

            LOG.info("Successfully initialized SEPA mandate draft for company: {}", companyId);
            return response;

        } catch (Exception e) {
            LOG.error("Failed to initialize SEPA mandate draft for company: {}", companyId, e);
            throw new RuntimeException("Failed to initialize SEPA mandate draft", e);
        }
    }

    /**
     * Update or finalize a SEPA mandate by reference
     * @param reference - The mandate reference
     * @param payload - The mandate data
     * @return Response from the sepa-mandate service
     */
    public ResponseEntity<Map<String, Object>> updateOrFinalizeSepaMandateByReference(String reference, Map<String, Object> payload) {
        LOG.info("Updating/finalizing SEPA mandate with reference: {}", reference);
        
        try {
            String url = sepaMandateServiceUrl + MANDATES_API_PATH + "/" + reference + "/activate";

            // Get RestTemplate with authentication filter configured
            RestTemplate restTemplate = restTemplateFactory.createSepaMandateRestTemplate();
            
            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(payload, headers);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    (Class<Map<String, Object>>) (Class<?>) Map.class
            );

            LOG.info("Successfully updated/finalized SEPA mandate with reference: {}", reference);
            return response;

        } catch (Exception e) {
            LOG.error("Failed to update/finalize SEPA mandate with reference: {}", reference, e);
            throw new RuntimeException("Failed to update/finalize SEPA mandate", e);
        }
    }

    /**
     * Create HTTP headers for requests
     * Note: JWT authentication is handled by the AuthenticationFilter in RestTemplate
     * @return HttpHeaders with Content-Type
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        // JWT token will be automatically added by SepaMandateAuthenticationFilter
        return headers;
    }
}
