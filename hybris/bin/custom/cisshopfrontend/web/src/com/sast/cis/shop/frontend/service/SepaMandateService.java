package com.sast.cis.shop.frontend.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.Map;

/**
 * Service for handling SEPA mandate operations with JWT authentication.
 * This service acts as a proxy between the frontend and the sepa-mandate service,
 * ensuring proper JWT token authentication is included in all requests.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SepaMandateService {

    @Resource(name = "cisRestTemplate")
    private RestTemplate restTemplate;

    @Value("${sepa.mandate.service.url:http://localhost:8081}")
    private String sepaMandateServiceUrl;

    private static final String MANDATES_API_PATH = "/api/v1/mandates";

    /**
     * Initialize a draft SEPA mandate
     * @param companyId - The company ID for the mandate
     * @return Response from the sepa-mandate service
     */
    public ResponseEntity<Map<String, Object>> initializeSepaMandateDraft(String companyId) {
        LOG.info("Initializing SEPA mandate draft for company: {}", companyId);
        
        try {
            String url = UriComponentsBuilder.fromHttpUrl(sepaMandateServiceUrl + MANDATES_API_PATH + "/initialize")
                    .queryParam("companyId", companyId)
                    .toUriString();

            HttpHeaders headers = createAuthenticatedHeaders();
            HttpEntity<Void> entity = new HttpEntity<>(headers);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    (Class<Map<String, Object>>) (Class<?>) Map.class
            );

            LOG.info("Successfully initialized SEPA mandate draft for company: {}", companyId);
            return response;

        } catch (Exception e) {
            LOG.error("Failed to initialize SEPA mandate draft for company: {}", companyId, e);
            throw new RuntimeException("Failed to initialize SEPA mandate draft", e);
        }
    }

    /**
     * Update or finalize a SEPA mandate by reference
     * @param reference - The mandate reference
     * @param payload - The mandate data
     * @return Response from the sepa-mandate service
     */
    public ResponseEntity<Map<String, Object>> updateOrFinalizeSepaMandateByReference(String reference, Map<String, Object> payload) {
        LOG.info("Updating/finalizing SEPA mandate with reference: {}", reference);
        
        try {
            String url = sepaMandateServiceUrl + MANDATES_API_PATH + "/" + reference + "/activate";

            HttpHeaders headers = createAuthenticatedHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(payload, headers);

            ResponseEntity<Map<String, Object>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    (Class<Map<String, Object>>) (Class<?>) Map.class
            );

            LOG.info("Successfully updated/finalized SEPA mandate with reference: {}", reference);
            return response;

        } catch (Exception e) {
            LOG.error("Failed to update/finalize SEPA mandate with reference: {}", reference, e);
            throw new RuntimeException("Failed to update/finalize SEPA mandate", e);
        }
    }

    /**
     * Create HTTP headers with JWT authentication token
     * @return HttpHeaders with Authorization header
     */
    private HttpHeaders createAuthenticatedHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json");
        
        String token = getCurrentJwtToken();
        if (token != null) {
            headers.setBearerAuth(token);
            LOG.debug("Added JWT token to request headers");
        } else {
            LOG.warn("No JWT token found in security context");
        }
        
        return headers;
    }

    /**
     * Extract JWT token from the current security context
     * @return JWT token string or null if not found
     */
    private String getCurrentJwtToken() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication instanceof JwtAuthenticationToken) {
                JwtAuthenticationToken jwtAuth = (JwtAuthenticationToken) authentication;
                Jwt jwt = jwtAuth.getToken();
                return jwt.getTokenValue();
            }
            
            LOG.debug("Current authentication is not a JWT token: {}", 
                    authentication != null ? authentication.getClass().getSimpleName() : "null");
            return null;
            
        } catch (Exception e) {
            LOG.error("Error extracting JWT token from security context", e);
            return null;
        }
    }
}
