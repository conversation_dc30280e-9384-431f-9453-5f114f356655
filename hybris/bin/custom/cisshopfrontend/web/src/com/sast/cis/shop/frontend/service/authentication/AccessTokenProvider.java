package com.sast.cis.shop.frontend.service.authentication;

/**
 * Generic interface for providing access tokens.
 * This interface allows different implementations for different authentication mechanisms
 * while maintaining the same contract for the AuthenticationFilter.
 */
public interface AccessTokenProvider {
    
    /**
     * Get the current access token
     * @return access token string or null if not available
     */
    String getAccessToken();
}
