package com.sast.cis.brim.service.filter;

import com.sast.cis.brim.config.ConsumerFilterConfig;
import com.sast.cis.core.config.SpringProfileConfig;
import com.sast.cis.core.constants.Environment;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

@Slf4j
public class BrimProductConsumerFilter implements ProductConsumerFilter {
    private final SpringProfileConfig springProfileConfig;
    private final ConsumerFilterConfig consumerFilterConfig;

    public BrimProductConsumerFilter(SpringProfileConfig springProfileConfig, ConsumerFilterConfig consumerFilterConfig) {
        this.springProfileConfig = springProfileConfig;
        this.consumerFilterConfig = consumerFilterConfig;
    }

    @Override
    public boolean shouldFilterOut(String productId) {
        if (StringUtils.isEmpty(productId)) {
            return false;
        }

        if (Environment.DEV != springProfileConfig.getEnvironment() && Environment.DEMO != springProfileConfig.getEnvironment()
            && Environment.LIVE != springProfileConfig.getEnvironment()) {
            throw new IllegalStateException("Message is neither conforming to dev, demo or live, product code=" + productId);
        }

        return !(Pattern.matches(consumerFilterConfig.getProductRegexp(), productId));

    }

}
