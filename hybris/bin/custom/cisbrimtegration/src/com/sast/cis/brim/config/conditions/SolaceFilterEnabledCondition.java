package com.sast.cis.brim.config.conditions;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.env.Profiles;
import org.springframework.core.type.AnnotatedTypeMetadata;

@Slf4j
public class SolaceFilterEnabledCondition implements Condition {
    @Override
    public boolean matches(ConditionContext conditionContext, AnnotatedTypeMetadata annotatedTypeMetadata) {
        boolean filteringEnabled = conditionContext.getEnvironment().acceptsProfiles(Profiles.of("dev", "demo", "live"));
        LOG.info("Filter for incoming orders and products enabled={}", filteringEnabled);
        return filteringEnabled;
    }
}
