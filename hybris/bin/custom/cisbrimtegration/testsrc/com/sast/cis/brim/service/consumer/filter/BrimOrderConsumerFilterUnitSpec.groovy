package com.sast.cis.brim.service.consumer.filter

import com.sast.cis.brim.config.ConsumerFilterConfig
import com.sast.cis.brim.service.filter.BrimOrderConsumerFilter
import com.sast.cis.core.config.SpringProfileConfig
import com.sast.cis.core.constants.Environment
import de.hybris.bootstrap.annotations.UnitTest
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification
import spock.lang.Unroll

@UnitTest
class BrimOrderConsumerFilterUnitSpec extends JUnitPlatformSpecification {

    SpringProfileConfig springProfileConfig = Mock(SpringProfileConfig)

    @Unroll
    @Test
    def "dev filter for #orderId should be #expected"() {
        given:
        ConsumerFilterConfig config = new ConsumerFilterConfig("^\\d{1,8}\$", "^A_\\d{1,8}_.+\$", "^SC_.*\$", true)
        BrimOrderConsumerFilter devBrimOrderConsumerFilter = new BrimOrderConsumerFilter(springProfileConfig, config)

        when:
        springProfileConfig.environment >> Environment.DEV
        def actual = devBrimOrderConsumerFilter.shouldFilterOut(orderId)

        then:
        expected == actual

        where:
        orderId        || expected
        null           || false
        ""             || false
        "00000001"     || false
        "12345678"     || false
        "91234567"     || false
        "1234567"      || false
        "SC_abc"       || false

        "912345678"    || true
        "123455678910" || true
        "1234567A"     || true
        "123456789"    || true
        "SCD_abc"      || true


    }

    @Unroll
    @Test
    def "demo filter for #orderId should be #expected"() {
        given:
        ConsumerFilterConfig config = new ConsumerFilterConfig("^9\\d{8}\$", "^A_9\\d{8}_.+\$", "^SCD_.*\$", true)
        BrimOrderConsumerFilter demoBrimOrderConsumerFilter = new BrimOrderConsumerFilter(springProfileConfig, config)

        when:
        springProfileConfig.environment >> Environment.DEMO
        def actual = demoBrimOrderConsumerFilter.shouldFilterOut(orderId)

        then:
        expected == actual

        where:
        orderId       || expected
        null          || false
        ""            || false
        "912345678"   || false
        "SCD_abc"     || false

        "91234567"    || true
        "91234567810" || true
        "91234567A"   || true
        "9123456789"  || true
        "912345678A"  || true
        "12345678"    || true
        "12345679A"   || true
        "1234567890"  || true
        "SC_abc"      || true
    }

    @Unroll
    @Test
    def "live filter for #orderId should be #expected"() {
        given:
        ConsumerFilterConfig config = new ConsumerFilterConfig("^0\\d{7}\$", "^(A|AA|AA2)_.+\$", "^SC_.*\$", true)
        springProfileConfig.environment >> Environment.LIVE
        BrimOrderConsumerFilter demoBrimOrderConsumerFilter = new BrimOrderConsumerFilter(springProfileConfig, config)

        when:
        def actual = demoBrimOrderConsumerFilter.shouldFilterOut(orderId)

        then:
        expected == actual

        where:
        orderId       || expected
        null          || false
        ""            || false
        "00521015"    || false
        "00521021"    || false
        "00521018"    || false
        "91234567"    || true
        "91234567810" || true
        "91234567A"   || true
        "9123456789"  || true
        "912345678A"  || true
        "12345678"    || true
        "12345679A"   || true
        "1234567890"  || true
        "SCD_abc"     || true
        "SC_abc"      || false
    }


    @Test
    def "different env throws an exception"() {
        given:
        ConsumerFilterConfig config = new ConsumerFilterConfig("^9\\d{8}\$", "^A_9\\d{8}_.+\$", "^SCD_.*\$", true)
        BrimOrderConsumerFilter brimOrderConsumerFilter = new BrimOrderConsumerFilter(springProfileConfig, config)

        when:
        springProfileConfig.environment >> Environment.INIT
        brimOrderConsumerFilter.shouldFilterOut("orderId")

        then:
        thrown(IllegalStateException.class)
    }
}
