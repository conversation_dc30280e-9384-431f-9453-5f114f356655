package com.sast.cis.brim.service.consumer.filter

import com.sast.cis.brim.config.ConsumerFilterConfig
import com.sast.cis.brim.service.filter.BrimProductConsumerFilter
import com.sast.cis.core.config.SpringProfileConfig
import com.sast.cis.core.constants.Environment
import de.hybris.bootstrap.annotations.UnitTest
import org.junit.Test
import de.hybris.platform.testframework.JUnitPlatformSpecification
import spock.lang.Unroll

@UnitTest
class BrimProductConsumerFilterUnitSpec extends JUnitPlatformSpecification {

    SpringProfileConfig springProfileConfig = Mock(SpringProfileConfig)

    @Unroll
    @Test
    def "dev should filter #productId should be #expected"() {
        given:
        ConsumerFilterConfig config = new ConsumerFilterConfig("^\\d{1,8}\$", "^A?A_\\d{1,10}_.+\$", "^SC_.*\$", true)
        BrimProductConsumerFilter devBrimProductConsumerFilter = new BrimProductConsumerFilter(springProfileConfig, config)

        when:
        springProfileConfig.environment >> Environment.DEV
        def actual = devBrimProductConsumerFilter.shouldFilterOut(productId)

        then:
        expected == actual

        where:
        productId            || expected
        null                 || false
        ""                   || false
        "A_12345678_FULL"    || false
        "A_91234567_FULL"    || false
        "A_01234567_FULL"    || false
        "A_012345_FULL"      || false
        // FOR AA store
        "AA_1234567890_FULL" || false
        "AA_91234567_FULL"   || false
        "AA_01234567_FULL"   || false
        "AA_123_full"        || false

        "A_12345678901"      || true
        "AB_12345678"        || true
        "BA_12345678"        || true
        "1234567"            || true
        "productId"          || true

    }

    @Unroll
    @Test
    def "demo should filter #productId should be #expected"() {
        given:
        ConsumerFilterConfig config = new ConsumerFilterConfig("^9\\d{8}\$", "^A_9\\d{8}_.+\$", "^SCD_.*\$", true)
        BrimProductConsumerFilter demoBrimProductConsumerFilter = new BrimProductConsumerFilter(springProfileConfig, config)

        when:
        springProfileConfig.environment >> Environment.DEMO
        def actual = demoBrimProductConsumerFilter.shouldFilterOut(productId)

        then:
        expected == actual

        where:
        productId          || expected
        null               || false
        ""                 || false
        "A_912345678_FULL" || false

        "A_012345_FULL"    || true
        "A_012345678"      || true
        "A_12345678"       || true
        "B_123456789"      || true
        "912345678A"       || true
        "91234567"         || true

    }

    @Unroll
    @Test
    def "live should filter #productId should be #expected"() {
        given:
        ConsumerFilterConfig config = new ConsumerFilterConfig("^0\\d{7}\$", "^(A|AA|AA2)_.+\$", "^SC_.*\$", true)
        springProfileConfig.environment >> Environment.LIVE
        BrimProductConsumerFilter demoBrimProductConsumerFilter = new BrimProductConsumerFilter(springProfileConfig, config)

        when:
        def actual = demoBrimProductConsumerFilter.shouldFilterOut(productId)

        then:
        expected == actual

        where:
        productId          || expected
        null               || false
        ""                 || false
        "A_912345678_FULL" || false
        "PRX_PC_15756M_US" || true
        "PRX_PC_15786M_FI" || true
        "A_12345678"       || false
        "AA_12345678"      || false
        "AA2_12345678"     || false
        "B_123456789"      || true
        "912345678A"       || true
        "91234567"         || true

    }


    @Test
    def "different env throws an exception"() {
        given:
        ConsumerFilterConfig config = new ConsumerFilterConfig("^\\d{1,8}\$", "^A_\\d{1,8}_.+\$", "^SC_.*\$", true)
        BrimProductConsumerFilter brimProductConsumerFilter = new BrimProductConsumerFilter(springProfileConfig, config)

        when:
        springProfileConfig.environment >> Environment.LOCAL
        brimProductConsumerFilter.shouldFilterOut("productId")

        then:
        thrown(IllegalStateException.class)
    }

}
