package com.sast.cis.payment.adyen.client.tokenization


import com.sast.adyengateway.dto.TokenizationResponseDto
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel
import com.sast.cis.payment.adyen.client.util.AdyenGatewayWiremockRule
import com.sast.cis.test.utils.SampleDataCreator
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.payment.model.PaymentTransactionModel
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import de.hybris.platform.servicelayer.model.ModelService
import generated.com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoBuilder
import generated.de.hybris.platform.payment.model.PaymentTransactionBuilder
import org.assertj.core.util.DateUtil
import org.junit.Rule

import javax.annotation.Resource

import static com.sast.cis.core.enums.PaymentProvider.ADYEN
import static com.sast.cis.core.util.Base58UUIDCodeGenerator.generateCode
import static com.sast.cis.test.utils.TestDataConstants.AA_AUSTRIA1_COMPANY_INTEGRATOR_UID
import static de.hybris.platform.payment.enums.PaymentTransactionType.AUTHORIZATION

@IntegrationTest
class AdyenGatewayTokenizationFacadeISpec extends ServicelayerTransactionalSpockSpecification {

    private final SampleDataCreator sampleDataCreator = new SampleDataCreator()

    @Rule
    public AdyenGatewayWiremockRule adyenGatewayWiremockRule = new AdyenGatewayWiremockRule()

    @Resource
    private ModelService modelService

    @Resource
    private TokenizationRequestDtoFactory tokenizationRequestDtoFactory

    @Resource
    private AdyenGatewayTokenizationFacade adyenGatewayTokenizationFacade

    private PaymentTransactionModel paymentTransaction
    private TokenizedSepaDirectDebitPaymentInfoModel tokenizedSepaDirectDebitPaymentInfo

    def setup() {
        def paymentInfoOwner = sampleDataCreator.getIntegratorByInternalUserId(AA_AUSTRIA1_COMPANY_INTEGRATOR_UID)

        tokenizedSepaDirectDebitPaymentInfo = TokenizedSepaDirectDebitPaymentInfoBuilder.generate()
                .withUser(paymentInfoOwner)
                .withPaymentProvider(ADYEN)
                .withCode("tokenized-sepa-dd-payment-info")
                .withPgwMerchantId("adyen-merchant-id")
                .withShopperReference("shopper-reference")
                .withIBAN("********************")
                .withAccountHolderName("Test User")
                .withMandateReference("sepa-mandate-reference")
                .withDateOfSignature(DateUtil.now())
                .buildIntegrationInstance()

        paymentTransaction = PaymentTransactionBuilder.generate()
                .withCode(generateCode("authTx"))
                .withType(AUTHORIZATION)
                .withInfo(tokenizedSepaDirectDebitPaymentInfo)
                .withPaymentProvider(ADYEN.toString())
                .buildIntegrationInstance()

        modelService.saveAll(tokenizedSepaDirectDebitPaymentInfo, paymentTransaction)
    }

    def "tokenize should call the Adyen Gateway with correct request and return parsed response"() {
        given:
        def expectedTokenizationResponse = TokenizationResponseDto.builder()
                .resultCode("success")
                .pspReference("psp-reference")
                .storedPaymentMethodId("stored-payment-method-id")
                .build()
        adyenGatewayWiremockRule.prepareTokenizationResponse(expectedTokenizationResponse)

        when:
        def tokenizationResult = adyenGatewayTokenizationFacade.tokenize(paymentTransaction)

        then:
        tokenizationResult != null
        tokenizationResult.resultCode() == expectedTokenizationResponse.resultCode()
        tokenizationResult.pspReference() == expectedTokenizationResponse.pspReference()
        tokenizationResult.storedPaymentMethodId() == expectedTokenizationResponse.storedPaymentMethodId()

        and:
        adyenGatewayWiremockRule.verifyTokenizationRequestPayload(
                tokenizationRequestDtoFactory.createForTransaction(paymentTransaction)
        )
    }
}
