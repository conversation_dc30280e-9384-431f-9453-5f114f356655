package com.sast.cis.payment.adyen.client.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.common.Slf4jNotifier;
import com.github.tomakehurst.wiremock.http.Request;
import com.github.tomakehurst.wiremock.http.Response;
import com.github.tomakehurst.wiremock.junit.WireMockRule;
import com.github.tomakehurst.wiremock.matching.StringValuePattern;
import com.sast.adyengateway.dto.TokenizationRequestDto;
import com.sast.adyengateway.dto.TokenizationResponseDto;
import de.hybris.platform.core.Registry;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options;
import static com.sast.cis.payment.adyen.client.authentication.config.AdyenGatewayConfigProvider.ADYEN_GATEWAY_API_URL;

@Slf4j
public class AdyenGatewayWiremockRule extends WireMockRule {
    private static final String AUTHORIZATION_HEADER = "Authorization";

    private static final String BASE_URL_PATH = "/rest";
    private static final String TOKENIZATION_ENDPOINT_PATH = BASE_URL_PATH + "/payment/tokenization/";
    private static final String PING_ENDPOINT_PATH = BASE_URL_PATH + "/ping/";

    private final ObjectMapper objectMapper;
    private final ConfigurationService configurationService;

    public AdyenGatewayWiremockRule() {
        super(options().dynamicPort().notifier(new Slf4jNotifier(true)), true);

        objectMapper = Registry.getApplicationContext().getBean("adyenGatewayClientObjectMapper", ObjectMapper.class);
        configurationService = Registry.getApplicationContext().getBean("configurationService", ConfigurationService.class);
    }

    @Override
    protected void before() {
        super.before();
        addMockServiceRequestListener(this::log);
        configurationService.getConfiguration().setProperty(ADYEN_GATEWAY_API_URL, getAdyenGatewayUrl().toString());
    }

    public void preparePingResponse(final int responseCode) {
        stubFor(get(urlPathEqualTo(PING_ENDPOINT_PATH))
            .withHeader(AUTHORIZATION_HEADER, authorizationHeaderPattern())
            .willReturn(aResponse().withStatus(responseCode)));
    }

    public void prepareTokenizationResponse(final TokenizationResponseDto responseDto) {
        stubFor(post(urlPathEqualTo(TOKENIZATION_ENDPOINT_PATH))
            .withHeader(AUTHORIZATION_HEADER, authorizationHeaderPattern())
            .willReturn(jsonResponse(responseDto, 200)));
    }

    public URI getAdyenGatewayUrl() {
        return UriComponentsBuilder.fromUriString("http://localhost")
            .port(port())
            .path(BASE_URL_PATH)
            .build().toUri();
    }

    @SneakyThrows
    public void verifyTokenizationRequestPayload(final TokenizationRequestDto expectedRequest) {
        final String expectedRequestJson = objectMapper.writeValueAsString(expectedRequest);
        verify(postRequestedFor(urlPathEqualTo(TOKENIZATION_ENDPOINT_PATH))
            .withRequestBody(equalToJson(expectedRequestJson)));
    }

    private StringValuePattern authorizationHeaderPattern() {
        return matching("Bearer\\s.+");
    }

    private void log(Request request, Response response) {
        LOG.info("Request: {}", request);
        LOG.info("RequestBody: {}", request.getBodyAsString());
        LOG.info("Response: {}", response);
        LOG.info("ResponseBody: {}", response.getBodyAsString());
    }
}
