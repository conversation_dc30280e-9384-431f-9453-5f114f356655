package com.sast.cis.payment.adyen.client.tokenization

import com.sast.cis.core.CisTimeService
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel
import de.hybris.bootstrap.annotations.UnitTest
import de.hybris.platform.core.model.order.payment.CreditCardPaymentInfoModel
import de.hybris.platform.payment.model.PaymentTransactionModel
import de.hybris.platform.testframework.JUnitPlatformSpecification
import org.assertj.core.util.DateUtil

import java.time.LocalDate

@UnitTest
class TokenizationRequestDtoFactoryUnitSpec extends JUnitPlatformSpecification {

    private CisTimeService cisTimeService = Spy()

    private TokenizationRequestDtoFactory tokenizationRequestDtoFactory

    private PaymentTransactionModel transaction = Mock()
    private TokenizedSepaDirectDebitPaymentInfoModel tokenizedSepaDirectDebitPaymentInfo = Mock()

    private final String transactionCode = "transaction-code"
    private final String merchantAccount = "merchant-account"
    private final String shopperReference = "shopper-reference"
    private final String iban = "**********************"
    private final String accountHolderName = "Account Holder Name"
    private final String mandateReference = "mandate-reference"
    private final String dateOfSignature = "2025-06-01T16:00:00Z"

    void setup() {
        tokenizationRequestDtoFactory = new TokenizationRequestDtoFactory(cisTimeService)

        transaction.getCode() >> transactionCode
        transaction.getInfo() >> tokenizedSepaDirectDebitPaymentInfo

        tokenizedSepaDirectDebitPaymentInfo.getPgwMerchantId() >> merchantAccount
        tokenizedSepaDirectDebitPaymentInfo.getShopperReference() >> shopperReference
        tokenizedSepaDirectDebitPaymentInfo.getIBAN() >> iban
        tokenizedSepaDirectDebitPaymentInfo.getAccountHolderName() >> accountHolderName
        tokenizedSepaDirectDebitPaymentInfo.getMandateReference() >> mandateReference
        tokenizedSepaDirectDebitPaymentInfo.getDateOfSignature() >> DateUtil.parseDatetime(dateOfSignature)
    }

    def "should create TokenizationRequestDto for valid transaction"() {
        when:
        def tokenizationRequestDto = tokenizationRequestDtoFactory.createForTransaction(transaction)

        then:
        tokenizationRequestDto.reference() == transactionCode
        tokenizationRequestDto.merchantAccount() == merchantAccount
        tokenizationRequestDto.shopperReference() == shopperReference
        tokenizationRequestDto.mandateData().mandateId() == mandateReference
        tokenizationRequestDto.mandateData().dateOfSignature() == LocalDate.parse("2025-06-01")
        tokenizationRequestDto.bankAccountDetails().ibanNumber() == iban
        tokenizationRequestDto.bankAccountDetails().ownerName() == accountHolderName
    }

    def "should throw IllegalStateException for invalid payment info type"() {
        when:
        tokenizationRequestDtoFactory.createForTransaction(transaction)

        then:
        transaction.getInfo() >> Mock(CreditCardPaymentInfoModel)

        and:
        def e = thrown(IllegalStateException)
        e.message == "Payment info in transaction transaction-code is not of expected type TokenizedSepaDirectDebitPaymentInfo"
    }

    def "should throw IllegalArgumentException when transaction has no payment info"() {
        when:
        tokenizationRequestDtoFactory.createForTransaction(transaction)

        then:
        transaction.getInfo() >> null

        and:
        def e = thrown(IllegalArgumentException)
        e.message == "Given Payment Transaction transaction-code has no payment info"
    }
}
