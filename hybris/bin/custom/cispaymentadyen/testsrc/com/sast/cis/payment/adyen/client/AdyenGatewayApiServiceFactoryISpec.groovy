package com.sast.cis.payment.adyen.client

import com.sast.adyengateway.dto.TokenizationRequestDto
import com.sast.adyengateway.dto.TokenizationResponseDto
import com.sast.cis.payment.adyen.client.util.AdyenGatewayWiremockRule
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification
import org.junit.Rule

import javax.annotation.Resource

import static org.assertj.core.api.Assertions.assertThat

@IntegrationTest
class AdyenGatewayApiServiceFactoryISpec extends ServicelayerTransactionalSpockSpecification {

    @Rule
    public AdyenGatewayWiremockRule adyenGatewayWiremockRule = new AdyenGatewayWiremockRule();

    @Resource
    private AdyenGatewayApiServiceFactory adyenGatewayApiServiceFactory

    def "should create a valid api client"() {
        given:
        adyenGatewayWiremockRule.preparePingResponse(200)

        when:
        adyenGatewayApiServiceFactory.getPingApi().ping()

        then:
        noExceptionThrown()
    }

    def "when tokenize then should return a valid response"() {
        given:
        def tokenizationRequest = buildTokenizationRequestDto()

        and:
        def expectedResponse = buildTokenizationResponseDto()
        adyenGatewayWiremockRule.prepareTokenizationResponse(expectedResponse)

        when:
        def actualResponse = adyenGatewayApiServiceFactory.getTokenizationApi().tokenize(tokenizationRequest)

        then:
        assertThat(actualResponse).usingRecursiveComparison().isEqualTo(expectedResponse)
    }

    private static buildTokenizationRequestDto() {
        return TokenizationRequestDto.builder()
                .reference("shopperReference")
                .build()
    }

    private static buildTokenizationResponseDto() {
        return TokenizationResponseDto.builder()
                .storedPaymentMethodId("storedPaymentMethodId")
                .build()
    }
}
