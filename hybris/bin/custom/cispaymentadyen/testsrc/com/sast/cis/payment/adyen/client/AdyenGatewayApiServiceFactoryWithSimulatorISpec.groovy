package com.sast.cis.payment.adyen.client

import com.sast.adyengateway.dto.TokenizationRequestDto
import de.hybris.bootstrap.annotations.IntegrationTest
import de.hybris.platform.servicelayer.ServicelayerTransactionalSpockSpecification

import javax.annotation.Resource
import java.time.LocalDate

@IntegrationTest
class AdyenGatewayApiServiceFactoryWithSimulatorISpec extends ServicelayerTransactionalSpockSpecification {

    @Resource
    private AdyenGatewayApiServiceFactory adyenGatewayApiServiceFactory

    private final String merchantReference = "test-merchant-reference"
    private final String shopperReference = "test-shopper-reference"

    // Test to ensure that the API client can integrate with the Adyen Gateway simulator
    def "when tokenize then return a valid response"() {
        given:
        def tokenizationRequest = buildTokenizationRequestDto()

        when:
        def tokenizationResponse = adyenGatewayApiServiceFactory.getTokenizationApi().tokenize(tokenizationRequest)

        then:
        tokenizationResponse
        tokenizationResponse.storedPaymentMethodId()
        tokenizationResponse.recurringDetailReference()
        tokenizationResponse.merchantReference() == merchantReference
        tokenizationResponse.shopperReference() == shopperReference
    }

    private buildTokenizationRequestDto() {
        return TokenizationRequestDto.builder()
                .merchantAccount(merchantReference)
                .shopperReference(shopperReference)
                .reference("tokenization-tx-reference")
                .bankAccountDetails(
                        TokenizationRequestDto.BankAccountDetails.builder()
                                .ownerName("Account Owner")
                                .ibanNumber("**********************")
                                .build()
                )
                .mandateData(
                        TokenizationRequestDto.MandateData.builder()
                                .mandateId("MANDATE12345")
                                .dateOfSignature(LocalDate.now())
                                .build()
                )
                .build();
    }
}
