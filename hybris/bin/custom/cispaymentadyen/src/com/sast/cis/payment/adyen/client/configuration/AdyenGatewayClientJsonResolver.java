package com.sast.cis.payment.adyen.client.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.stereotype.Component;

import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.ext.ContextResolver;
import javax.ws.rs.ext.Provider;

@Component
@Provider
@Produces({ MediaType.APPLICATION_JSON })
@Setter
@RequiredArgsConstructor
public class AdyenGatewayClientJsonResolver implements ContextResolver<ObjectMapper> {

    private final ObjectMapper adyenGatewayClientObjectMapper;

    @Override
    public ObjectMapper getContext(Class<?> type) {
        return adyenGatewayClientObjectMapper;
    }

}
