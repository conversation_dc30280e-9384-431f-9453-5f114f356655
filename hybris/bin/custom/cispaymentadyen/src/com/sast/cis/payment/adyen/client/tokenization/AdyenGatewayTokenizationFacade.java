package com.sast.cis.payment.adyen.client.tokenization;

import com.sast.adyengateway.dto.TokenizationRequestDto;
import com.sast.adyengateway.dto.TokenizationResponseDto;
import com.sast.cis.payment.adyen.client.AdyenGatewayApiServiceFactory;
import com.sast.cis.payment.adyen.client.proxyclientapi.TokenizationApi;
import de.hybris.platform.payment.model.PaymentTransactionModel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Facade for handling tokenization requests to the Adyen Gateway.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AdyenGatewayTokenizationFacade {
    private final AdyenGatewayApiServiceFactory adyenGatewayApiServiceFactory;
    private final TokenizationRequestDtoFactory tokenizationRequestDtoFactory;

    /**
     * Initiates the tokenization process for a given payment transaction.
     *
     * @param tx the payment transaction for which tokenization is requested
     * @return a TokenizationResult containing the stored payment method ID, PSP reference, and result code
     */
    public TokenizationResult tokenize(@NonNull final PaymentTransactionModel tx) {
        LOG.info("Initiate tokenization for transaction with code '{}'", tx.getCode());

        final TokenizationRequestDto tokenizationRequest = tokenizationRequestDtoFactory.createForTransaction(tx);
        final TokenizationApi tokenizationApi = adyenGatewayApiServiceFactory.getTokenizationApi();
        final TokenizationResponseDto tokenizationResponse = tokenizationApi.tokenize(tokenizationRequest);

        LOG.info("Tokenization response for transaction with code '{}' received: resultCode='{}', pspReference='{}'",
            tx.getCode(),
            tokenizationResponse.resultCode(),
            tokenizationResponse.pspReference()
        );

        return TokenizationResult.of(
            tokenizationResponse.storedPaymentMethodId(),
            tokenizationResponse.pspReference(),
            tokenizationResponse.resultCode()
        );
    }
}
