package com.sast.cis.payment.adyen.client.tokenization;

import com.sast.adyengateway.dto.TokenizationRequestDto;
import com.sast.cis.core.CisTimeService;
import com.sast.cis.core.model.TokenizedSepaDirectDebitPaymentInfoModel;
import de.hybris.platform.payment.model.PaymentTransactionModel;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Date;

import static com.google.common.base.Preconditions.checkArgument;

@Component
@RequiredArgsConstructor
public class TokenizationRequestDtoFactory {

    private final CisTimeService cisTimeService;

    TokenizationRequestDto createForTransaction(final PaymentTransactionModel tx) {
        checkArgument(tx.getInfo() != null,
            "Given Payment Transaction %s has no payment info", tx.getCode());

        final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo = validateAndCastPaymentInfo(tx);

        final String tokenizationTxInternalReference = tx.getCode();
        final String merchantAccount = paymentInfo.getPgwMerchantId();
        final String shopperReference = paymentInfo.getShopperReference();

        final TokenizationRequestDto.MandateData mandateData = buildMandateData(paymentInfo);
        final TokenizationRequestDto.BankAccountDetails bankAccountDetails = buildBankAccountDetails(paymentInfo);

        return TokenizationRequestDto.builder()
            .reference(tokenizationTxInternalReference)
            .merchantAccount(merchantAccount)
            .shopperReference(shopperReference)
            .mandateData(mandateData)
            .bankAccountDetails(bankAccountDetails)
            .build();
    }

    private TokenizationRequestDto.MandateData buildMandateData(final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo) {
        final String mandateReference = paymentInfo.getMandateReference();
        final LocalDate dateOfSignature = convertToLocalDate(paymentInfo.getDateOfSignature());

        return TokenizationRequestDto.MandateData.builder()
            .mandateId(mandateReference)
            .dateOfSignature(dateOfSignature)
            .build();
    }

    private TokenizationRequestDto.BankAccountDetails buildBankAccountDetails(final TokenizedSepaDirectDebitPaymentInfoModel paymentInfo) {
        final String iban = paymentInfo.getIBAN();
        final String accountHolderName = paymentInfo.getAccountHolderName();

        return TokenizationRequestDto.BankAccountDetails.builder()
            .ibanNumber(iban)
            .ownerName(accountHolderName)
            .build();
    }

    private TokenizedSepaDirectDebitPaymentInfoModel validateAndCastPaymentInfo(final PaymentTransactionModel paymentTransaction) {
        if (paymentTransaction.getInfo() instanceof TokenizedSepaDirectDebitPaymentInfoModel paymentInfo) {
            return paymentInfo;
        } else {
            throw new IllegalStateException(
                "Payment info in transaction %s is not of expected type TokenizedSepaDirectDebitPaymentInfo"
                    .formatted(paymentTransaction.getCode())
            );
        }
    }

    private LocalDate convertToLocalDate(final Date dateToConvert) {
        return cisTimeService.convertToLocalDate(dateToConvert);
    }
}
