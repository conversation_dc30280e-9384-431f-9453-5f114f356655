package com.sast.cis.payment.sepa.client.api;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.Map;

/**
 * JAX-RS API interface for SEPA mandate operations.
 * This interface defines the REST endpoints for communicating with the sepa-mandate service.
 * Follows the same pattern as Adyen API interfaces.
 */
@Path("/api/v1/mandates")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public interface SepaMandateApi {

    /**
     * Initialize a draft SEPA mandate
     * @param companyId - The company ID for the mandate
     * @return Response with SepaMandateDto containing auto-generated mandate reference
     */
    @POST
    @Path("/initialize")
    Response initializeSepaMandateDraft(@QueryParam("companyId") String companyId);

    /**
     * Update or finalize a SEPA mandate by reference
     * @param reference - The mandate reference
     * @param payload - The mandate data (can be partial for updates or complete for finalization)
     * @return Response with updated/finalized SepaMandateDto
     */
    @POST
    @Path("/{reference}/activate")
    Response updateOrFinalizeSepaMandateByReference(@PathParam("reference") String reference, Map<String, Object> payload);
}
