package com.sast.cis.payment.adyen.client.authentication.config;

import com.sast.cis.payment.adyen.client.authentication.config.AdyenGatewayConfig.AuthenticationConfig;
import de.hybris.platform.servicelayer.config.ConfigurationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import static org.apache.commons.lang3.StringUtils.EMPTY;

@Slf4j
@RequiredArgsConstructor
@Component
public class AdyenGatewayConfigProvider {

    public static final String ADYEN_GATEWAY_API_URL = "adyen.gateway.api.url";
    public static final String ADYEN_GATEWAY_AUTH_IDP_URL = "adyen.gateway.auth.idp.url";
    public static final String ADYEN_GATEWAY_AUTH_TOKEN_ENDPOINT_URL = "adyen.gateway.auth.token.endpoint.url";
    public static final String ADYEN_GATEWAY_AUTH_CLIENT_ID = "adyen.gateway.auth.client.id";
    public static final String ADYEN_GATEWAY_AUTH_CLIENT_SECRET = "adyen.gateway.auth.client.secret";
    public static final String ADYEN_GATEWAY_AUTH_CLIENT_REGISTRATION_ID = "adyen.gateway.auth.client.registration.id";

    private static final String DEFAULT_CLIENT_REGISTRATION_ID = "baam-adyen-client";

    private final ConfigurationService configurationService;

    public AdyenGatewayConfig getConfig() {
        final String apiUrl = getProperty(ADYEN_GATEWAY_API_URL);

        final String tokenEndpointUrl = getProperty(ADYEN_GATEWAY_AUTH_TOKEN_ENDPOINT_URL);
        final String clientId = getProperty(ADYEN_GATEWAY_AUTH_CLIENT_ID);
        final String clientSecret = getProperty(ADYEN_GATEWAY_AUTH_CLIENT_SECRET);
        final String clientRegistrationId = getProperty(
            ADYEN_GATEWAY_AUTH_CLIENT_REGISTRATION_ID, DEFAULT_CLIENT_REGISTRATION_ID
        );

        return new AdyenGatewayConfig(
            apiUrl,
            new AuthenticationConfig(tokenEndpointUrl, clientId, clientSecret, clientRegistrationId)
        );
    }

    private String getProperty(final String propertyKey) {
        return getProperty(propertyKey, EMPTY);
    }

    private String getProperty(final String propertyKey, final String defaultValue) {
        final String propertyValue = configurationService.getConfiguration().getString(propertyKey, defaultValue);
        if (StringUtils.isBlank(propertyValue)) {
            LOG.error("ALERT: Property with key '{}' not found", propertyKey);
        }
        return propertyValue;
    }
}
