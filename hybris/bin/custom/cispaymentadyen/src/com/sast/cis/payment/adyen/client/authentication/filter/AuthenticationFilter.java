package com.sast.cis.payment.adyen.client.authentication.filter;

import com.sast.cis.payment.adyen.client.authentication.AccessTokenProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.ws.rs.client.ClientRequestContext;
import javax.ws.rs.client.ClientRequestFilter;

@RequiredArgsConstructor
@Component
public class AuthenticationFilter implements ClientRequestFilter {

    private final AccessTokenProvider accessTokenProvider;

    @Override
    public void filter(final ClientRequestContext requestContext) {
        final String token = accessTokenProvider.getAccessToken();
        requestContext.getHeaders().add("Authorization", "Bearer " + token);
    }
}
