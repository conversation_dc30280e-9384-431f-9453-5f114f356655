package com.sast.cis.payment.adyen.client;

import com.sast.cis.payment.adyen.client.authentication.config.AdyenGatewayConfigProvider;
import com.sast.cis.payment.adyen.client.authentication.filter.AuthenticationFilter;
import com.sast.cis.payment.adyen.client.configuration.AdyenGatewayClientJsonResolver;
import com.sast.cis.payment.adyen.client.proxyclientapi.PingApi;
import com.sast.cis.payment.adyen.client.proxyclientapi.TokenizationApi;
import lombok.RequiredArgsConstructor;
import org.glassfish.jersey.client.proxy.WebResourceFactory;
import org.glassfish.jersey.logging.LoggingFeature;
import org.springframework.stereotype.Service;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.WebTarget;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
@RequiredArgsConstructor
public class AdyenGatewayApiServiceFactory {

    private final AuthenticationFilter authenticationFilter;
    private final AdyenGatewayConfigProvider adyenGatewayConfigProvider;
    private final AdyenGatewayClientJsonResolver adyenGatewayClientJsonResolver;

    public TokenizationApi getTokenizationApi() {
        return WebResourceFactory.newResource(TokenizationApi.class, getPgwGatewayTarget());
    }

    public PingApi getPingApi() {
        return WebResourceFactory.newResource(PingApi.class, getPgwGatewayTarget());
    }

    private WebTarget getPgwGatewayTarget() {
        return getAdyenGatewayClient().target(adyenGatewayConfigProvider.getConfig().apiUrl());
    }

    private Client getAdyenGatewayClient() {
        return ClientBuilder.newClient()
            .register(getLoggingFeature())
            .register(authenticationFilter)
            .register(adyenGatewayClientJsonResolver);
    }

    private LoggingFeature getLoggingFeature() {
        return new LoggingFeature(
            Logger.getLogger(LoggingFeature.DEFAULT_LOGGER_NAME),
            Level.INFO, LoggingFeature.DEFAULT_VERBOSITY, 10000
        );
    }
}
