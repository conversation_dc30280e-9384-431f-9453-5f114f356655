package com.sast.cis.payment.sepa.client;

import com.sast.cis.payment.adyen.client.authentication.filter.AuthenticationFilter;
import com.sast.cis.payment.sepa.client.api.SepaMandateApi;
import com.sast.cis.payment.sepa.client.configuration.SepaMandateClientJsonResolver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.glassfish.jersey.client.proxy.WebResourceFactory;
import org.glassfish.jersey.logging.LoggingFeature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.client.WebTarget;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Factory for creating SEPA mandate API service clients.
 * Follows the exact same pattern as AdyenGatewayApiServiceFactory.
 * Reuses the existing AuthenticationFilter for JWT authentication.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SepaMandateGatewayApiServiceFactory {

    private final AuthenticationFilter authenticationFilter;
    private final SepaMandateClientJsonResolver sepaMandateClientJsonResolver;

    @Value("${sepa.mandate.service.url:http://localhost:8081}")
    private String sepaMandateServiceUrl;

    /**
     * Get the SEPA mandate API client
     * @return SepaMandateApi client instance
     */
    public SepaMandateApi getSepaMandateApi() {
        return WebResourceFactory.newResource(SepaMandateApi.class, getSepaMandateGatewayTarget());
    }

    /**
     * Create the WebTarget for SEPA mandate service
     * @return Configured WebTarget
     */
    private WebTarget getSepaMandateGatewayTarget() {
        return getSepaMandateGatewayClient().target(sepaMandateServiceUrl);
    }

    /**
     * Create the JAX-RS Client for SEPA mandate service.
     * This follows the exact same pattern as AdyenGatewayApiServiceFactory.getAdyenGatewayClient()
     * @return Configured JAX-RS Client
     */
    private Client getSepaMandateGatewayClient() {
        return ClientBuilder.newClient()
            .register(getLoggingFeature())
            .register(authenticationFilter)  // Reuse the existing AuthenticationFilter!
            .register(sepaMandateClientJsonResolver);
    }

    /**
     * Create logging feature for the client
     * @return Configured LoggingFeature
     */
    private LoggingFeature getLoggingFeature() {
        return new LoggingFeature(
            Logger.getLogger(LoggingFeature.DEFAULT_LOGGER_NAME),
            Level.INFO, LoggingFeature.DEFAULT_VERBOSITY, 10000
        );
    }
}
