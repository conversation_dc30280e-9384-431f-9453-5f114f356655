package com.sast.cis.payment.adyen.client.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class JacksonConfig {

    @Bean
    public ObjectMapper adyenGatewayClientObjectMapper() {
        return new ObjectMapper()
            .registerModule(new JavaTimeModule());
    }
}
