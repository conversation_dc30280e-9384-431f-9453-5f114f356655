# -----------------------------------------------------------------------
# Copyright (c) 2021 SAP SE or an SAP affiliate company. All rights reserved.
# -----------------------------------------------------------------------

# cispaymentadyen.key=value
adyen.gateway.api.url=http://localhost:8091/adyen
adyen.gateway.auth.idp.url=http://localhost:8080/auth/realms/baam
adyen.gateway.auth.token.endpoint.url=${adyen.gateway.auth.idp.url}/protocol/openid-connect/token
adyen.gateway.auth.client.id=iotstore-adyen-gateway
adyen.gateway.auth.client.secret=B-E3SJeIUb8ee*jwKLdp7S10

# Specifies the location of the spring context file putted automatically to the global platform application context.
cispaymentadyen.application-context=cispaymentadyen-spring.xml
# SEPA Mandate Service Configuration
sepa.mandate.service.url=http://localhost:8081
